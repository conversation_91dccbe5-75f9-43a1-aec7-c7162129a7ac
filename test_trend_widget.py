#!/usr/bin/env python
"""
趋势榜单组件测试脚本 - 用于测试趋势榜单组件的显示功能
"""

import sys
import logging
from PyQt5.QtWidgets import QApplication, QMainWindow
from ui.trend_widget import TrendWidget
from api_service import APIService

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('trend_widget_test')

class TestWindow(QMainWindow):
    """测试窗口类"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("趋势榜单测试")
        self.setGeometry(100, 100, 800, 600)
        
        # 创建趋势榜单组件
        self.trend_widget = TrendWidget(self)
        self.setCentralWidget(self.trend_widget)
        
        # 连接信号
        self.trend_widget.token_selected.connect(self.on_token_selected)
        
        # 直接测试获取数据
        self.test_get_trending_tokens()
    
    def on_token_selected(self, token):
        """处理代币选择事件"""
        logger.info(f"选择了代币: {token['name']} ({token['symbol']})")
    
    def test_get_trending_tokens(self):
        """直接测试获取趋势榜单数据"""
        logger.info("开始直接测试获取趋势榜单数据...")
        
        # 直接获取数据
        tokens = APIService.get_trending_tokens()
        logger.info(f"直接获取到 {len(tokens) if tokens else 0} 个趋势代币")
        
        if tokens:
            # 打印第一个代币的信息
            logger.info(f"第一个代币: {tokens[0]['name']} ({tokens[0]['symbol']})")
            
            # 直接更新UI
            self.trend_widget.tokens_data = tokens
            self.trend_widget.update_table()
            self.trend_widget.status_label.setText(f"已加载 {len(tokens)} 个代币")
        else:
            logger.error("直接获取趋势榜单数据失败")
            self.trend_widget.status_label.setText("获取数据失败，请稍后重试")

def main():
    """主函数"""
    app = QApplication(sys.argv)
    window = TestWindow()
    window.show()
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
