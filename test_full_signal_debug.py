#!/usr/bin/env python3
"""
完整的策略信号调试测试
验证整个信号流程：数据下载 -> 策略分析 -> 信号发射
"""

import sys
import logging
import time
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

from api_service import APIService
from multi_thread_ohlcv_manager import MultiThreadOHLCVManager

def test_full_signal_debug():
    """完整的策略信号调试测试"""
    
    # 配置详细日志
    logging.basicConfig(
        level=logging.DEBUG, 
        format='%(asctime)s - %(levelname)s - %(name)s - %(message)s'
    )
    
    logger = logging.getLogger(__name__)
    logger.info("=== 完整策略信号调试测试 ===")
    
    app = QApplication(sys.argv)
    
    # 统计信息
    stats = {
        'downloads_completed': 0,
        'downloads_failed': 0,
        'signals_received': 0,
        'analysis_completed': 0
    }
    
    def on_download_completed(token_address, symbol, ohlcv_data, token_data):
        """下载完成回调"""
        stats['downloads_completed'] += 1
        logger.info(f"📥 下载完成: {symbol} - {len(ohlcv_data)} 条数据")
    
    def on_download_failed(token_address, symbol, error_msg):
        """下载失败回调"""
        stats['downloads_failed'] += 1
        logger.warning(f"❌ 下载失败: {symbol} - {error_msg}")
    
    def on_strategy_signal(token_address, symbol, signal_type, price, timestamp, strategy_name):
        """策略信号回调"""
        stats['signals_received'] += 1
        logger.info(f"🎯 策略信号: {symbol} -> {signal_type} @ ${price:.6f} (策略: {strategy_name})")
    
    def on_all_downloads_completed(success_count, total_count):
        """所有下载完成回调"""
        logger.info(f"📊 所有下载任务完成: 成功 {success_count}/{total_count}")
    
    try:
        # 创建API服务和管理器
        api_service = APIService()
        manager = MultiThreadOHLCVManager(api_service, max_workers=2)
        
        # 连接所有信号
        manager.download_completed.connect(on_download_completed)
        manager.download_failed.connect(on_download_failed)
        manager.strategy_signal_generated.connect(on_strategy_signal)
        manager.all_downloads_completed.connect(on_all_downloads_completed)
        
        # 测试单个代币（使用SOL）
        test_token = {
            'tokenAddress': 'So11111111111111111111111111111111111111112',  # WSOL
            'symbol': 'SOL',
            'name': 'Wrapped SOL',
            'source': 'trend',
            'strategy_name': 'VWAP 交叉策略'
        }
        
        logger.info(f"🚀 提交测试代币: {test_token['symbol']}")
        
        # 提交测试任务
        success = manager.submit_download_task(
            token_data=test_token,
            strategy_name='VWAP 交叉策略',
            timeframe='1m',
            days=1
        )
        
        if success:
            logger.info("✅ 任务提交成功")
        else:
            logger.error("❌ 任务提交失败")
            return
        
        # 定时检查状态
        def check_status():
            manager_stats = manager.get_statistics()
            logger.info(f"📈 状态检查:")
            logger.info(f"  - 管理器统计: {manager_stats}")
            logger.info(f"  - 测试统计: {stats}")
            
            # 如果有信号，测试成功
            if stats['signals_received'] > 0:
                logger.info("🎉 SUCCESS: 策略信号修复成功！")
                QTimer.singleShot(2000, app.quit)
            
            # 如果下载完成但没有信号，可能是策略分析问题
            elif stats['downloads_completed'] > 0 and stats['signals_received'] == 0:
                logger.warning("⚠️  下载完成但没有策略信号，检查策略分析...")
                
            # 如果任务全部完成
            total_tasks = manager_stats['total_completed'] + manager_stats['total_failed']
            if total_tasks >= manager_stats['total_submitted'] and manager_stats['total_submitted'] > 0:
                if stats['signals_received'] == 0:
                    logger.error("❌ FAILED: 所有任务完成但没有接收到策略信号")
                QTimer.singleShot(3000, app.quit)
        
        # 每3秒检查一次状态
        status_timer = QTimer()
        status_timer.timeout.connect(check_status)
        status_timer.start(3000)
        
        # 60秒后强制退出
        QTimer.singleShot(60000, lambda: [
            logger.info("⏰ 测试超时"),
            app.quit()
        ])
        
        logger.info("⏳ 开始测试，监控策略信号...")
        logger.info("预期流程:")
        logger.info("1. 下载SOL的OHLCV数据")
        logger.info("2. 执行策略分析")
        logger.info("3. 发射策略信号")
        logger.info("4. 在日志中看到策略信号")
        
        # 运行应用
        sys.exit(app.exec_())
        
    except Exception as e:
        logger.error(f"测试失败: {e}", exc_info=True)

if __name__ == '__main__':
    test_full_signal_debug() 