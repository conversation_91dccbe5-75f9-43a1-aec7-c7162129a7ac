#!/usr/bin/env python3
"""
打包测试脚本 - 验证打包后的应用程序是否正常工作
"""

import os
import sys
import subprocess
import platform
import time
from pathlib import Path

def find_executable():
    """查找打包后的可执行文件"""
    dist_dir = Path("dist")
    
    if not dist_dir.exists():
        return None
    
    system = platform.system()
    
    if system == "Windows":
        exe_path = dist_dir / "TrendTrader.exe"
    elif system == "Darwin":
        exe_path = dist_dir / "TrendTrader.app" / "Contents" / "MacOS" / "TrendTrader"
    else:  # Linux
        exe_path = dist_dir / "TrendTrader"
    
    return exe_path if exe_path.exists() else None

def test_executable_exists():
    """测试可执行文件是否存在"""
    print("🔍 检查可执行文件...")
    
    exe_path = find_executable()
    
    if exe_path:
        print(f"✅ 找到可执行文件: {exe_path}")
        print(f"📊 文件大小: {exe_path.stat().st_size / (1024*1024):.1f} MB")
        return True
    else:
        print("❌ 未找到可执行文件")
        print("请先运行打包脚本: python quick_build.py")
        return False

def test_executable_permissions():
    """测试可执行文件权限"""
    print("🔐 检查文件权限...")
    
    exe_path = find_executable()
    
    if not exe_path:
        return False
    
    if os.access(exe_path, os.X_OK):
        print("✅ 文件具有执行权限")
        return True
    else:
        print("❌ 文件缺少执行权限")
        if platform.system() != "Windows":
            print("尝试添加执行权限...")
            os.chmod(exe_path, 0o755)
            if os.access(exe_path, os.X_OK):
                print("✅ 执行权限添加成功")
                return True
        return False

def test_dependencies():
    """测试依赖文件"""
    print("📦 检查依赖文件...")
    
    required_dirs = ["ui", "strategies"]
    missing_dirs = []
    
    for dir_name in required_dirs:
        if not os.path.exists(dir_name):
            missing_dirs.append(dir_name)
    
    if missing_dirs:
        print(f"⚠️  缺少目录: {', '.join(missing_dirs)}")
        print("这可能影响打包后的应用程序运行")
        return False
    else:
        print("✅ 所有必要目录都存在")
        return True

def test_quick_launch():
    """快速启动测试（非阻塞）"""
    print("🚀 测试应用程序启动...")
    
    exe_path = find_executable()
    
    if not exe_path:
        return False
    
    try:
        # 启动应用程序（非阻塞模式）
        if platform.system() == "Darwin":
            # macOS 使用 open 命令
            process = subprocess.Popen(["open", str(exe_path.parent.parent)])
        else:
            # Windows 和 Linux 直接运行
            process = subprocess.Popen([str(exe_path)])
        
        print("✅ 应用程序启动成功")
        print(f"📋 进程 ID: {process.pid}")
        
        # 等待几秒钟检查进程是否还在运行
        time.sleep(3)
        
        if process.poll() is None:
            print("✅ 应用程序正在运行")
            
            # 尝试优雅地终止进程
            try:
                process.terminate()
                process.wait(timeout=5)
                print("✅ 应用程序已正常关闭")
            except subprocess.TimeoutExpired:
                process.kill()
                print("⚠️  强制关闭应用程序")
            
            return True
        else:
            print(f"❌ 应用程序启动后立即退出，退出码: {process.returncode}")
            return False
            
    except Exception as e:
        print(f"❌ 启动测试失败: {e}")
        return False

def test_file_structure():
    """测试打包后的文件结构"""
    print("📁 检查文件结构...")
    
    dist_dir = Path("dist")
    
    if not dist_dir.exists():
        print("❌ dist 目录不存在")
        return False
    
    print(f"📂 dist 目录内容:")
    for item in dist_dir.iterdir():
        if item.is_file():
            size = item.stat().st_size / (1024*1024)
            print(f"  📄 {item.name} ({size:.1f} MB)")
        else:
            print(f"  📁 {item.name}/")
    
    return True

def generate_test_report():
    """生成测试报告"""
    print("\n" + "="*50)
    print("📋 打包测试报告")
    print("="*50)
    
    tests = [
        ("可执行文件存在", test_executable_exists),
        ("文件权限检查", test_executable_permissions),
        ("依赖文件检查", test_dependencies),
        ("文件结构检查", test_file_structure),
        ("启动测试", test_quick_launch),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 {test_name}:")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    print("\n" + "="*50)
    print("📊 测试结果汇总")
    print("="*50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！应用程序打包成功")
        print("\n📋 下一步:")
        print("1. 将 dist/ 目录中的文件复制到目标电脑")
        print("2. 在目标电脑上测试运行")
        print("3. 如有问题，查看打包部署指南")
    else:
        print("⚠️  部分测试失败，请检查打包配置")
        print("\n🔧 建议:")
        print("1. 检查 requirements.txt 中的依赖")
        print("2. 确认所有必要文件都已包含")
        print("3. 重新运行打包脚本")
    
    return passed == total

def main():
    """主函数"""
    print("🧪 TrendTrader 打包测试工具")
    print("="*50)
    
    # 检查是否在正确的目录
    if not os.path.exists("main.py"):
        print("❌ 请在项目根目录运行此脚本")
        return 1
    
    # 运行测试
    success = generate_test_report()
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main()) 