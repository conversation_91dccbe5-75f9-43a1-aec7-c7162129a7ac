#!/usr/bin/env python
"""
趋势榜单API测试脚本 - 用于测试趋势榜单API的响应格式
"""

import requests
import json
import logging
from config import TREND_API_URL

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('trend_api_test')

def test_trend_api():
    """测试趋势榜单API"""
    print(f"开始测试趋势榜单API: {TREND_API_URL}")
    
    try:
        # 添加请求头部
        headers = {
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36',
            'Accept': 'application/json'
        }
        
        # 发送请求
        print("发送请求...")
        response = requests.get(TREND_API_URL, headers=headers, timeout=15)
        
        # 记录响应状态码
        print(f"响应状态码: {response.status_code}")
        
        # 检查响应状态
        response.raise_for_status()
        
        # 打印原始响应内容
        print(f"原始响应内容: {response.text[:500]}...")
        
        # 尝试解析JSON
        try:
            data = response.json()
            print(f"响应数据类型: {type(data)}")
            
            # 打印响应数据的结构
            if isinstance(data, dict):
                print(f"响应数据是字典，包含以下键: {list(data.keys())}")
                if 'status' in data:
                    print(f"状态: {data['status']}")
                if 'data' in data:
                    if isinstance(data['data'], list):
                        print(f"数据是列表，包含 {len(data['data'])} 个元素")
                        if data['data']:
                            print(f"第一个元素包含以下键: {list(data['data'][0].keys()) if isinstance(data['data'][0], dict) else '不是字典'}")
                            print(f"第一个元素: {json.dumps(data['data'][0], indent=2)}")
                    else:
                        print(f"数据不是列表，而是 {type(data['data'])}")
            elif isinstance(data, list):
                print(f"响应数据是列表，包含 {len(data)} 个元素")
                if data:
                    print(f"第一个元素包含以下键: {list(data[0].keys()) if isinstance(data[0], dict) else '不是字典'}")
                    print(f"第一个元素: {json.dumps(data[0], indent=2)}")
            else:
                print(f"响应数据是其他类型: {type(data)}")
                print(f"响应数据: {data}")
        except json.JSONDecodeError as e:
            print(f"解析JSON失败: {str(e)}")
            print(f"原始响应内容: {response.text}")
            
        return response.text
    except Exception as e:
        print(f"测试趋势榜单API失败: {str(e)}")
        return None

if __name__ == "__main__":
    test_trend_api()
