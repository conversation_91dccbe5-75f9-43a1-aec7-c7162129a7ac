#!/usr/bin/env python3
"""
图表数据加载诊断脚本
用于调试K线图表的数据获取和显示问题
"""

import sys
import os
import logging
import traceback
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QLabel, QTextEdit
from PyQt5.QtCore import QTimer, pyqtSlot

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from api_service import APIService
from ui.chart_widget import ChartWidget

# 配置详细日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('debug_chart.log', mode='w')
    ]
)

logger = logging.getLogger(__name__)

class ChartDebugWindow(QMainWindow):
    """图表调试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("图表数据加载诊断")
        self.resize(1200, 800)
        
        # 创建APIService
        logger.info("正在创建APIService实例...")
        try:
            self.api_service = APIService(self)
            logger.info("APIService创建成功")
        except Exception as e:
            logger.error(f"APIService创建失败: {e}")
            logger.error(traceback.format_exc())
            
        # 创建主界面
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 添加状态标签
        self.status_label = QLabel("等待测试...")
        layout.addWidget(self.status_label)
        
        # 添加日志输出区域
        self.log_output = QTextEdit()
        self.log_output.setMaximumHeight(200)
        layout.addWidget(self.log_output)
        
        # 添加测试按钮
        self.test_trend_button = QPushButton("测试趋势代币数据获取")
        self.test_trend_button.clicked.connect(self.test_trend_data)
        layout.addWidget(self.test_trend_button)
        
        self.test_historical_button = QPushButton("测试历史代币数据获取")
        self.test_historical_button.clicked.connect(self.test_historical_data)
        layout.addWidget(self.test_historical_button)
        
        self.test_chart_button = QPushButton("测试ChartWidget创建")
        self.test_chart_button.clicked.connect(self.test_chart_widget)
        layout.addWidget(self.test_chart_button)
        
        # 连接APIService信号
        if hasattr(self, 'api_service'):
            self.api_service.trending_tokens_ready.connect(self.on_trend_data_received)
            self.api_service.trending_tokens_error.connect(self.on_trend_error)
            self.api_service.ohlcv_data_ready.connect(self.on_ohlcv_data_received)
            self.api_service.ohlcv_data_error.connect(self.on_ohlcv_error)
            logger.info("APIService信号连接完成")
    
    def log_message(self, message):
        """添加日志消息到输出区域"""
        self.log_output.append(f"[{self.get_timestamp()}] {message}")
        logger.info(message)
    
    def get_timestamp(self):
        """获取当前时间戳"""
        from datetime import datetime
        return datetime.now().strftime("%H:%M:%S")
    
    def test_trend_data(self):
        """测试趋势数据获取"""
        self.log_message("开始测试趋势数据获取...")
        self.status_label.setText("正在获取趋势数据...")
        
        try:
            if hasattr(self, 'api_service'):
                self.api_service.get_trending_tokens_async()
                self.log_message("已发送异步趋势数据请求")
            else:
                self.log_message("ERROR: APIService未初始化")
        except Exception as e:
            self.log_message(f"ERROR: 趋势数据请求失败: {e}")
            self.log_message(traceback.format_exc())
    
    def test_historical_data(self):
        """测试历史数据获取"""
        self.log_message("开始测试历史数据获取...")
        self.status_label.setText("正在获取历史数据...")
        
        try:
            if hasattr(self, 'api_service'):
                self.api_service.get_historical_tokens_async()
                self.log_message("已发送异步历史数据请求")
            else:
                self.log_message("ERROR: APIService未初始化")
        except Exception as e:
            self.log_message(f"ERROR: 历史数据请求失败: {e}")
            self.log_message(traceback.format_exc())
    
    def test_chart_widget(self):
        """测试ChartWidget创建和数据获取"""
        self.log_message("开始测试ChartWidget...")
        self.status_label.setText("正在测试ChartWidget...")
        
        try:
            # 创建测试token数据（使用已知存在的代币）
            test_token_trend = {
                'tokenAddress': 'So11111111111111111111111111111111111111112',  # WSOL
                'symbol': 'SOL',
                'name': 'Wrapped SOL',
                'source': 'trend'
            }
            
            test_token_historical = {
                'tokenAddress': 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',  # USDC
                'symbol': 'USDC',
                'name': 'USD Coin',
                'source': 'historical'
            }
            
            self.log_message("创建ChartWidget实例...")
            
            if hasattr(self, 'api_service'):
                # 测试趋势代币
                self.chart_widget_trend = ChartWidget(api_service=self.api_service)
                self.chart_widget_trend.setWindowTitle("测试图表 - 趋势代币")
                self.chart_widget_trend.resize(800, 600)
                
                self.log_message("设置趋势代币数据...")
                self.chart_widget_trend.set_token(test_token_trend)
                self.chart_widget_trend.show()
                
                # 延迟测试历史代币
                QTimer.singleShot(5000, lambda: self.test_historical_chart(test_token_historical))
                
                self.log_message("ChartWidget创建完成，开始数据获取...")
            else:
                self.log_message("ERROR: APIService未初始化，无法创建ChartWidget")
                
        except Exception as e:
            self.log_message(f"ERROR: ChartWidget测试失败: {e}")
            self.log_message(traceback.format_exc())
    
    def test_historical_chart(self, token_data):
        """测试历史数据图表"""
        try:
            self.log_message("创建历史数据ChartWidget...")
            self.chart_widget_historical = ChartWidget(api_service=self.api_service)
            self.chart_widget_historical.setWindowTitle("测试图表 - 历史代币")
            self.chart_widget_historical.resize(800, 600)
            self.chart_widget_historical.move(100, 100)  # 错开位置
            
            self.log_message("设置历史代币数据...")
            self.chart_widget_historical.set_token(token_data)
            self.chart_widget_historical.show()
            
        except Exception as e:
            self.log_message(f"ERROR: 历史数据ChartWidget测试失败: {e}")
            self.log_message(traceback.format_exc())
    
    @pyqtSlot(list)
    def on_trend_data_received(self, data):
        """处理趋势数据接收"""
        self.log_message(f"SUCCESS: 收到趋势数据，共 {len(data)} 条记录")
        if data:
            self.log_message(f"第一条数据示例: {data[0]}")
        self.status_label.setText("趋势数据获取成功")
    
    @pyqtSlot(str)
    def on_trend_error(self, error):
        """处理趋势数据错误"""
        self.log_message(f"ERROR: 趋势数据获取失败: {error}")
        self.status_label.setText("趋势数据获取失败")
    
    @pyqtSlot(str, str, list)
    def on_ohlcv_data_received(self, token_address, timeframe, data):
        """处理OHLCV数据接收"""
        self.log_message(f"SUCCESS: 收到OHLCV数据 {token_address} [{timeframe}]，共 {len(data)} 条记录")
        if data:
            self.log_message(f"第一条OHLCV数据: {data[0]}")
            self.log_message(f"最后一条OHLCV数据: {data[-1]}")
        self.status_label.setText(f"OHLCV数据获取成功 ({len(data)} 条)")
    
    @pyqtSlot(str)
    def on_ohlcv_error(self, error):
        """处理OHLCV数据错误"""
        self.log_message(f"ERROR: OHLCV数据获取失败: {error}")
        self.status_label.setText("OHLCV数据获取失败")

if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    try:
        window = ChartDebugWindow()
        window.show()
        
        logger.info("诊断窗口已启动，准备开始测试...")
        
        sys.exit(app.exec_())
        
    except Exception as e:
        logger.error(f"应用启动失败: {e}")
        logger.error(traceback.format_exc())
        sys.exit(1) 