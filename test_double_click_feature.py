#!/usr/bin/env python3
"""
测试双击代币打开K线图功能
"""

import sys
import logging
from typing import Dict, List

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_double_click_functionality():
    """测试双击功能的完整性"""
    
    print("🔥 测试双击代币打开K线图功能")
    print("=" * 50)
    
    try:
        # 测试导入
        print("1. 测试模块导入...")
        from trending_window import TrendingWindow
        from ui.chart_widget import ChartWidget
        from ui.headless_chart_widget import HeadlessChartWidget
        from multi_thread_ohlcv_manager import MultiThreadOHLCVManager
        from api_service import APIService
        print("   ✅ 所有模块导入成功")
        
        # 测试关键方法存在性
        print("\n2. 检查TrendingWindow关键方法...")
        key_methods = [
            'on_token_double_clicked',
            'start_single_token_analysis',
            'get_cached_ohlcv_data', 
            'open_chart_with_cached_data',
            'on_single_download_completed'
        ]
        
        for method in key_methods:
            if hasattr(TrendingWindow, method):
                print(f"   ✅ {method}")
            else:
                print(f"   ❌ {method} - 缺失!")
                return False
        
        # 测试HeadlessChartWidget方法
        print("\n3. 检查HeadlessChartWidget方法...")
        headless_methods = ['get_current_ohlcv_data', 'get_current_dataframe']
        for method in headless_methods:
            if hasattr(HeadlessChartWidget, method):
                print(f"   ✅ {method}")
            else:
                print(f"   ❌ {method} - 缺失!")
                return False
        
        # 测试MultiThreadOHLCVManager方法
        print("\n4. 检查MultiThreadOHLCVManager方法...")
        if hasattr(MultiThreadOHLCVManager, 'get_headless_widget'):
            print("   ✅ get_headless_widget")
        else:
            print("   ❌ get_headless_widget - 缺失!")
            return False
        
        # 测试功能流程逻辑
        print("\n5. 测试功能流程逻辑...")
        
        # 模拟代币数据
        mock_token_data = {
            'tokenAddress': '0x1234567890abcdef',
            'symbol': 'TEST',
            'name': 'Test Token',
            'price': 0.001234,
            'source': 'trend'
        }
        
        print("   ✅ 模拟代币数据创建成功")
        
        # 检查ChartWidget的display_provided_ohlcv方法
        print("\n6. 检查ChartWidget方法...")
        if hasattr(ChartWidget, 'display_provided_ohlcv'):
            print("   ✅ display_provided_ohlcv")
        else:
            print("   ❌ display_provided_ohlcv - 缺失!")
            return False
            
        if hasattr(ChartWidget, 'set_token'):
            print("   ✅ set_token")
        else:
            print("   ❌ set_token - 缺失!")
            return False
        
        print("\n🎉 双击代币打开K线图功能测试通过！")
        print("\n功能说明:")
        print("   1. 双击代币行 → 触发 on_token_double_clicked")
        print("   2. 检查缓存数据 → get_cached_ohlcv_data (优先从HeadlessWidget获取)")  
        print("   3. 如有数据 → 直接打开图表 open_chart_with_cached_data")
        print("   4. 如无数据 → 提示用户获取数据 start_single_token_analysis") 
        print("   5. 数据下载完成 → 自动打开图表 on_single_download_completed")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False

if __name__ == '__main__':
    success = test_double_click_functionality()
    if success:
        print("\n✅ 测试完成 - 功能实现正确")
        sys.exit(0)
    else:
        print("\n❌ 测试失败 - 请检查实现")
        sys.exit(1) 