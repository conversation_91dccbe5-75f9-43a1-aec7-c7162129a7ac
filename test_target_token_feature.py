#!/usr/bin/env python3
"""
测试目标代币选择功能
"""

import sys
import os
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from ui.portfolio_widget import PortfolioWidget


class TestWindow(QMainWindow):
    """测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("目标代币选择功能测试")
        self.setGeometry(100, 100, 1200, 800)
        
        # 创建中央组件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 标题
        title = QLabel("🎯 目标代币选择功能测试")
        title.setFont(QFont("Arial", 16, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("color: #2E8B57; padding: 10px;")
        layout.addWidget(title)
        
        # 说明文字
        description = QLabel("""
✨ 新功能说明：

🟣 SOL (推荐)：
  • 原生代币，流动性最好
  • 直接用于支付Gas费
  • 滑点通常更小
  • 无需额外转换步骤

💚 USDC：
  • 稳定币，价值稳定
  • 适合保值用途
  • 广泛支持的稳定币

📝 使用说明：
1. 在"卖出目标"下拉框中选择目标代币
2. 按钮会显示对应的图标：💰→🟣 (SOL) 或 💰→💚 (USDC)
3. 确认对话框会显示目标代币信息
4. 交易成功后会显示实际收到的代币

🔧 测试内容：
• 目标代币选择下拉框
• 按钮图标动态更新
• 确认对话框显示
• 错误处理和按钮状态恢复
        """)
        description.setWordWrap(True)
        description.setStyleSheet("""
            background-color: #2c3e50; 
            color: #ffffff; 
            padding: 20px; 
            border-radius: 10px; 
            font-size: 14px; 
            font-weight: bold;
            border: 2px solid #34495e;
            line-height: 1.4;
        """)
        layout.addWidget(description)
        
        # Portfolio组件
        self.portfolio_widget = PortfolioWidget()
        layout.addWidget(self.portfolio_widget)
        
        print("🎯 目标代币选择功能测试窗口已创建")
        print("📋 测试要点：")
        print("   1. 查看'卖出目标'下拉框（默认选择SOL）")
        print("   2. 切换目标代币，观察按钮图标变化")
        print("   3. 测试卖出功能，查看确认对话框")
        print("   4. 验证错误处理时按钮状态恢复")


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序信息
    app.setApplicationName("目标代币选择功能测试")
    app.setApplicationVersion("1.0")
    
    # 创建并显示主窗口
    window = TestWindow()
    window.show()
    
    print("🚀 目标代币选择功能测试启动")
    print("🎯 这次的改进让用户可以选择卖出的目标代币")
    print("🟣 SOL是推荐选择，因为它是Solana的原生代币")
    print("💰 快去试试新的目标代币选择功能！")
    
    # 运行应用程序
    sys.exit(app.exec_())


if __name__ == "__main__":
    main() 