"""测试策略信号生成"""

import pandas as pd
import numpy as np
from strategies import StrategyFactory
from indicators import TechnicalIndicators

def test_strategy_signals():
    """测试各个策略是否能产生信号"""
    
    # 创建模拟数据
    np.random.seed(42)
    n_points = 200
    
    # 生成价格数据，模拟一些上涨和下跌的趋势
    base_price = 0.001
    trend = np.linspace(0, 0.0005, n_points)
    noise = np.random.normal(0, 0.00001, n_points)
    prices = base_price + trend + noise
    
    # 添加一些明显的价格波动
    # 在第50个点附近创建一个下跌
    prices[45:55] *= 0.95
    # 在第100个点附近创建一个上涨
    prices[95:105] *= 1.05
    # 在第150个点附近再创建一个下跌
    prices[145:155] *= 0.97
    
    # 创建DataFrame
    df = pd.DataFrame({
        'timestamp': pd.date_range(start='2024-01-01', periods=n_points, freq='1min').astype(int) // 10**9,
        'open': prices * (1 + np.random.uniform(-0.001, 0.001, n_points)),
        'high': prices * (1 + np.random.uniform(0, 0.002, n_points)),
        'low': prices * (1 - np.random.uniform(0, 0.002, n_points)),
        'close': prices,
        'volume': np.random.uniform(1000, 10000, n_points)
    })
    
    # 设置时间索引
    df['datetime_pd'] = pd.to_datetime(df['timestamp'], unit='s')
    df = df.set_index('datetime_pd')
    
    # 添加技术指标
    df = TechnicalIndicators.add_all_indicators(df)
    
    print("测试数据准备完成")
    print(f"数据形状: {df.shape}")
    print(f"价格范围: {df['close'].min():.6f} - {df['close'].max():.6f}")
    print(f"VWAP范围: {df['vwap'].min():.6f} - {df['vwap'].max():.6f}" if 'vwap' in df.columns else "VWAP未计算")
    print("\n")
    
    # 测试所有策略
    strategies = StrategyFactory.get_all_strategies()
    
    for strategy in strategies:
        print(f"测试策略: {strategy.name}")
        print("-" * 50)
        
        # 生成信号
        df_with_signals = strategy.generate_signals(df.copy())
        
        if 'signal' in df_with_signals.columns:
            buy_signals = (df_with_signals['signal'] == 1).sum()
            sell_signals = (df_with_signals['signal'] == -1).sum()
            
            print(f"买入信号数: {buy_signals}")
            print(f"卖出信号数: {sell_signals}")
            
            # 显示前几个信号的位置
            if buy_signals > 0:
                buy_indices = df_with_signals[df_with_signals['signal'] == 1].index[:3]
                print(f"前几个买入信号时间: {[str(idx) for idx in buy_indices]}")
                
            if sell_signals > 0:
                sell_indices = df_with_signals[df_with_signals['signal'] == -1].index[:3]
                print(f"前几个卖出信号时间: {[str(idx) for idx in sell_indices]}")
        else:
            print("策略未生成signal列")
            
        print("\n")

if __name__ == "__main__":
    test_strategy_signals() 