#!/usr/bin/env python3
"""
快速测试图表修复
"""

import sys
import logging
from PyQt5.QtWidgets import QApplication, QMainWindow
from PyQt5.QtCore import QTimer

from api_service import APIService
from ui.chart_widget import ChartWidget

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TestChartWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("测试图表修复")
        self.resize(1000, 700)
        
        # 创建API服务
        self.api_service = APIService(self)
        
        # 创建图表组件
        self.chart_widget = ChartWidget(api_service=self.api_service)
        self.setCentralWidget(self.chart_widget)
        
        # 等待2秒后设置测试代币
        QTimer.singleShot(2000, self.set_test_token)
    
    def set_test_token(self):
        """设置测试代币数据"""
        # 使用一个已知的Solana代币地址进行测试
        test_token = {
            'tokenAddress': 'So11111111111111111111111111111111111111112',  # SOL
            'symbol': 'SOL',
            'name': 'Wrapped SOL',
            'source': 'trend',  # 使用Birdeye API
            'price': 100.0,
            'priceChange24h': 5.2
        }
        
        logger.info("设置测试代币: SOL")
        self.chart_widget.set_token(test_token)

if __name__ == "__main__":
    app = QApplication(sys.argv)
    
    window = TestChartWindow()
    window.show()
    
    logger.info("测试窗口已启动...")
    
    sys.exit(app.exec_()) 