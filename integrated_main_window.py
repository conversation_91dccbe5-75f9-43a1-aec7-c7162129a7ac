#!/usr/bin/env python3
"""
超级优化版集成主程序 - 智能模式切换，彻底解决CPU占用问题
"""

import sys
import logging
from typing import Dict, List, Set
from datetime import datetime
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, 
    QWidget, QPushButton, QLabel, QTabWidget, QFrame,
    QCheckBox, QSpinBox, QMessageBox, QGroupBox, QProgressBar,
    QRadioButton, QButtonGroup
)
from PyQt5.QtCore import QTimer, pyqtSlot, pyqtSignal, QObject
from PyQt5.QtGui import QIcon

# 导入两个窗口组件
from trending_window import TrendingWindow
from holdings_panel_window import HoldingsWindow
from api_service import APIService
from multi_thread_ohlcv_manager import MultiThreadOHLCVManager


class OptimizedStrategySignalAggregator(QObject):
    """优化版策略信号聚合器 - 单例模式，包含性能监控"""
    
    _instance = None
    signal_received = pyqtSignal(str, str, str, str, float, int)  # token_address, symbol, signal_type, source, price, timestamp
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance.initialized = False
        return cls._instance
    
    def __init__(self):
        if not self.initialized:
            super().__init__()
            self.signals = []
            self.ignored_tokens = {
                'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',  # USDC稳定币
                '11111111111111111111111111111111',  # SOL (如果不想分析原生SOL)
                '11111111111111111111111111111112',  # WSOL
            }
            self.signal_count_by_source = {'trending': 0, 'holdings': 0}
            self.max_signals = 1000  # 限制信号数量，避免内存溢出
            self.initialized = True
            logging.info("OptimizedStrategySignalAggregator: 单例初始化完成")
    
    def add_signal(self, token_address: str, symbol: str, signal_type: str, 
                   price: float, timestamp: int, source: str):
        """添加策略信号"""
        # 忽略稳定币
        if token_address in self.ignored_tokens:
            logging.debug(f"信号聚合器: 忽略稳定币 {symbol} ({token_address})")
            return
            
        # 检查是否是重复信号（相同代币，相同信号类型，时间间隔小于30秒）
        current_time = timestamp
        for existing_signal in reversed(self.signals[-10:]):  # 只检查最近10个信号
            if (existing_signal['token_address'] == token_address and 
                existing_signal['signal_type'] == signal_type and
                abs(current_time - existing_signal['timestamp']) < 30):
                logging.debug(f"信号聚合器: 跳过重复信号 {symbol} {signal_type}")
                return
        
        signal = {
            'token_address': token_address,
            'symbol': symbol,
            'signal_type': signal_type,
            'price': price,
            'timestamp': timestamp,
            'source': source
        }
        
        self.signals.append(signal)
        self.signal_count_by_source[source] += 1
        
        # 限制信号数量，移除旧信号
        if len(self.signals) > self.max_signals:
            removed_signal = self.signals.pop(0)
            removed_source = removed_signal['source']
            self.signal_count_by_source[removed_source] -= 1
        
        # 发射信号
        self.signal_received.emit(token_address, symbol, signal_type, source, price, timestamp)
        
        logging.info(f"信号聚合器: 添加信号 - {symbol} {signal_type} (来源: {source})")
    
    def get_recent_signals(self, limit: int = 50):
        """获取最近的信号"""
        return self.signals[-limit:]
    
    def get_statistics(self) -> Dict:
        """获取统计信息"""
        return {
            'total_signals': len(self.signals),
            'trending_signals': self.signal_count_by_source['trending'],
            'holdings_signals': self.signal_count_by_source['holdings'],
            'ignored_tokens_count': len(self.ignored_tokens)
        }


class SuperOptimizedIntegratedMainWindow(QMainWindow):
    """超级优化版集成主窗口 - 智能模式切换，彻底解决CPU问题"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("智能交易助手 - 超级优化版 (智能模式切换)")
        self.setGeometry(100, 100, 1400, 900)
        
        # 🔥 关键：运行模式管理
        self.current_mode = "智能模式"  # 智能模式、高性能模式、精简模式
        self.active_window = None  # 当前活跃窗口
        
        # 共享API服务
        self.api_service = APIService()
        
        # 🔥 超级关键：共享多线程管理器，极低并发数
        self.shared_multi_thread_manager = MultiThreadOHLCVManager(
            api_service=self.api_service,
            max_workers=2,  # 进一步降低到2个并发
            parent=self
        )
        
        # 策略信号聚合器
        self.signal_aggregator = OptimizedStrategySignalAggregator()
        
        # 性能监控
        self.performance_stats = {
            'api_calls_count': 0,
            'last_refresh_time': None,
            'memory_usage_mb': 0,
            'cpu_load': 0.0
        }
        
        # 🔥 模式控制标志
        self.trending_enabled = True
        self.holdings_enabled = True
        self.strategy_analysis_enabled = True
        
        # 初始化UI
        self._init_ui()
        
        # 连接信号
        self._connect_signals()
        
        # 🔥 超级优化：智能协调器，根据模式动态调整
        self._setup_intelligent_coordinator()
        
        logging.info("SuperOptimizedIntegratedMainWindow: 超级优化版初始化完成")
    
    def _init_ui(self):
        """初始化用户界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        
        # 🔥 新增：智能模式选择面板
        self._create_intelligent_mode_panel(layout)
        
        # 性能控制面板
        self._create_performance_control_panel(layout)
        
        # 标签页
        self.tab_widget = QTabWidget()
        
        # 🔥 优化：创建窗口时默认只启动一个，根据模式动态开启
        
        # 趋势榜单标签页
        self.trending_window = TrendingWindow(
            parent=self, 
            api_service=self.api_service
        )
        # 替换趋势窗口的多线程管理器为共享版本
        self.trending_window.multi_thread_manager = self.shared_multi_thread_manager
        # 🔥 默认禁用独立刷新定时器
        if hasattr(self.trending_window, 'refresh_timer'):
            self.trending_window.refresh_timer.stop()
        
        self.tab_widget.addTab(self.trending_window, "📈 趋势榜单")
        
        # 持仓面板标签页
        self.holdings_window = HoldingsWindow(
            parent=self,
            api_service=self.api_service
        )
        # 替换持仓窗口的多线程管理器为共享版本
        self.holdings_window.multi_thread_manager = self.shared_multi_thread_manager
        # 🔥 默认禁用独立刷新定时器
        if hasattr(self.holdings_window, 'refresh_timer'):
            self.holdings_window.refresh_timer.stop()
        
        self.tab_widget.addTab(self.holdings_window, "💰 持仓管理")
        
        # 连接标签页切换事件
        self.tab_widget.currentChanged.connect(self.on_tab_changed)
        
        layout.addWidget(self.tab_widget)
        
        # 信号聚合状态显示
        self._create_signal_status_panel(layout)
        
        # 状态栏
        self.status_bar = self.statusBar()
        self.status_bar.showMessage("就绪 - 超级优化版，智能模式切换")
    
    def _create_intelligent_mode_panel(self, layout):
        """创建智能模式选择面板"""
        mode_group = QGroupBox("🧠 智能模式切换")
        mode_layout = QHBoxLayout(mode_group)
        
        # 模式选择按钮组
        self.mode_button_group = QButtonGroup()
        
        # 智能模式（默认）
        self.intelligent_mode_btn = QRadioButton("🧠 智能模式")
        self.intelligent_mode_btn.setChecked(True)
        self.intelligent_mode_btn.setToolTip("根据当前标签页智能调度，只运行活跃窗口的分析")
        self.mode_button_group.addButton(self.intelligent_mode_btn, 0)
        mode_layout.addWidget(self.intelligent_mode_btn)
        
        # 精简模式
        self.lite_mode_btn = QRadioButton("⚡ 精简模式")
        self.lite_mode_btn.setToolTip("禁用策略分析，只显示基础数据，最低CPU占用")
        self.mode_button_group.addButton(self.lite_mode_btn, 1)
        mode_layout.addWidget(self.lite_mode_btn)
        
        # 睡眠模式
        self.sleep_mode_btn = QRadioButton("😴 睡眠模式")
        self.sleep_mode_btn.setToolTip("停止所有自动刷新，手动操作模式")
        self.mode_button_group.addButton(self.sleep_mode_btn, 2)
        mode_layout.addWidget(self.sleep_mode_btn)
        
        # 高性能模式（开发者模式）
        self.performance_mode_btn = QRadioButton("🚀 高性能模式")
        self.performance_mode_btn.setToolTip("两个窗口都启用，适合高配置机器")
        self.mode_button_group.addButton(self.performance_mode_btn, 3)
        mode_layout.addWidget(self.performance_mode_btn)
        
        # 连接模式切换事件
        self.mode_button_group.buttonClicked.connect(self.on_mode_changed)
        
        mode_layout.addStretch()
        layout.addWidget(mode_group)
    
    def _create_performance_control_panel(self, layout):
        """创建性能控制面板"""
        perf_group = QGroupBox("🚀 性能控制")
        perf_layout = QHBoxLayout(perf_group)
        
        # 当前活跃窗口显示
        self.active_window_label = QLabel("活跃窗口: 趋势榜单")
        perf_layout.addWidget(self.active_window_label)
        
        # 刷新间隔控制
        perf_layout.addWidget(QLabel("刷新间隔:"))
        self.refresh_interval_spinbox = QSpinBox()
        self.refresh_interval_spinbox.setRange(30, 300)  # 30秒到5分钟
        self.refresh_interval_spinbox.setValue(90)  # 默认90秒，更保守
        self.refresh_interval_spinbox.setSuffix(" 秒")
        self.refresh_interval_spinbox.valueChanged.connect(self.on_refresh_interval_changed)
        perf_layout.addWidget(self.refresh_interval_spinbox)
        
        # 手动刷新按钮
        manual_refresh_btn = QPushButton("🔄 手动刷新当前窗口")
        manual_refresh_btn.clicked.connect(self.manual_refresh_current_window)
        perf_layout.addWidget(manual_refresh_btn)
        
        # 暂停所有分析
        self.pause_analysis_btn = QPushButton("⏸️ 暂停分析")
        self.pause_analysis_btn.clicked.connect(self.toggle_analysis_pause)
        perf_layout.addWidget(self.pause_analysis_btn)
        
        # 性能统计
        self.perf_stats_label = QLabel("API调用: 0 | 内存: 0MB | CPU: 0%")
        perf_layout.addWidget(self.perf_stats_label)
        
        perf_layout.addStretch()
        layout.addWidget(perf_group)
    
    def _create_signal_status_panel(self, layout):
        """创建信号状态面板"""
        signal_group = QGroupBox("📊 策略信号聚合状态")
        signal_layout = QHBoxLayout(signal_group)
        
        # 信号统计
        self.signal_stats_label = QLabel("总信号: 0 | 趋势: 0 | 持仓: 0")
        signal_layout.addWidget(self.signal_stats_label)
        
        # 最新信号显示
        self.latest_signal_label = QLabel("最新信号: 无")
        signal_layout.addWidget(self.latest_signal_label)
        
        # 忽略稳定币开关
        self.ignore_stablecoins = QCheckBox("忽略稳定币")
        self.ignore_stablecoins.setChecked(True)
        signal_layout.addWidget(self.ignore_stablecoins)
        
        # 清除信号历史
        clear_signals_btn = QPushButton("🗑️ 清除信号历史")
        clear_signals_btn.clicked.connect(self.clear_signal_history)
        signal_layout.addWidget(clear_signals_btn)
        
        signal_layout.addStretch()
        layout.addWidget(signal_group)
    
    def _setup_intelligent_coordinator(self):
        """设置智能协调器"""
        # 🔥 智能刷新定时器：根据模式和活跃窗口动态调整
        self.intelligent_refresh_timer = QTimer(self)
        self.intelligent_refresh_timer.timeout.connect(self.intelligent_refresh)
        
        # 信号统计更新定时器
        self.signal_stats_timer = QTimer()
        self.signal_stats_timer.timeout.connect(self.update_signal_statistics)
        self.signal_stats_timer.start(5000)  # 每5秒更新一次
        
        # 性能监控定时器
        self.performance_monitor_timer = QTimer()
        self.performance_monitor_timer.timeout.connect(self.update_performance_stats)
        self.performance_monitor_timer.start(15000)  # 每15秒监控一次，降低频率
        
        # 根据初始模式启动
        self.on_mode_changed()
        
        logging.info("SuperOptimizedIntegratedMainWindow: 智能协调器启动")
    
    def _connect_signals(self):
        """连接信号"""
        # 连接共享多线程管理器的策略信号
        self.shared_multi_thread_manager.strategy_signal_generated.connect(
            self.on_unified_strategy_signal_received
        )
        
        # 连接信号聚合器的信号
        self.signal_aggregator.signal_received.connect(self.on_aggregated_signal_received)
    
    def on_mode_changed(self):
        """模式切换处理"""
        # 停止现有定时器
        if self.intelligent_refresh_timer.isActive():
            self.intelligent_refresh_timer.stop()
        
        # 获取选中的模式
        selected_mode = self.mode_button_group.checkedId()
        
        if selected_mode == 0:  # 智能模式
            self.current_mode = "智能模式"
            self.strategy_analysis_enabled = True
            self.trending_enabled = True
            self.holdings_enabled = True
            self.shared_multi_thread_manager.max_workers = 2
            # 启动智能刷新，间隔更长
            interval = self.refresh_interval_spinbox.value() * 1000
            self.intelligent_refresh_timer.start(interval)
            self.status_bar.showMessage("智能模式: 根据活跃窗口智能调度")
            
        elif selected_mode == 1:  # 精简模式
            self.current_mode = "精简模式"
            self.strategy_analysis_enabled = False
            self.trending_enabled = True
            self.holdings_enabled = True
            self.shared_multi_thread_manager.max_workers = 1
            # 启动精简刷新，间隔更长
            interval = max(self.refresh_interval_spinbox.value() * 1500, 120000)  # 至少2分钟
            self.intelligent_refresh_timer.start(interval)
            self.status_bar.showMessage("精简模式: 禁用策略分析，最低CPU占用")
            
        elif selected_mode == 2:  # 睡眠模式
            self.current_mode = "睡眠模式"
            self.strategy_analysis_enabled = False
            self.trending_enabled = False
            self.holdings_enabled = False
            self.shared_multi_thread_manager.max_workers = 1
            # 不启动自动刷新
            self.status_bar.showMessage("睡眠模式: 停止所有自动操作")
            
        elif selected_mode == 3:  # 高性能模式
            self.current_mode = "高性能模式"
            self.strategy_analysis_enabled = True
            self.trending_enabled = True
            self.holdings_enabled = True
            self.shared_multi_thread_manager.max_workers = 3
            # 启动高性能刷新
            interval = max(self.refresh_interval_spinbox.value() * 1000, 60000)  # 至少1分钟
            self.intelligent_refresh_timer.start(interval)
            self.status_bar.showMessage("高性能模式: 全功能启用")
        
        logging.info(f"SuperOptimizedIntegratedMainWindow: 切换到{self.current_mode}")
    
    def on_tab_changed(self, index):
        """标签页切换处理"""
        if index == 0:
            self.active_window = "trending"
            self.active_window_label.setText("活跃窗口: 趋势榜单")
        elif index == 1:
            self.active_window = "holdings"
            self.active_window_label.setText("活跃窗口: 持仓管理")
        
        logging.info(f"SuperOptimizedIntegratedMainWindow: 切换到{self.active_window}窗口")
    
    def intelligent_refresh(self):
        """智能刷新 - 根据模式和活跃窗口调度"""
        try:
            current_time = datetime.now()
            self.performance_stats['last_refresh_time'] = current_time
            
            if self.current_mode == "睡眠模式":
                return  # 睡眠模式不刷新
            
            # 🔥 关键优化：只刷新当前活跃的窗口
            current_tab = self.tab_widget.currentIndex()
            
            if self.current_mode == "智能模式":
                # 智能模式：只刷新当前活跃窗口
                if current_tab == 0 and self.trending_enabled:
                    self.status_bar.showMessage("智能刷新: 更新趋势榜单...")
                    self.refresh_trending_data()
                elif current_tab == 1 and self.holdings_enabled:
                    self.status_bar.showMessage("智能刷新: 更新持仓数据...")
                    self.refresh_holdings_data()
                
                # 更新API调用计数
                self.performance_stats['api_calls_count'] += 1
                
            elif self.current_mode == "精简模式":
                # 精简模式：只刷新数据，不做策略分析
                if current_tab == 0:
                    self.refresh_trending_data_only()
                elif current_tab == 1:
                    self.refresh_holdings_data_only()
                    
                self.performance_stats['api_calls_count'] += 1
                
            elif self.current_mode == "高性能模式":
                # 高性能模式：错开刷新两个窗口
                if current_tab == 0:
                    self.refresh_trending_data()
                    QTimer.singleShot(10000, self.refresh_holdings_data)  # 延迟10秒
                else:
                    self.refresh_holdings_data()
                    QTimer.singleShot(10000, self.refresh_trending_data)
                    
                self.performance_stats['api_calls_count'] += 2
            
            # 更新状态
            QTimer.singleShot(5000, lambda: self.status_bar.showMessage(f"{self.current_mode} - 刷新完成"))
            
        except Exception as e:
            logging.error(f"SuperOptimizedIntegratedMainWindow: 智能刷新失败: {e}")
            self.status_bar.showMessage(f"刷新失败: {str(e)}")
    
    def refresh_trending_data(self):
        """刷新趋势数据"""
        try:
            if hasattr(self.trending_window, 'refresh_trending_list'):
                self.trending_window.refresh_trending_list()
                logging.info("SuperOptimizedIntegratedMainWindow: 趋势数据刷新完成")
        except Exception as e:
            logging.error(f"SuperOptimizedIntegratedMainWindow: 刷新趋势数据失败: {e}")
    
    def refresh_holdings_data(self):
        """刷新持仓数据"""
        try:
            if hasattr(self.holdings_window, 'refresh_holdings'):
                self.holdings_window.refresh_holdings()
                logging.info("SuperOptimizedIntegratedMainWindow: 持仓数据刷新完成")
        except Exception as e:
            logging.error(f"SuperOptimizedIntegratedMainWindow: 刷新持仓数据失败: {e}")
    
    def refresh_trending_data_only(self):
        """精简模式：只刷新趋势数据，不做策略分析"""
        try:
            # 暂时禁用策略分析
            original_strategy_state = getattr(self.trending_window, 'strategy_enabled_checkbox', None)
            if original_strategy_state:
                original_strategy_state.setChecked(False)
            
            self.refresh_trending_data()
            
            # 恢复策略分析状态（如果需要）
            if original_strategy_state and self.strategy_analysis_enabled:
                original_strategy_state.setChecked(True)
                
        except Exception as e:
            logging.error(f"SuperOptimizedIntegratedMainWindow: 精简刷新趋势数据失败: {e}")
    
    def refresh_holdings_data_only(self):
        """精简模式：只刷新持仓数据，不做策略分析"""
        try:
            # 暂时禁用策略分析
            original_strategy_state = getattr(self.holdings_window, 'strategy_enabled_checkbox', None)
            if original_strategy_state:
                original_strategy_state.setChecked(False)
            
            self.refresh_holdings_data()
            
            # 恢复策略分析状态（如果需要）
            if original_strategy_state and self.strategy_analysis_enabled:
                original_strategy_state.setChecked(True)
                
        except Exception as e:
            logging.error(f"SuperOptimizedIntegratedMainWindow: 精简刷新持仓数据失败: {e}")
    
    def manual_refresh_current_window(self):
        """手动刷新当前窗口"""
        current_tab = self.tab_widget.currentIndex()
        self.status_bar.showMessage("正在手动刷新当前窗口...")
        
        if current_tab == 0:
            self.refresh_trending_data()
        elif current_tab == 1:
            self.refresh_holdings_data()
        
        QTimer.singleShot(3000, lambda: self.status_bar.showMessage("手动刷新完成"))
        logging.info("SuperOptimizedIntegratedMainWindow: 手动刷新完成")
    
    def toggle_analysis_pause(self):
        """切换分析暂停状态"""
        if self.strategy_analysis_enabled:
            # 暂停分析
            self.strategy_analysis_enabled = False
            self.pause_analysis_btn.setText("▶️ 恢复分析")
            self.status_bar.showMessage("已暂停所有策略分析")
            
            # 停止所有正在进行的分析
            if hasattr(self.shared_multi_thread_manager, 'force_stop_all_tasks'):
                self.shared_multi_thread_manager.force_stop_all_tasks()
                
        else:
            # 恢复分析
            self.strategy_analysis_enabled = True
            self.pause_analysis_btn.setText("⏸️ 暂停分析")
            self.status_bar.showMessage("已恢复策略分析")
        
        logging.info(f"SuperOptimizedIntegratedMainWindow: 策略分析状态切换为 {self.strategy_analysis_enabled}")
    
    def on_refresh_interval_changed(self, value):
        """刷新间隔改变"""
        if self.intelligent_refresh_timer.isActive():
            # 根据当前模式调整间隔
            if self.current_mode == "精简模式":
                new_interval = max(value * 1500, 120000)  # 精简模式至少2分钟
            elif self.current_mode == "高性能模式":
                new_interval = max(value * 1000, 60000)  # 高性能模式至少1分钟
            else:
                new_interval = value * 1000
                
            self.intelligent_refresh_timer.setInterval(new_interval)
        
        self.status_bar.showMessage(f"刷新间隔已设置为 {value} 秒")
        logging.info(f"SuperOptimizedIntegratedMainWindow: 刷新间隔设置为 {value} 秒")
    
    @pyqtSlot(str, str, str, float, int, str)
    def on_unified_strategy_signal_received(self, token_address: str, symbol: str, signal_type: str,
                                          price: float, timestamp: int, strategy_name: str):
        """处理统一的策略信号"""
        # 判断信号来源（基于当前活跃的标签页）
        current_tab_index = self.tab_widget.currentIndex()
        if current_tab_index == 0:
            source = 'trending'
        elif current_tab_index == 1:
            source = 'holdings'
        else:
            source = 'unified'
        
        # 发送到信号聚合器
        self.signal_aggregator.add_signal(
            token_address=token_address,
            symbol=symbol,
            signal_type=signal_type,
            price=price,
            timestamp=timestamp,
            source=source
        )
        
        logging.info(f"主窗口: 接收到统一策略信号 - {symbol} {signal_type} (来源: {source})")
    
    @pyqtSlot(str, str, str, str, float, int)
    def on_aggregated_signal_received(self, token_address: str, symbol: str, signal_type: str,
                                    source: str, price: float, timestamp: int):
        """处理聚合后的信号"""
        # 更新最新信号显示
        time_str = datetime.fromtimestamp(timestamp).strftime("%H:%M:%S")
        self.latest_signal_label.setText(f"最新信号: {symbol} {signal_type} ({source}) {time_str}")
        
        # 🔥 将信号分发回对应的窗口（避免重复处理）
        if source == 'trending' and hasattr(self.trending_window, 'update_strategy_column'):
            signal_mapping = {'buy': '买入', 'sell': '卖出', 'hold': '持有', 'wait': '观察'}
            signal_zh = signal_mapping.get(signal_type, signal_type)
            self.trending_window.update_strategy_column(token_address, signal_zh)
        
        elif source == 'holdings' and hasattr(self.holdings_window, 'update_strategy_column'):
            signal_mapping = {'buy': '买入', 'sell': '卖出', 'hold': '持有', 'wait': '观察'}
            signal_zh = signal_mapping.get(signal_type, signal_type)
            time_str = datetime.fromtimestamp(timestamp).strftime("%H:%M:%S")
            self.holdings_window.update_strategy_column(token_address, signal_zh, time_str)
    
    def update_signal_statistics(self):
        """更新信号统计"""
        try:
            stats = self.signal_aggregator.get_statistics()
            self.signal_stats_label.setText(
                f"总信号: {stats['total_signals']} | "
                f"趋势: {stats['trending_signals']} | "
                f"持仓: {stats['holdings_signals']}"
            )
        except Exception as e:
            logging.error(f"SuperOptimizedIntegratedMainWindow: 更新信号统计失败: {e}")
    
    def update_performance_stats(self):
        """更新性能统计"""
        try:
            import psutil
            import os
            
            # 获取当前进程的内存和CPU使用情况
            process = psutil.Process(os.getpid())
            memory_info = process.memory_info()
            memory_mb = memory_info.rss / 1024 / 1024  # 转换为MB
            cpu_percent = process.cpu_percent()
            
            self.performance_stats['memory_usage_mb'] = memory_mb
            self.performance_stats['cpu_load'] = cpu_percent
            
            self.perf_stats_label.setText(
                f"API调用: {self.performance_stats['api_calls_count']} | "
                f"内存: {memory_mb:.1f}MB | "
                f"CPU: {cpu_percent:.1f}%"
            )
            
        except ImportError:
            # 如果没有psutil，只显示API调用统计
            self.perf_stats_label.setText(f"API调用: {self.performance_stats['api_calls_count']}")
        except Exception as e:
            logging.error(f"SuperOptimizedIntegratedMainWindow: 更新性能统计失败: {e}")
    
    def clear_signal_history(self):
        """清除信号历史"""
        reply = QMessageBox.question(
            self, 
            '确认清除', 
            '确定要清除所有信号历史吗？',
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.signal_aggregator.signals.clear()
            self.signal_aggregator.signal_count_by_source = {'trending': 0, 'holdings': 0}
            self.latest_signal_label.setText("最新信号: 无")
            self.signal_stats_label.setText("总信号: 0 | 趋势: 0 | 持仓: 0")
            self.status_bar.showMessage("信号历史已清除")
            logging.info("SuperOptimizedIntegratedMainWindow: 信号历史已清除")
    
    def closeEvent(self, event):
        """窗口关闭事件"""
        logging.info("SuperOptimizedIntegratedMainWindow: 正在关闭...")
        
        # 停止所有定时器
        timers = [
            'intelligent_refresh_timer', 'signal_stats_timer', 
            'performance_monitor_timer'
        ]
        for timer_name in timers:
            if hasattr(self, timer_name):
                timer = getattr(self, timer_name)
                if timer.isActive():
                    timer.stop()
        
        # 关闭共享多线程管理器
        if hasattr(self, 'shared_multi_thread_manager'):
            self.shared_multi_thread_manager.shutdown()
        
        # 关闭子窗口
        if hasattr(self, 'trending_window'):
            self.trending_window.close()
        
        if hasattr(self, 'holdings_window'):
            self.holdings_window.close()
        
        super().closeEvent(event)


def main():
    """主函数"""
    print("🚀 启动智能交易助手 - 超级优化版集成模式")
    print("="*70)
    
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(name)s - %(message)s'
    )
    
    # 创建应用程序
    app = QApplication(sys.argv)
    app.setApplicationName("智能交易助手-超级优化版")
    app.setApplicationVersion("3.0")
    
    try:
        # 创建超级优化版主窗口
        main_window = SuperOptimizedIntegratedMainWindow()
        main_window.show()
        
        # 启动趋势榜单数据加载（延迟启动避免冲突）
        QTimer.singleShot(3000, lambda: main_window.trending_window.start_data_loading())
        
        print("✅ 超级优化版集成主程序已启动")
        print("\n🔥 超级优化特性:")
        print("• 🧠 智能模式切换: 根据使用场景动态调整")
        print("• ⚡ 精简模式: 禁用策略分析，最低CPU占用")
        print("• 😴 睡眠模式: 停止所有自动操作")
        print("• 🚀 高性能模式: 适合高配置机器")
        print("• 🎯 智能调度: 只运行当前活跃窗口的分析")
        print("• 🔄 协调刷新: 彻底避免API冲突")
        print("• 📊 实时性能监控: CPU和内存使用情况")
        
        print("\n💡 使用建议:")
        print("• 🧠 日常使用建议选择'智能模式'")
        print("• ⚡ CPU占用高时切换到'精简模式'")
        print("• 😴 长时间不用时切换到'睡眠模式'")
        print("• 🚀 分析大量数据时使用'高性能模式'")
        print("• 🎯 充分利用标签页切换，系统只刷新当前窗口")
        
        # 运行应用程序
        sys.exit(app.exec_())
        
    except Exception as e:
        logging.error(f"启动失败: {e}")
        print(f"❌ 启动失败: {e}")


if __name__ == '__main__':
    main() 