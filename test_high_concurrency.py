#!/usr/bin/env python3
"""
测试高并发I/O密集型任务
"""

import multiprocessing
import concurrent.futures
import time
import requests
import os
from datetime import datetime

def io_intensive_task(task_id):
    """模拟I/O密集型任务（网络请求）"""
    start_time = time.time()
    pid = os.getpid()
    
    try:
        # 模拟API请求（低CPU占用，高I/O等待）
        response = requests.get('https://httpbin.org/delay/1', timeout=5)
        status = response.status_code
        
        # 一些轻量级计算
        result = sum(i * i for i in range(1000))
        
        end_time = time.time()
        duration = end_time - start_time
        
        return {
            'task_id': task_id,
            'pid': pid,
            'duration': duration,
            'status': status,
            'result': result,
            'cpu_intensive': False  # 标记为I/O密集型
        }
        
    except Exception as e:
        end_time = time.time()
        return {
            'task_id': task_id,
            'pid': pid,
            'duration': end_time - start_time,
            'error': str(e),
            'cpu_intensive': False
        }

def test_high_concurrency():
    """测试高并发I/O处理"""
    print(f"🖥️  系统信息:")
    print(f"   CPU核心数: {multiprocessing.cpu_count()}")
    print(f"   测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 测试不同的并发级别
    concurrency_levels = [4, 8, 16]
    num_tasks = 20
    
    for workers in concurrency_levels:
        if workers > multiprocessing.cpu_count():
            continue
            
        print(f"\n🚀 测试并发数: {workers}")
        print(f"   任务数量: {num_tasks}")
        
        start_time = time.time()
        
        # 使用进程池执行I/O密集型任务
        with concurrent.futures.ProcessPoolExecutor(max_workers=workers) as executor:
            futures = [executor.submit(io_intensive_task, i) for i in range(num_tasks)]
            results = [future.result() for future in futures]
        
        total_time = time.time() - start_time
        
        # 分析结果
        successful_tasks = [r for r in results if 'error' not in r]
        failed_tasks = [r for r in results if 'error' in r]
        unique_pids = set(r['pid'] for r in results)
        
        print(f"   总耗时: {total_time:.2f}秒")
        print(f"   成功任务: {len(successful_tasks)}/{num_tasks}")
        print(f"   失败任务: {len(failed_tasks)}")
        print(f"   使用进程数: {len(unique_pids)}")
        
        if successful_tasks:
            avg_task_time = sum(r['duration'] for r in successful_tasks) / len(successful_tasks)
            print(f"   平均任务时间: {avg_task_time:.2f}秒")
            print(f"   吞吐量: {len(successful_tasks)/total_time:.2f} 任务/秒")
        
        # 显示进程使用情况
        pid_counts = {}
        for result in results:
            pid = result['pid']
            pid_counts[pid] = pid_counts.get(pid, 0) + 1
        
        print(f"   进程负载分布: {dict(sorted(pid_counts.items()))}")

def test_cpu_vs_io():
    """对比CPU密集型 vs I/O密集型的并发效果"""
    print(f"\n📊 CPU密集型 vs I/O密集型对比:")
    
    def cpu_intensive_task(task_id):
        """CPU密集型任务"""
        start_time = time.time()
        result = sum(i * i for i in range(1000000))  # 更多计算
        end_time = time.time()
        return {
            'task_id': task_id,
            'pid': os.getpid(),
            'duration': end_time - start_time,
            'result': result
        }
    
    # 测试CPU密集型（4进程）
    print(f"\n🔥 CPU密集型任务（4进程）:")
    start_time = time.time()
    with concurrent.futures.ProcessPoolExecutor(max_workers=4) as executor:
        cpu_results = [executor.submit(cpu_intensive_task, i).result() for i in range(8)]
    cpu_time = time.time() - start_time
    print(f"   总耗时: {cpu_time:.2f}秒")
    print(f"   吞吐量: {len(cpu_results)/cpu_time:.2f} 任务/秒")

if __name__ == "__main__":
    print("🚀 高并发I/O密集型任务测试\n")
    test_high_concurrency()
    test_cpu_vs_io()
    print(f"\n💡 建议:")
    print(f"   - 对于回测这种I/O密集型任务，可以使用更高的并发数")
    print(f"   - 您的16核系统适合使用16个并发进程")
    print(f"   - 充分利用网络请求等待时间，大幅提升整体效率") 