"""
Trending Hot List Window
"""
import logging
import time
import os
from typing import Dict, List, Optional
from datetime import datetime, timedelta

# 忽略 Qt 图像相关警告
os.environ['QT_LOGGING_RULES'] = '*.debug=false;qt.gui.icc.debug=false;qt.gui.imageio.debug=false'
# 静默图像处理库的警告
import warnings
warnings.filterwarnings("ignore", message=".*iCCP.*")
warnings.filterwarnings("ignore", message=".*libpng.*")
warnings.filterwarnings("ignore", message=".*fromIccProfile.*")

from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QTableWidget, QTableWidgetItem, QHeaderView, QPushButton,
    QLabel, QCheckBox, QFrame, QProgressBar
)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QThread, pyqtSlot, QUrl, QObject
from PyQt5.QtGui import QFont, QColor, QPixmap, QPainter, QCursor, QPixmapCache
from PyQt5.QtNetwork import QNetworkAccessManager, QNetworkRequest, QNetworkReply
import pandas as pd

# Assuming api_service.py is in the same directory or accessible via PYTHONPATH
from api_service import APIService 

# 🔥 导入多线程OHLCV管理器
from multi_thread_ohlcv_manager import MultiThreadOHLCVManager

# 🔥 新增：导入ChartWidget用于创建独立图表窗口
from ui.chart_widget import ChartWidget

logger = logging.getLogger(__name__)


class TokenImageLabel(QLabel):
    """代币图像标签，支持悬停显示大图"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setFixedSize(32, 32)
        self.setScaledContents(True)
        self.setAlignment(Qt.AlignCenter)
        self.setStyleSheet("""
            QLabel {
                border: 1px solid #34495e;
                border-radius: 4px;
                background-color: #2c3e50;
            }
            QLabel:hover {
                border: 2px solid #3498db;
            }
        """)
        
        # 悬停大图相关
        self.large_image_label = None
        self.image_url = ""
        self.token_symbol = ""
        
        # 🔥 修复：使用共享的网络管理器，避免创建过多实例
        self.network_manager = self._get_shared_network_manager()
        self.network_reply = None  # 跟踪当前的网络请求
        
        # 重试机制
        self.retry_count = 0
        self.max_retries = 2
        self.retry_timer = QTimer()
        self.retry_timer.setSingleShot(True)
        self.retry_timer.timeout.connect(self.retry_load_image)
        
        # 🔥 修复：标记销毁状态，避免异步回调中的错误
        self.is_destroyed = False
        
        # 默认占位图
        self.set_placeholder()
    
    def _get_shared_network_manager(self):
        """获取共享的网络管理器实例"""
        # 使用应用程序级别的共享网络管理器
        from PyQt5.QtWidgets import QApplication
        app = QApplication.instance()
        if not hasattr(app, '_token_image_network_manager'):
            app._token_image_network_manager = QNetworkAccessManager()
        return app._token_image_network_manager
    
    def _get_safe_error_info(self, reply):
        """安全地获取网络错误信息"""
        try:
            error_code = reply.error()
            error_string = reply.errorString() if hasattr(reply, 'errorString') else "Unknown error"
            return error_code, error_string
        except Exception:
            return -1, "Error info unavailable"
    
    def set_placeholder(self):
        """设置占位图"""
        pixmap = QPixmap(32, 32)
        pixmap.fill(QColor("#34495e"))
        
        # 绘制默认图标（代币符号的首字母）
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.Antialiasing)
        painter.setPen(QColor("#bdc3c7"))
        painter.setFont(QFont("Arial", 12, QFont.Bold))
        painter.drawText(pixmap.rect(), Qt.AlignCenter, "?")
        painter.end()
        
        self.setPixmap(pixmap)
    
    def load_token_image(self, image_url: str, symbol: str):
        """加载代币图像"""
        self.image_url = image_url
        self.token_symbol = symbol
        self.retry_count = 0
        
        if not image_url:
            self.set_placeholder_with_symbol(symbol)
            return
        
        cache_key = f"token_icon_{image_url}"
        cached_pixmap = QPixmapCache.find(cache_key)
        if cached_pixmap and not cached_pixmap.isNull():
            scaled_pixmap = cached_pixmap.scaled(32, 32, Qt.KeepAspectRatio, Qt.SmoothTransformation)
            self.setPixmap(scaled_pixmap)
            return
        
        self._request_image(image_url)
    
    def _request_image(self, image_url: str):
        """发起网络请求获取图像"""
        try:
            # 🔥 修复：取消之前的请求避免重复
            if self.network_reply and not self.network_reply.isFinished():
                self.network_reply.abort()
                self.network_reply.deleteLater()
                self.network_reply = None
            
            request = QNetworkRequest(QUrl(image_url))
            request.setHeader(QNetworkRequest.UserAgentHeader, "TrendTrader/1.0")
            # 设置超时时间
            request.setTransferTimeout(10000)  # 10秒超时
            # 设置重定向策略
            request.setAttribute(QNetworkRequest.RedirectPolicyAttribute, QNetworkRequest.NoLessSafeRedirectPolicy)
            
            # 🔥 修复：跟踪网络请求并连接回调
            self.network_reply = self.network_manager.get(request)
            self.network_reply.finished.connect(lambda: self.on_image_loaded(self.network_reply))
        except Exception as e:
            logger.error(f"发起图像请求失败 {image_url}: {e}")
            self.set_placeholder_with_symbol(self.token_symbol)
    
    def retry_load_image(self):
        """重试加载图像"""
        if self.retry_count < self.max_retries and self.image_url:
            self.retry_count += 1
            logger.info(f"重试加载图像 ({self.retry_count}/{self.max_retries}): {self.token_symbol}")
            self._request_image(self.image_url)
    
    def set_placeholder_with_symbol(self, symbol: str):
        """设置带符号的占位图"""
        pixmap = QPixmap(32, 32)
        pixmap.fill(QColor("#34495e"))
        
        painter = QPainter(pixmap)
        painter.setRenderHint(QPainter.Antialiasing)
        painter.setPen(QColor("#bdc3c7"))
        painter.setFont(QFont("Arial", 10, QFont.Bold))
        
        display_text = symbol[:2] if len(symbol) > 1 else symbol[:1]
        painter.drawText(pixmap.rect(), Qt.AlignCenter, display_text)
        painter.end()
        
        self.setPixmap(pixmap)
    
    def on_image_loaded(self, reply: QNetworkReply):
        """图像加载完成回调"""
        try:
            # 🔥 修复：检查对象是否已销毁
            if self.is_destroyed or not reply:
                return
            
            # 使用数值比较避免属性错误
            if reply.error() == 0:  # QNetworkReply.NoError = 0
                image_data = reply.readAll()
                if image_data.size() > 0:
                    pixmap = QPixmap()
                    
                    # 尝试加载图像，忽略颜色配置文件警告
                    if pixmap.loadFromData(image_data):
                        # 🔥 修复：再次检查对象是否还存在
                        if not self.is_destroyed:
                            scaled_pixmap = pixmap.scaled(32, 32, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                            self.setPixmap(scaled_pixmap)
                            cache_key = f"token_icon_{self.image_url}"
                            QPixmapCache.insert(cache_key, scaled_pixmap)
                            logger.debug(f"成功加载代币图像: {self.token_symbol}")
                        return  # 成功加载，直接返回
                    else:
                        logger.debug(f"图像数据无法解析: {self.token_symbol}")
                else:
                    logger.debug(f"接收到空图像数据: {self.token_symbol}")
            else:
                # 简化错误处理，所有网络错误都用debug级别
                error_code, error_string = self._get_safe_error_info(reply)
                logger.debug(f"网络错误 ({error_code}): {self.token_symbol} - {error_string}")
            
            # 所有失败情况都执行重试逻辑
            if not self.is_destroyed:
                self._handle_load_failure()
            
        except Exception as e:
            logger.debug(f"图像处理异常: {self.token_symbol} - {e}")
            if not self.is_destroyed:
                self._handle_load_failure()
        finally:
            # 🔥 修复：安全清理网络回复
            if reply:
                reply.deleteLater()
            # 清理当前请求引用
            if self.network_reply == reply:
                self.network_reply = None
    
    def _handle_load_failure(self):
        """处理加载失败的情况"""
        if self.retry_count < self.max_retries:
            # 延迟重试，避免立即重试造成更多失败
            retry_delay = (self.retry_count + 1) * 2000  # 2秒, 4秒
            self.retry_timer.start(retry_delay)
        else:
            # 所有重试都失败，显示占位图
            self.set_placeholder_with_symbol(self.token_symbol)
    
    def enterEvent(self, event):
        """鼠标进入事件 - 显示大图"""
        if self.image_url: # Removed check for self.large_image_label to allow creation on hover
            self.show_large_image()
        super().enterEvent(event)
    
    def leaveEvent(self, event):
        """鼠标离开事件 - 隐藏大图"""
        if self.large_image_label:
            self.large_image_label.hide()
            self.large_image_label.deleteLater()
            self.large_image_label = None
        super().leaveEvent(event)
    
    def show_large_image(self):
        """显示大图悬浮窗"""
        try:
            if not self.image_url or self.is_destroyed:
                return

            # Create the label for large image if it doesn't exist
            if not self.large_image_label:
                self.large_image_label = QLabel(self.window()) # Use parent window for the tooltip
                self.large_image_label.setWindowFlags(Qt.ToolTip | Qt.FramelessWindowHint)
                self.large_image_label.setAttribute(Qt.WA_TranslucentBackground)
                self.large_image_label.setFixedSize(128,128) # Initial size
                self.large_image_label.setAlignment(Qt.AlignCenter)
                self.large_image_label.setStyleSheet("background-color: transparent; border: 1px solid #3498db; border-radius: 5px;")

            large_cache_key = f"token_large_icon_{self.image_url}"
            cached_pixmap = QPixmapCache.find(large_cache_key)
            
            if cached_pixmap and not cached_pixmap.isNull():
                # Ensure large_image_label exists before setting pixmap
                if self.large_image_label and not self.is_destroyed:
                    scaled_pixmap = cached_pixmap.scaled(128, 128, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                    self.large_image_label.setPixmap(scaled_pixmap)
                    self.large_image_label.resize(scaled_pixmap.size()) # Adjust size after pixmap is set
                    cursor_pos = QCursor.pos()
                    self.large_image_label.move(cursor_pos.x() + 20, cursor_pos.y() - self.large_image_label.height() / 2)
                    self.large_image_label.show()
                return

            # 🔥 修复：使用共享网络管理器，避免创建过多实例
            if not hasattr(self, 'large_image_reply') or not self.large_image_reply:
                request = QNetworkRequest(QUrl(self.image_url))
                request.setHeader(QNetworkRequest.UserAgentHeader, "TrendTrader/1.0")
                request.setTransferTimeout(10000)  # 10秒超时
                
                self.large_image_reply = self.network_manager.get(request)
                self.large_image_reply.finished.connect(lambda: self.on_large_image_loaded(self.large_image_reply))
            
        except Exception as e:
            logger.error(f"显示大图失败: {e}")
    
    def on_large_image_loaded(self, reply: QNetworkReply):
        """大图加载完成回调"""
        try:
            # 🔥 修复：检查对象状态
            if self.is_destroyed or not self.large_image_label or not reply:
                return

            if reply.error() == 0:  # QNetworkReply.NoError = 0
                image_data = reply.readAll()
                if image_data.size() > 0:
                    pixmap = QPixmap()
                    
                    if pixmap.loadFromData(image_data):
                        if not self.is_destroyed and self.large_image_label:
                            scaled_pixmap = pixmap.scaled(128, 128, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                            self.large_image_label.setPixmap(scaled_pixmap)
                            self.large_image_label.resize(scaled_pixmap.size()) # Adjust size
                            
                            large_cache_key = f"token_large_icon_{self.image_url}"
                            QPixmapCache.insert(large_cache_key, scaled_pixmap)
                            
                            cursor_pos = QCursor.pos()
                            # Adjust y-position to center the tooltip vertically relative to cursor
                            self.large_image_label.move(cursor_pos.x() + 20, cursor_pos.y() - self.large_image_label.height() / 2)
                            self.large_image_label.show()
                    else:
                        # 图像数据无法解析，隐藏大图
                        if self.large_image_label:
                            self.large_image_label.hide()
                else:
                    # 空数据，隐藏大图
                    if self.large_image_label:
                        self.large_image_label.hide()
            else:
                # 网络错误，隐藏大图（不记录日志，避免污染）
                if self.large_image_label:
                    self.large_image_label.hide()

        except Exception as e:
            logger.error(f"处理大图失败: {e}")
            if self.large_image_label:
                self.large_image_label.hide()
        finally:
            # 🔥 修复：安全清理资源
            if reply:
                reply.deleteLater()
            # 清理大图请求引用
            if hasattr(self, 'large_image_reply') and self.large_image_reply == reply:
                self.large_image_reply = None
    
    def cleanup_resources(self):
        """清理资源，防止内存泄露"""
        try:
            self.is_destroyed = True
            
            # 停止定时器
            if hasattr(self, 'retry_timer'):
                self.retry_timer.stop()
            
            # 取消网络请求
            if hasattr(self, 'network_reply') and self.network_reply:
                if not self.network_reply.isFinished():
                    self.network_reply.abort()
                self.network_reply.deleteLater()
                self.network_reply = None
            
            if hasattr(self, 'large_image_reply') and self.large_image_reply:
                if not self.large_image_reply.isFinished():
                    self.large_image_reply.abort()
                self.large_image_reply.deleteLater()
                self.large_image_reply = None
            
            # 清理大图标签
            if self.large_image_label:
                self.large_image_label.hide()
                self.large_image_label.deleteLater()
                self.large_image_label = None
                
        except Exception as e:
            logger.error(f"TokenImageLabel清理资源失败: {e}")
    
    def __del__(self):
        """析构函数"""
        try:
            self.cleanup_resources()
        except:
            pass


class TrendingWindow(QDialog):
    def __init__(self, parent=None, api_service=None):
        super().__init__(parent)
        self.setWindowTitle("趋势热门榜单")
        self.setGeometry(200, 200, 1200, 700) # 增大窗口以容纳新控件

        # Use provided APIService or create a new one
        self.api_service = api_service if api_service else APIService()
        self.trending_coins_data = [] # To store fetched data
        
        # 🔥 多线程OHLCV下载管理器
        self.multi_thread_manager = MultiThreadOHLCVManager(
            api_service=self.api_service,
            max_workers=5,  # 限制并发数避免API限制
            parent=self
        )
        
        # 🔥 新增：管理独立图表窗口
        self.chart_windows = []  # 存储打开的图表窗口引用

        self._init_ui()
        self._setup_timers()
        self._connect_api_signals()
        
        # Don't automatically refresh on init - let caller decide when to start
        logger.info("TrendingWindow: Initialized successfully.")

    def _connect_api_signals(self):
        logger.info("TrendingWindow: Connecting signals...")
        # API信号
        self.api_service.trending_tokens_ready.connect(self.on_trending_tokens_received)
        self.api_service.trending_tokens_error.connect(self.on_trending_tokens_error)
        
        # 多线程管理器信号
        self.multi_thread_manager.strategy_signal_generated.connect(self.on_strategy_signal_received)
        self.multi_thread_manager.download_completed.connect(self.on_single_download_completed)
        self.multi_thread_manager.download_failed.connect(self.on_single_download_failed)
        self.multi_thread_manager.all_downloads_completed.connect(self.on_batch_analysis_completed)
        
        logger.info("TrendingWindow: All signals connected successfully.")

    @pyqtSlot(list)
    def on_trending_tokens_received(self, tokens: List[Dict]):
        logger.info(f"TrendingWindow: Received {len(tokens)} trending tokens from API.")
        
        # 更新表格显示
        self.update_table(tokens)
        
        # 🔥 如果启用了自动策略分析，立即开始分析新榜单
        if self.strategy_enabled_checkbox.isChecked():
            logger.info("TrendingWindow: 榜单更新完成，立即开始自动策略分析...")
            self.start_batch_strategy_analysis(is_auto=True)

    @pyqtSlot(str)
    def on_trending_tokens_error(self, error_message: str):
        logger.error(f"TrendingWindow: Error fetching trending tokens: {error_message}")
        print(f"[DEBUG] TrendingWindow: Error - {error_message}")
        # Optionally, display an error message to the user in the UI
        # For example, update a status bar or show a QMessageBox
        # self.trend_table.setRowCount(0) # Clear table on error or show a message in table
        # For now, just log the error.

    def _init_ui(self):
        layout = QVBoxLayout(self)

        # 🔥 控制面板
        control_frame = QFrame()
        control_layout = QHBoxLayout(control_frame)
        
        # 策略分析开关
        self.strategy_enabled_checkbox = QCheckBox("启用自动策略分析 (30秒周期)")
        self.strategy_enabled_checkbox.setChecked(True)  # 默认启用
        self.strategy_enabled_checkbox.stateChanged.connect(self.on_strategy_enabled_changed)
        control_layout.addWidget(self.strategy_enabled_checkbox)
        
        # 手动分析按钮
        self.manual_analysis_button = QPushButton("立即分析")
        self.manual_analysis_button.clicked.connect(self.trigger_manual_analysis)
        control_layout.addWidget(self.manual_analysis_button)
        
        # 状态标签
        self.status_label = QLabel("状态: 就绪")
        control_layout.addWidget(self.status_label)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        control_layout.addWidget(self.progress_bar)
        
        control_layout.addStretch()
        layout.addWidget(control_frame)

        # Trend table
        self.trend_table = QTableWidget()
        self.trend_table.setColumnCount(11)
        self.trend_table.setHorizontalHeaderLabels([
            "图像", "代币", "网络", "5分钟", "市值", "24h交易量", "持有者", "讨论", "V/M", "策略", "更新时间"
        ])
        self.trend_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.trend_table.verticalHeader().setVisible(False)
        self.trend_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.trend_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.trend_table.setStyleSheet("""
            QTableWidget {
                background-color: #2c3e50;
                color: #ecf0f1;
                border: 1px solid #34495e;
                gridline-color: #34495e;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: #ecf0f1;
                padding: 4px;
                border: 1px solid #2c3e50;
            }
            QTableWidgetItem {
                padding: 5px;
            }
        """)
        
        # 🔥 添加双击事件监听器
        self.trend_table.cellDoubleClicked.connect(self.on_token_double_clicked)
        layout.addWidget(self.trend_table)

        self.setLayout(layout)
        logger.info("TrendingWindow: UI initialized.")

    def _setup_timers(self):
        # 趋势榜单刷新定时器 (包含自动策略分析)
        self.refresh_timer = QTimer(self)
        self.refresh_timer.timeout.connect(self.refresh_trending_list)
        self.refresh_timer.start(30 * 1000) # 30 seconds
        logger.info("TrendingWindow: Refresh timer started (30 seconds interval, includes auto analysis).")
        
        # 🔥 修复：添加内存清理定时器
        self.cleanup_timer = QTimer(self)
        self.cleanup_timer.timeout.connect(self.periodic_cleanup)
        self.cleanup_timer.start(300 * 1000)  # 5分钟清理一次
        logger.info("TrendingWindow: Cleanup timer started (5 minutes interval).")
        
        # 分析状态跟踪
        self.is_analysis_running = False
        self.analysis_start_time = None

    def refresh_trending_list(self):
        """刷新趋势榜单 (并触发自动策略分析)"""
        logger.info("TrendingWindow: Requesting trending list from APIService...")
        
        # 🔥 如果有分析正在进行，先强制停止
        if self.is_analysis_running:
            logger.info("TrendingWindow: 检测到分析正在进行，强制停止以便开始新周期...")
            self.force_stop_current_analysis()
        
        # 请求新的榜单数据
        self.api_service.get_trending_tokens_async()


    def trigger_manual_analysis(self):
        """手动触发策略分析"""
        try:
            # 强制停止当前分析（如果有）
            if self.is_analysis_running:
                self.force_stop_current_analysis()
            
            # 开始手动分析
            self.start_batch_strategy_analysis(is_auto=False)
            
        except Exception as e:
            logger.error(f"TrendingWindow: 手动策略分析失败: {e}")
    
    def force_stop_current_analysis(self):
        """强制停止当前正在进行的分析"""
        try:
            if self.is_analysis_running:
                logger.info("TrendingWindow: 强制停止当前策略分析...")
                
                # 强制停止多线程管理器的所有任务
                self.multi_thread_manager.force_stop_all_tasks()
                
                # 重置统计信息
                self.multi_thread_manager.reset_statistics()
                
                # 更新状态
                self.is_analysis_running = False
                self.analysis_start_time = None
                self.status_label.setText("状态: 分析已强制停止")
                self.progress_bar.setVisible(False)
                
                logger.info("TrendingWindow: 当前分析已强制停止")
                
        except Exception as e:
            logger.error(f"TrendingWindow: 强制停止分析时出错: {e}")
    
    def periodic_cleanup(self):
        """定期清理内存和资源"""
        try:
            logger.debug("TrendingWindow: 执行定期清理...")
            
            # 🔥 清理图像缓存，防止内存积累
            cache_limit = 100  # 限制缓存项目数
            QPixmapCache.setCacheLimit(10240)  # 设置缓存大小限制 (10MB)
            
            # 🔥 清理已关闭的图表窗口
            self.clear_closed_chart_windows()
            
            # 🔥 清理表格中已销毁的TokenImageLabel
            self.cleanup_table_widgets()
            
            # 🔥 强制垃圾回收
            import gc
            collected = gc.collect()
            logger.debug(f"TrendingWindow: 定期清理完成，回收 {collected} 个对象")
            
        except Exception as e:
            logger.error(f"TrendingWindow: 定期清理失败: {e}")
    
    def cleanup_table_widgets(self):
        """清理表格中的小部件"""
        try:
            row_count = self.trend_table.rowCount()
            for row in range(row_count):
                # 获取第0列的TokenImageLabel
                widget = self.trend_table.cellWidget(row, 0)
                if isinstance(widget, TokenImageLabel):
                    # 检查是否需要清理
                    if hasattr(widget, 'is_destroyed') and not widget.is_destroyed:
                        # 小部件还在使用，跳过
                        continue
                    else:
                        # 清理资源
                        widget.cleanup_resources()
                        
        except Exception as e:
            logger.error(f"TrendingWindow: 清理表格小部件失败: {e}")
    
    def on_strategy_enabled_changed(self, state):
        """策略分析开关状态改变"""
        if state == 2:  # 启用
            logger.info("TrendingWindow: 自动策略分析已启用")
            self.status_label.setText("状态: 自动分析已启用 (等待下个周期)")
        else:  # 禁用
            logger.info("TrendingWindow: 自动策略分析已禁用")
            if self.is_analysis_running:
                self.force_stop_current_analysis()
            self.status_label.setText("状态: 自动分析已禁用")

    def start_batch_strategy_analysis(self, is_auto: bool = False):
        """开始批量策略分析"""
        if not self.trending_coins_data:
            status_msg = "状态: 没有数据进行分析"
            self.status_label.setText(status_msg)
            logger.warning("TrendingWindow: No trending data available for analysis")
            return
        
        # 限制分析数量，避免API过载
        max_analysis_count = 30  # 降低并发数
        tokens_to_analyze = self.trending_coins_data[:max_analysis_count]
        
        analysis_type = "自动" if is_auto else "手动"
        logger.info(f"TrendingWindow: 开始{analysis_type}批量策略分析 ({len(tokens_to_analyze)} 个代币)")
        
        # 更新分析状态
        self.is_analysis_running = True
        self.analysis_start_time = time.time()
        
        # 更新状态UI
        self.status_label.setText(f"状态: {analysis_type}分析中... (0/{len(tokens_to_analyze)})")
        self.progress_bar.setVisible(True)
        self.progress_bar.setMaximum(len(tokens_to_analyze))
        self.progress_bar.setValue(0)
        
        # 提交批量下载任务到多线程管理器
        # 🔥 使用配置中的默认策略，与其他组件保持一致
        from config import PORTFOLIO_CONFIG
        default_strategy = PORTFOLIO_CONFIG.get("default_strategy", "VWAP 交叉策略")
        
        submitted_count = self.multi_thread_manager.submit_batch_download(
            token_list=tokens_to_analyze,
            strategy_name=default_strategy,
            timeframe='1m',  # 使用1分钟数据
            days=1
        )
        
        if submitted_count > 0:
            logger.info(f"TrendingWindow: 成功提交 {submitted_count} 个{analysis_type}分析任务")
        else:
            self.status_label.setText(f"状态: {analysis_type}分析提交失败")
            self.progress_bar.setVisible(False)
            self.is_analysis_running = False
            self.analysis_start_time = None
    
    @pyqtSlot(str, str, str, float, int, str)
    def on_strategy_signal_received(self, token_address: str, symbol: str, signal_type: str, 
                                  price: float, timestamp: int, strategy_name: str):
        """处理策略信号"""
        try:
            logger.info(f"🔥 TEST: TrendingWindow 接收到策略信号 - {symbol}: {signal_type}")
            
            # 🔥 支持更多信号类型的中文显示
            signal_type_mapping = {
                'buy': '买入',
                'sell': '卖出', 
                'hold': '持有',
                'wait': '观察'
            }
            signal_type_zh = signal_type_mapping.get(signal_type, signal_type)
            
            time_str = datetime.fromtimestamp(timestamp).strftime("%H:%M:%S")
            
            logger.info(f"🔥 TEST: TrendingWindow 处理策略信号 - {symbol} {signal_type_zh} @ ${price:.6f} ({time_str})")
            
            # 更新表格中的策略列
            logger.info(f"🔥 TEST: 准备更新表格策略列 - {symbol}: {signal_type_zh}")
            self.update_strategy_column(token_address, signal_type_zh)
            logger.info(f"🔥 TEST: 完成更新表格策略列 - {symbol}: {signal_type_zh}")
            
        except Exception as e:
            logger.error(f"🔥 TEST: TrendingWindow 处理策略信号失败: {e}", exc_info=True)
    
    @pyqtSlot(str, str, list, dict)
    def on_single_download_completed(self, token_address: str, symbol: str, ohlcv_data: list, token_data: dict):
        """单个下载完成"""
        try:
            # 更新进度条和状态标签
            if self.progress_bar.isVisible():
                current_value = self.progress_bar.value()
                new_value = current_value + 1
                self.progress_bar.setValue(new_value)
                
                # 🔥 同时更新状态标签显示当前进度
                max_value = self.progress_bar.maximum()
                analysis_type = "自动" if self.strategy_enabled_checkbox.isChecked() else "手动"
                self.status_label.setText(f"状态: {analysis_type}分析中... ({new_value}/{max_value})")
                
            logger.debug(f"TrendingWindow: {symbol} 下载完成，{len(ohlcv_data)} 条数据")
            
            # 🔥 检查是否有等待自动打开图表的代币
            if hasattr(self, 'pending_chart_opens') and token_address in self.pending_chart_opens:
                # 从等待列表中移除
                self.pending_chart_opens.remove(token_address)
                logger.info(f"TrendingWindow: 检测到 {symbol} 在等待自动打开图表，立即打开")
                
                # 自动打开图表窗口
                try:
                    # 从ChartWidget获取最新数据
                    chart_widget = self.multi_thread_manager.get_chart_widget(token_address)
                    if chart_widget and hasattr(chart_widget, 'ohlcv_data') and chart_widget.ohlcv_data:
                        widget_ohlcv_data = chart_widget.ohlcv_data
                        logger.info(f"TrendingWindow: 从ChartWidget获取到 {len(widget_ohlcv_data)} 条数据用于自动打开图表")
                        
                        # 使用ChartWidget的数据打开图表
                        self.open_chart_with_cached_data(token_data, widget_ohlcv_data)
                        # 🔥 只有在非分析状态时才更新状态标签
                        if not self.is_analysis_running:
                            self.status_label.setText(f"状态: 已自动打开 {symbol} K线图表 ({len(widget_ohlcv_data)} 条数据)")
                    else:
                        # 如果ChartWidget没有数据，使用下载回调提供的数据
                        logger.info(f"TrendingWindow: ChartWidget暂无数据，使用下载回调的 {len(ohlcv_data)} 条数据")
                        self.open_chart_with_cached_data(token_data, ohlcv_data)
                        # 🔥 只有在非分析状态时才更新状态标签
                        if not self.is_analysis_running:
                            self.status_label.setText(f"状态: 已自动打开 {symbol} K线图表 ({len(ohlcv_data)} 条数据)")
                
                except Exception as chart_error:
                    logger.error(f"TrendingWindow: 自动打开 {symbol} 图表失败: {chart_error}")
                    # 🔥 只有在非分析状态时才更新状态标签
                    if not self.is_analysis_running:
                        self.status_label.setText(f"状态: 自动打开 {symbol} 图表失败")
                
        except Exception as e:
            logger.error(f"TrendingWindow: 处理下载完成事件失败: {e}")
    
    @pyqtSlot(str, str, str)
    def on_single_download_failed(self, token_address: str, symbol: str, error_message: str):
        """单个下载失败"""
        try:
            # 更新进度条和状态标签
            if self.progress_bar.isVisible():
                current_value = self.progress_bar.value()
                new_value = current_value + 1
                self.progress_bar.setValue(new_value)
                
                # 🔥 同时更新状态标签显示当前进度
                max_value = self.progress_bar.maximum()
                analysis_type = "自动" if self.strategy_enabled_checkbox.isChecked() else "手动"
                self.status_label.setText(f"状态: {analysis_type}分析中... ({new_value}/{max_value})")
                
            logger.debug(f"TrendingWindow: {symbol} 下载失败: {error_message}")
        except Exception as e:
            logger.error(f"TrendingWindow: 处理下载失败事件失败: {e}")
    
    @pyqtSlot(int, int)
    def on_batch_analysis_completed(self, success_count: int, total_count: int):
        """批量分析完成"""
        try:
            # 计算分析耗时
            analysis_duration = ""
            if self.analysis_start_time:
                duration = time.time() - self.analysis_start_time
                analysis_duration = f" ({duration:.1f}秒)"
            
            logger.info(f"TrendingWindow: 批量分析完成 - 成功: {success_count}/{total_count}{analysis_duration}")
            
            completion_rate = (success_count / total_count * 100) if total_count > 0 else 0
            
            # 根据成功率设置不同的状态消息
            analysis_status = "自动分析" if self.strategy_enabled_checkbox.isChecked() else "手动分析"
            if completion_rate >= 80:
                status_msg = f"状态: {analysis_status}完成 ✅ (成功率: {completion_rate:.1f}%{analysis_duration})"
            elif completion_rate >= 50:
                status_msg = f"状态: {analysis_status}完成 ⚠️ (成功率: {completion_rate:.1f}%{analysis_duration})"
            else:
                status_msg = f"状态: {analysis_status}完成 ❌ (成功率: {completion_rate:.1f}%{analysis_duration})"
                
            self.status_label.setText(status_msg)
            self.progress_bar.setVisible(False)
            
            # 重置分析状态
            self.is_analysis_running = False
            self.analysis_start_time = None
            
            # 提示用户如果成功率较低
            if completion_rate < 50:
                logger.warning(f"TrendingWindow: 分析成功率较低 ({completion_rate:.1f}%)，可能是API限制或网络问题")
                
        except Exception as e:
            logger.error(f"TrendingWindow: 处理分析完成事件时出错: {e}")
            # 确保状态重置
            self.is_analysis_running = False
            self.analysis_start_time = None
            self.progress_bar.setVisible(False)
    
    def update_strategy_column(self, token_address: str, signal: str):
        """更新表格中的策略列"""
        try:
            logger.info(f"🔥 TEST: update_strategy_column 被调用 - token_address: {token_address}, signal: {signal}")
            logger.info(f"🔥 TEST: 表格行数: {self.trend_table.rowCount()}, 数据条数: {len(self.trending_coins_data)}")
            
            # 在表格中查找对应的代币行
            found_match = False
            for row in range(self.trend_table.rowCount()):
                # 从trending_coins_data中获取对应行的代币地址
                if row < len(self.trending_coins_data):
                    coin_data = self.trending_coins_data[row]
                    coin_address = coin_data.get('tokenAddress') or coin_data.get('address')
                    coin_symbol = coin_data.get('symbol', 'Unknown')
                    
                    logger.debug(f"🔥 TEST: 检查行 {row} - {coin_symbol}: {coin_address}")
                    
                    if coin_address == token_address:
                        found_match = True
                        logger.info(f"🔥 TEST: 找到匹配的代币 - 行 {row}, {coin_symbol}")
                        
                        strategy_item = QTableWidgetItem(signal)
                        # 🔥 为不同策略信号设置对应颜色
                        if signal == "买入":
                            strategy_item.setForeground(QColor("#27ae60"))  # 绿色
                        elif signal == "卖出":
                            strategy_item.setForeground(QColor("#e74c3c"))  # 红色
                        elif signal == "持有":
                            strategy_item.setForeground(QColor("#f39c12"))  # 橙色
                        elif signal == "观察":
                            strategy_item.setForeground(QColor("#95a5a6"))  # 灰色
                        else:
                            strategy_item.setForeground(QColor("#ecf0f1"))  # 默认白色
                        
                        self.trend_table.setItem(row, 9, strategy_item)  # 策略列是第9列
                        logger.info(f"🔥 TEST: 成功更新 {coin_symbol} 策略列 -> {signal}")
                        
                        # 🔥 测试：验证更新是否成功
                        updated_item = self.trend_table.item(row, 9)
                        if updated_item:
                            logger.info(f"🔥 TEST: 验证更新成功 - {coin_symbol} 策略列显示: {updated_item.text()}")
                        else:
                            logger.error(f"🔥 TEST: 验证失败 - {coin_symbol} 策略列为空")
                        
                        break
            
            if not found_match:
                logger.warning(f"🔥 TEST: 未找到匹配的代币地址: {token_address}")
                logger.warning(f"🔥 TEST: 可用的代币地址列表:")
                for i, coin_data in enumerate(self.trending_coins_data[:5]):  # 只显示前5个
                    addr = coin_data.get('tokenAddress') or coin_data.get('address')
                    symbol = coin_data.get('symbol', 'Unknown')
                    logger.warning(f"🔥 TEST:   [{i}] {symbol}: {addr}")
                        
        except Exception as e:
            logger.error(f"🔥 TEST: 更新策略列失败: {e}", exc_info=True)
    
    def get_strategy_analysis_statistics(self) -> Dict:
        """获取策略分析统计信息"""
        return self.multi_thread_manager.get_statistics()
    
    def on_token_double_clicked(self, row: int, column: int):
        """处理代币双击事件 - 打开K线图表"""
        try:
            if row >= len(self.trending_coins_data):
                logger.warning(f"TrendingWindow: 双击行索引超出范围: {row}")
                self.status_label.setText("状态: 无效的代币选择")
                return
            
            # 获取代币数据
            token_data = self.trending_coins_data[row]
            token_address = token_data.get('tokenAddress') or token_data.get('address')
            symbol = token_data.get('symbol', 'Unknown')
            name = token_data.get('name', symbol)
            
            if not token_address:
                logger.warning(f"TrendingWindow: 代币数据缺少地址信息: {token_data}")
                self.status_label.setText(f"状态: {symbol} 代币地址信息缺失")
                return
            
            logger.info(f"TrendingWindow: 双击打开图表 - {symbol} ({token_address[:8]}...)")
            self.status_label.setText(f"状态: 正在打开 {symbol} K线图表...")
            
            # 🔥 从缓存获取OHLCV数据，优先从HeadlessChartWidget获取
            cached_ohlcv_data = self.get_cached_ohlcv_data(token_address)
            
            if cached_ohlcv_data:
                # 使用缓存数据打开图表
                self.open_chart_with_cached_data(token_data, cached_ohlcv_data)
                logger.info(f"TrendingWindow: 成功为 {symbol} 打开图表，数据来源: 缓存")
            else:
                # 没有缓存数据，提示用户并给出建议
                self.status_label.setText(f"状态: {symbol} 暂无缓存数据，请等待分析完成后再试")
                logger.info(f"TrendingWindow: {symbol} 暂无缓存数据。建议：1) 等待策略分析完成；2) 手动触发分析")
                
                # 🔥 可选：提示用户是否要触发手动分析
                # 这里可以添加一个消息框询问用户是否要开始分析
                from PyQt5.QtWidgets import QMessageBox
                reply = QMessageBox.question(
                    self, 
                    '数据不可用', 
                    f'{symbol} 暂无K线数据。\n\n是否现在开始获取数据？\n\n注意：这可能需要几秒钟时间。',
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.Yes
                )
                
                if reply == QMessageBox.Yes:
                    # 触发单个代币的数据获取
                    self.start_single_token_analysis(token_data)
                
        except Exception as e:
            logger.error(f"TrendingWindow: 处理代币双击事件失败: {e}")
            self.status_label.setText(f"状态: 打开图表失败 - {str(e)[:50]}")
    
    def start_single_token_analysis(self, token_data: Dict):
        """为单个代币启动数据分析"""
        try:
            symbol = token_data.get('symbol', 'Unknown')
            token_address = token_data.get('tokenAddress') or token_data.get('address')
            
            self.status_label.setText(f"状态: 正在为 {symbol} 获取K线数据...")
            
            # 🔥 记录等待自动打开图表的代币
            if not hasattr(self, 'pending_chart_opens'):
                self.pending_chart_opens = set()
            self.pending_chart_opens.add(token_address)
            
            # 使用多线程管理器获取单个代币的数据
            from multi_thread_ohlcv_manager import OHLCVDownloadTask
            
            # 🔥 使用配置中的默认策略，与其他组件保持一致
            from config import PORTFOLIO_CONFIG
            default_strategy = PORTFOLIO_CONFIG.get("default_strategy", "VWAP 交叉策略")

            # 提交批量下载任务到多线程管理器

            task = OHLCVDownloadTask(
                token_data=token_data,
                strategy_name=default_strategy,  # 🔥 修复：单个代币分析时不显示策略信号，避免K线图混乱
                timeframe='1m',
                days=1
            )
            
            # 添加到多线程管理器的任务队列
            self.multi_thread_manager.submit_task(task)
            
            logger.info(f"TrendingWindow: 已为 {symbol} 提交数据获取任务")
            
        except Exception as e:
            logger.error(f"TrendingWindow: 启动单个代币分析失败: {e}")
            self.status_label.setText(f"状态: 启动数据获取失败")
    
    def get_cached_ohlcv_data(self, token_address: str) -> Optional[List]:
        """从缓存获取OHLCV数据"""
        try:
            # 🔥🔥 优先从MultiThreadOHLCVManager的chart_widgets获取数据
            chart_widget = self.multi_thread_manager.get_chart_widget(token_address)
            if chart_widget:
                # 对于ChartWidget，直接获取ohlcv_data属性
                if hasattr(chart_widget, 'ohlcv_data') and chart_widget.ohlcv_data:
                    widget_data = chart_widget.ohlcv_data
                    logger.info(f"TrendingWindow: 从ChartWidget获取到 {len(widget_data)} 条数据")
                    return widget_data
                else:
                    logger.debug(f"TrendingWindow: ChartWidget存在但没有OHLCV数据: {token_address}")
            else:
                logger.debug(f"TrendingWindow: 未找到对应的ChartWidget: {token_address}")
            
            # 🔥 回退：尝试从API服务的内存缓存获取
            if hasattr(self.api_service, 'get_cached_ohlcv_data'):
                cached_data = self.api_service.get_cached_ohlcv_data(token_address, '1m')
                if cached_data:
                    logger.info(f"TrendingWindow: 从API服务缓存获取到 {len(cached_data)} 条数据")
                    return cached_data
            
            # 🔥 最后尝试从多线程管理器的已完成任务获取
            if token_address in self.multi_thread_manager.completed_tasks:
                completed_data = self.multi_thread_manager.completed_tasks[token_address]
                logger.info(f"TrendingWindow: 从多线程管理器已完成任务获取到 {len(completed_data)} 条数据")
                return completed_data
            
            logger.debug(f"TrendingWindow: 所有缓存源都没有找到数据: {token_address}")
            return None
            
        except Exception as e:
            logger.error(f"TrendingWindow: 获取缓存OHLCV数据失败: {e}")
            return None
    
    def open_chart_with_cached_data(self, token_data: Dict, ohlcv_data: List):
        """使用缓存数据打开图表窗口"""
        try:
            symbol = token_data.get('symbol', 'Unknown')
            name = token_data.get('name', symbol)
            token_address = token_data.get('tokenAddress') or token_data.get('address', 'Unknown')
            
            # 创建图表窗口
            chart_window = ChartWidget(
                api_service=self.api_service,
                parent=None  # 🔥 设置为None，让窗口独立存在
            )
            
            # 设置窗口标题和大小
            chart_window.setWindowTitle(f"K线图表 - {name} ({symbol}) | 地址: {token_address[:8]}...")
            chart_window.resize(1200, 800)
            

            # 🔥 使用配置中的默认策略，与其他组件保持一致
            from config import PORTFOLIO_CONFIG
            default_strategy = PORTFOLIO_CONFIG.get("default_strategy", "VWAP 交叉策略")

            # 🔥 确保token_data包含必要的字段
            enhanced_token_data = token_data.copy()
            enhanced_token_data.update({
                'source': 'trend',  # 标识数据来源
                'timeframe': '1m',  # 设置时间周期
                'strategy_name': default_strategy
            })
            
                        # 🔥 临时禁用自动刷新，避免set_token的refresh_data()与我们的缓存数据冲突
            chart_window.auto_refresh = False
            
            # 🔥 先设置代币信息
            chart_window.set_token(enhanced_token_data)
            
            # 🔥 使用QTimer延迟调用，避免与set_token内部的refresh_data()冲突
            from PyQt5.QtCore import QTimer
            def delayed_display_data():
                try:
                    chart_window.display_provided_ohlcv(
                        ohlcv_data=ohlcv_data,
                        timeframe='1m',
                        source_description=f"ChartWidget缓存 ({len(ohlcv_data)} 条)"
                    )
                    # 数据提供完成后，恢复自动刷新（如果用户需要的话）
                    chart_window.auto_refresh_check.setChecked(False)  # 让用户自己决定是否启用
                except Exception as e:
                    logger.error(f"TrendingWindow: 延迟显示数据失败: {e}")
            
            # 延迟100毫秒后提供数据，让set_token的异步操作先完成
            QTimer.singleShot(100, delayed_display_data)
            
            # 显示窗口
            chart_window.show()
            
            # 存储窗口引用
            self.chart_windows.append(chart_window)
            
            # 更新状态
            self.status_label.setText(f"状态: 已打开 {symbol} 图表 (缓存数据: {len(ohlcv_data)} 条)")
            
            logger.info(f"TrendingWindow: 成功打开 {symbol} 图表窗口，使用 {len(ohlcv_data)} 条缓存数据")
            
        except Exception as e:
            logger.error(f"TrendingWindow: 打开图表窗口失败: {e}")
            self.status_label.setText(f"状态: 打开图表失败 - {str(e)}")
    
    def clear_closed_chart_windows(self):
        """清理已关闭的图表窗口引用"""
        try:
            # 移除已关闭的窗口引用
            active_windows = []
            for window in self.chart_windows:
                if window and window.isVisible():
                    active_windows.append(window)
                elif window:
                    window.deleteLater()
            
            self.chart_windows = active_windows
            
        except Exception as e:
            logger.error(f"TrendingWindow: 清理图表窗口引用失败: {e}")

    def update_table(self, data: List[Dict]):
        """
        Updates the trend table with new data.
        """
        logger.info(f"TrendingWindow: Updating table with {len(data)} tokens...")
        print(f"[DEBUG] TrendingWindow: update_table called with {len(data)} tokens")
        
        # 🔥 修复：更新表格前先清理旧的小部件
        self.cleanup_old_table_widgets()
        
        self.trend_table.setRowCount(0) 
        if not data:
            logger.warning("TrendingWindow: No data to display in table.")
            return

        self.trend_table.setRowCount(len(data))
        # Fixed column width for the image column
        self.trend_table.setColumnWidth(0, 40) 

        for row, coin_info in enumerate(data):
            try:
                # Image Column using TokenImageLabel
                image_label = TokenImageLabel(self.trend_table) # Parent to table for proper cleanup
                
                # Try to get image URL from different possible fields
                image_url = coin_info.get("image_url", "") or coin_info.get("tokenImage", "") or coin_info.get("logo_uri", "")
                
                # Get symbol for display, handle missing symbol gracefully
                symbol_for_label = coin_info.get("symbol", "").strip()
                if not symbol_for_label:
                    # Use name as fallback
                    symbol_for_label = coin_info.get("name", "")[:10]  # Truncate long names
                    if not symbol_for_label:
                        # Use first part of address as last resort
                        token_addr = coin_info.get("tokenAddress") or coin_info.get("address", "")
                        symbol_for_label = token_addr[:8] if token_addr else "Unknown"
                
                image_label.load_token_image(image_url, symbol_for_label)
                self.trend_table.setCellWidget(row, 0, image_label)
                
                # Name column - use name or symbol as fallback
                name_display = coin_info.get("name", "") or symbol_for_label
                self.trend_table.setItem(row, 1, QTableWidgetItem(name_display))
                
                # Network column
                network_display = coin_info.get("network", "").title() or "Unknown"
                self.trend_table.setItem(row, 2, QTableWidgetItem(network_display))
                
                # Price change columns with actual data
                price_change_5m = coin_info.get("priceChange5m", 0)
                if isinstance(price_change_5m, (int, float)):
                    change_text = f"{price_change_5m:.2f}%"
                    change_item = QTableWidgetItem(change_text)
                    # Color code: green for positive, red for negative
                    if price_change_5m > 0:
                        change_item.setForeground(QColor("#27ae60"))  # Green
                    elif price_change_5m < 0:
                        change_item.setForeground(QColor("#e74c3c"))  # Red
                    self.trend_table.setItem(row, 3, change_item)
                else:
                    self.trend_table.setItem(row, 3, QTableWidgetItem("N/A"))
                
                # Market cap
                market_cap = coin_info.get("marketCap", 0)
                if isinstance(market_cap, (int, float)) and market_cap > 0:
                    if market_cap >= 1000000:
                        cap_text = f"${market_cap/1000000:.2f}M"
                    elif market_cap >= 1000:
                        cap_text = f"${market_cap/1000:.2f}K"
                    else:
                        cap_text = f"${market_cap:.2f}"
                    self.trend_table.setItem(row, 4, QTableWidgetItem(cap_text))
                else:
                    self.trend_table.setItem(row, 4, QTableWidgetItem("N/A"))
                
                # 24h Volume
                volume_24h = coin_info.get("volume24h", 0)
                if isinstance(volume_24h, (int, float)) and volume_24h > 0:
                    if volume_24h >= 1000000:
                        vol_text = f"${volume_24h/1000000:.2f}M"
                    elif volume_24h >= 1000:
                        vol_text = f"${volume_24h/1000:.2f}K"
                    else:
                        vol_text = f"${volume_24h:.2f}"
                    self.trend_table.setItem(row, 5, QTableWidgetItem(vol_text))
                else:
                    self.trend_table.setItem(row, 5, QTableWidgetItem("N/A"))
                
                # Holders
                holders = coin_info.get("holders", 0)
                if isinstance(holders, (int, float)) and holders > 0:
                    self.trend_table.setItem(row, 6, QTableWidgetItem(str(int(holders))))
                else:
                    self.trend_table.setItem(row, 6, QTableWidgetItem("N/A"))
                
                # Tweet count
                tweet_count = coin_info.get("tweetCount", 0) or coin_info.get("totalTweets", 0)
                if isinstance(tweet_count, (int, float)) and tweet_count > 0:
                    self.trend_table.setItem(row, 7, QTableWidgetItem(str(int(tweet_count))))
                else:
                    self.trend_table.setItem(row, 7, QTableWidgetItem("N/A"))
                
                # V/M Ratio
                vm_ratio = coin_info.get("vmRatio", 0)
                if isinstance(vm_ratio, (int, float)):
                    self.trend_table.setItem(row, 8, QTableWidgetItem(f"{vm_ratio:.2f}"))
                else:
                    self.trend_table.setItem(row, 8, QTableWidgetItem("N/A"))
                
                # Strategy column - placeholder
                self.trend_table.setItem(row, 9, QTableWidgetItem("N/A"))
                
                # Update time
                self.trend_table.setItem(row, 10, QTableWidgetItem(datetime.now().strftime("%H:%M:%S")))
                
                if row < 3:  # Debug: log first few rows
                    logger.info(f"TrendingWindow: Row {row} - Name: {name_display}, Symbol: {symbol_for_label}, Image: {bool(image_url)}")
            except Exception as e:
                logger.error(f"TrendingWindow: Error updating table row {row}: {e}")
        
        self.trending_coins_data = data
        logger.info(f"TrendingWindow: Table updated successfully with {len(data)} rows.")
        print(f"[DEBUG] TrendingWindow: Table updated, rows count: {self.trend_table.rowCount()}")
    
    def cleanup_old_table_widgets(self):
        """清理表格中的旧小部件"""
        try:
            row_count = self.trend_table.rowCount()
            for row in range(row_count):
                # 清理第0列的TokenImageLabel
                widget = self.trend_table.cellWidget(row, 0)
                if isinstance(widget, TokenImageLabel):
                    widget.cleanup_resources()
                    
        except Exception as e:
            logger.error(f"TrendingWindow: 清理旧表格小部件失败: {e}")

    def closeEvent(self, event):
        logger.info("TrendingWindow is closing. Cleaning up resources...")
        
        # 🔥 修复：停止所有定时器
        if hasattr(self, 'refresh_timer') and self.refresh_timer:
            self.refresh_timer.stop()
        
        if hasattr(self, 'cleanup_timer') and self.cleanup_timer:
            self.cleanup_timer.stop()
        
        # 强制停止当前分析
        if hasattr(self, 'is_analysis_running') and self.is_analysis_running:
            self.force_stop_current_analysis()
        
        # 🔥 修复：清理表格小部件资源
        self.cleanup_old_table_widgets()
        
        # 关闭多线程管理器
        if hasattr(self, 'multi_thread_manager'):
            self.multi_thread_manager.shutdown()
        
        # 清理图表窗口
        if hasattr(self, 'chart_windows'):
            for window in self.chart_windows:
                if window:
                    window.close()
        
        # 🔥 修复：清理共享网络管理器
        app = QApplication.instance()
        if app and hasattr(app, '_token_image_network_manager'):
            try:
                app._token_image_network_manager.deleteLater()
                delattr(app, '_token_image_network_manager')
            except:
                pass
        
        # 🔥 修复：强制垃圾回收
        import gc
        gc.collect()
        
        logger.info("TrendingWindow: 资源清理完成")
        super().closeEvent(event)

    def start_data_loading(self):
        """Start loading trending data - call this after initialization is complete"""
        logger.info("TrendingWindow: Starting initial data loading...")
        self.refresh_trending_list() # Initial refresh


# 🔥 OhlcvDownloaderThread 已被 MultiThreadOHLCVManager 取代


# Example usage (for testing this module directly)
if __name__ == '__main__':
    import sys
    from PyQt5.QtWidgets import QApplication

    # Configure logging for standalone testing
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(name)s - %(message)s')
    
    # 🔧 如果需要进一步减少日志输出，可以取消注释以下行：
    # logging.getLogger().setLevel(logging.WARNING)  # 只显示警告和错误
    # logging.getLogger("api_service").setLevel(logging.WARNING)  # 减少API服务日志
    # logging.getLogger("multi_thread_ohlcv_manager").setLevel(logging.WARNING)  # 减少多线程管理器日志
    
    app = QApplication(sys.argv)
    
    # Use real APIService
    from api_service import APIService
    main_api_service = APIService()
    
    # Create TrendingWindow with real APIService
    window = TrendingWindow(api_service=main_api_service)
    
    # Start loading data
    window.start_data_loading()
    
    window.show()
    sys.exit(app.exec_()) 