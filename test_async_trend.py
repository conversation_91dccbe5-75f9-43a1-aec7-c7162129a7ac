#!/usr/bin/env python
"""
异步趋势榜单测试脚本 - 专门测试异步获取趋势榜单的功能
"""

import sys
import time
import logging
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QPushButton, QTextEdit
from PyQt5.QtCore import QTimer

from api_service import APIService

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('async_trend_test')

class TestWindow(QMainWindow):
    """测试窗口类"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("异步趋势榜单测试")
        self.setGeometry(100, 100, 800, 600)
        
        # 创建中央组件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        
        # 创建状态标签
        self.status_label = QLabel("准备测试异步获取趋势榜单...")
        layout.addWidget(self.status_label)
        
        # 创建测试按钮
        self.test_button = QPushButton("开始测试")
        self.test_button.clicked.connect(self.start_test)
        layout.addWidget(self.test_button)
        
        # 创建日志显示区域
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        layout.addWidget(self.log_text)
        
        # 自动开始测试
        QTimer.singleShot(500, self.start_test)
    
    def log(self, message):
        """添加日志到显示区域"""
        self.log_text.append(message)
        logger.info(message)
    
    def start_test(self):
        """开始测试"""
        self.status_label.setText("正在测试异步获取趋势榜单...")
        self.test_button.setEnabled(False)
        self.log("开始测试异步获取趋势榜单...")
        
        # 定义成功回调函数
        def on_success(tokens):
            self.log(f"成功回调函数被调用，获取到 {len(tokens) if tokens else 0} 个代币")
            
            if tokens:
                self.status_label.setText(f"测试成功，获取到 {len(tokens)} 个代币")
                self.log(f"第一个代币: {tokens[0]['name']} ({tokens[0]['symbol']})")
                
                # 显示所有代币
                self.log("\n所有代币列表:")
                for i, token in enumerate(tokens[:10]):  # 只显示前10个
                    self.log(f"{i+1}. {token['name']} ({token['symbol']})")
                
                if len(tokens) > 10:
                    self.log(f"... 还有 {len(tokens) - 10} 个代币未显示")
            else:
                self.status_label.setText("测试失败，未获取到数据")
                self.log("未获取到任何代币数据")
            
            self.test_button.setEnabled(True)
        
        # 定义错误回调函数
        def on_error(error_msg):
            self.log(f"错误回调函数被调用: {error_msg}")
            self.status_label.setText(f"测试失败: {error_msg}")
            self.test_button.setEnabled(True)
        
        # 测试直接获取
        self.log("先测试直接获取趋势榜单...")
        try:
            tokens = APIService.get_trending_tokens()
            self.log(f"直接获取到 {len(tokens) if tokens else 0} 个代币")
            if tokens:
                self.log(f"直接获取的第一个代币: {tokens[0]['name']} ({tokens[0]['symbol']})")
        except Exception as e:
            self.log(f"直接获取趋势榜单失败: {str(e)}")
        
        # 测试异步获取
        self.log("\n开始测试异步获取趋势榜单...")
        try:
            task_id = APIService.get_trending_tokens_async(on_success, on_error)
            self.log(f"异步任务已启动，任务ID: {task_id}")
        except Exception as e:
            self.log(f"启动异步任务失败: {str(e)}")
            self.status_label.setText(f"启动异步任务失败: {str(e)}")
            self.test_button.setEnabled(True)

def main():
    """主函数"""
    app = QApplication(sys.argv)
    window = TestWindow()
    window.show()
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
