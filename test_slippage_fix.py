#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试滑点格式修复
验证滑点值在百分比和小数之间的正确转换
"""

def test_slippage_conversion():
    """测试滑点转换功能"""
    print("=== 滑点格式转换测试 ===\n")
    
    # 测试百分比到小数的转换
    test_cases = [
        1.0,   # 1%
        5.0,   # 5%
        10.0,  # 10%
        15.0,  # 15%
        20.0,  # 20%
        11.0,  # 之前导致错误的11%
    ]
    
    for percentage in test_cases:
        # 转换为小数格式（API期望的格式）
        decimal = percentage / 100.0
        decimal_str = f"{decimal:.3f}"
        
        # 验证反向转换
        back_to_percentage = decimal * 100
        
        print(f"📊 {percentage}% -> {decimal_str} (小数) -> {back_to_percentage}% (验证)")
        
        # 验证API格式是否正确
        if 0 <= decimal <= 1:
            print(f"   ✅ API格式正确：{decimal_str} 在 [0, 1] 范围内")
        else:
            print(f"   ❌ API格式错误：{decimal_str} 超出 [0, 1] 范围")
    
    print("\n=== 自适应滑点测试 ===\n")
    
    # 测试自适应滑点计算
    test_tokens = [
        {"value": 0.05, "percentage": 50, "expected_base": 15.0},
        {"value": 0.5, "percentage": 75, "expected_base": 10.0},
        {"value": 5.0, "percentage": 50, "expected_base": 5.0},
        {"value": 50.0, "percentage": 25, "expected_base": 3.0},
    ]
    
    for token in test_tokens:
        token_value = token["value"]
        percentage = token["percentage"]
        
        # 模拟自适应滑点计算逻辑
        if token_value < 0.1:
            base_slippage = 15.0
        elif token_value < 1.0:
            base_slippage = 10.0
        elif token_value < 10.0:
            base_slippage = 5.0
        else:
            base_slippage = 3.0
        
        # 根据卖出比例调整
        if percentage >= 75:
            slippage = base_slippage + 2.0
        elif percentage >= 50:
            slippage = base_slippage + 1.0
        else:
            slippage = base_slippage
        
        # 限制范围
        slippage = min(max(slippage, 1.0), 20.0)
        
        # 转换为API格式
        slippage_decimal = slippage / 100.0
        slippage_str = f"{slippage_decimal:.3f}"
        
        print(f"💰 代币价值: ${token_value:.2f}, 卖出: {percentage}%")
        print(f"   计算滑点: {slippage}% -> API格式: {slippage_str}")
        
        # 验证API格式
        if 0 <= slippage_decimal <= 1:
            print(f"   ✅ 格式正确")
        else:
            print(f"   ❌ 格式错误")
    
    print("\n=== 错误处理测试 ===\n")
    
    # 测试错误处理中的滑点显示
    api_slippage_values = ["0.050", "0.110", "0.200", "0.250"]
    
    for api_value in api_slippage_values:
        # 模拟从API滑点值转换为显示百分比
        decimal_value = float(api_value)
        percentage_display = decimal_value * 100
        
        print(f"🔄 API值: {api_value} -> 显示: {percentage_display}%")
        
        # 模拟建议更高的滑点
        suggested_slippage = min(percentage_display + 5.0, 25.0)
        print(f"   建议滑点: {suggested_slippage}%")

if __name__ == "__main__":
    test_slippage_conversion() 