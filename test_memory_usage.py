#!/usr/bin/env python3
"""
trending_window.py 内存使用情况测试脚本
监控程序运行时的内存使用情况，检测内存泄露
"""

import sys
import time
import psutil
import os
import threading
from datetime import datetime

def monitor_memory_usage(target_pid, duration_minutes=30):
    """监控目标进程的内存使用情况"""
    print(f"🔍 开始监控进程 {target_pid} 的内存使用 (持续 {duration_minutes} 分钟)")
    print("=" * 60)
    
    start_time = time.time()
    end_time = start_time + (duration_minutes * 60)
    
    try:
        process = psutil.Process(target_pid)
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        print(f"初始内存使用: {initial_memory:.2f} MB")
        print(f"监控开始时间: {datetime.now().strftime('%H:%M:%S')}")
        print("-" * 60)
        
        max_memory = initial_memory
        memory_history = []
        
        while time.time() < end_time:
            try:
                current_memory = process.memory_info().rss / 1024 / 1024  # MB
                memory_history.append(current_memory)
                
                if current_memory > max_memory:
                    max_memory = current_memory
                
                elapsed = (time.time() - start_time) / 60  # 分钟
                increase = current_memory - initial_memory
                
                # 每分钟输出一次
                if len(memory_history) % 12 == 0:  # 每5秒检查一次，12次 = 1分钟
                    print(f"[{elapsed:5.1f}分钟] 内存: {current_memory:7.2f} MB "
                          f"(增长: {increase:+6.2f} MB, 峰值: {max_memory:7.2f} MB)")
                
                # 内存泄露警告
                if increase > 200:  # 增长超过200MB
                    print(f"⚠️  内存使用量大幅增加: {increase:.2f} MB")
                
                time.sleep(5)  # 每5秒检查一次
                
            except psutil.NoSuchProcess:
                print("❌ 目标进程已结束")
                break
                
        # 总结报告
        print("\n" + "=" * 60)
        print("📊 内存使用总结:")
        print(f"   初始内存: {initial_memory:.2f} MB")
        print(f"   最终内存: {memory_history[-1]:.2f} MB")
        print(f"   峰值内存: {max_memory:.2f} MB")
        print(f"   总增长:   {memory_history[-1] - initial_memory:+.2f} MB")
        print(f"   监控时长: {duration_minutes} 分钟")
        
        # 内存趋势分析
        if len(memory_history) > 10:
            recent_avg = sum(memory_history[-10:]) / 10
            early_avg = sum(memory_history[:10]) / 10
            trend = recent_avg - early_avg
            
            print(f"   内存趋势: {trend:+.2f} MB (最近10次 vs 最初10次)")
            
            if trend > 10:
                print("🚨 检测到内存泄露趋势!")
            elif trend > 5:
                print("⚠️  内存使用有增长趋势")
            else:
                print("✅ 内存使用稳定")
        
    except psutil.NoSuchProcess:
        print(f"❌ 找不到进程 {target_pid}")
    except Exception as e:
        print(f"❌ 监控过程中出错: {e}")

def start_trending_window():
    """启动trending_window进程"""
    print("🚀 启动 trending_window.py...")
    
    import subprocess
    import sys
    
    # 启动trending_window
    process = subprocess.Popen([
        sys.executable, "trending_window.py"
    ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    
    # 等待进程启动
    time.sleep(3)
    
    return process

def main():
    print("🔥 trending_window.py 内存泄露测试工具")
    print("=" * 60)
    
    # 方式1: 监控现有进程
    # 查找正在运行的trending_window进程
    trending_processes = []
    for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
        try:
            if proc.info['cmdline'] and any('trending_window.py' in arg for arg in proc.info['cmdline']):
                trending_processes.append(proc.info['pid'])
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    
    if trending_processes:
        print(f"📍 找到现有的trending_window进程: {trending_processes}")
        target_pid = trending_processes[0]
        
        # 询问监控时长
        duration = 30
        try:
            user_input = input(f"输入监控时长(分钟，默认{duration}): ").strip()
            if user_input:
                duration = int(user_input)
        except:
            pass
        
        # 开始监控
        monitor_memory_usage(target_pid, duration)
    else:
        print("❌ 未找到运行中的trending_window.py进程")
        print("💡 请先运行: python trending_window.py")
        print("💡 然后在另一个终端运行此测试脚本")

if __name__ == '__main__':
    main() 