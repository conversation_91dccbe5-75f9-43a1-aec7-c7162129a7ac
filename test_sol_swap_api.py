#!/usr/bin/env python3
"""
测试SOL交换API请求
"""

import sys
import os
import json
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from okx_dex_client import OKXDexClient, QuoteRequest, SwapRequest
from config import PORTFOLIO_CONFIG

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_sol_swap_api():
    """测试SOL交换API"""
    
    print("🧪 SOL交换API测试")
    print("=" * 50)
    
    # 初始化客户端
    client = OKXDexClient(PORTFOLIO_CONFIG["okx_dex_api_url"])
    
    # 测试钱包地址
    test_wallet = "HfHb1cSKNm8BS2YYaLuYmURqQjnSoPwwdZgdWjs7crph"
    
    # 测试参数
    test_cases = [
        {
            "name": "USDC → SOL",
            "from_token": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",  # USDC
            "to_token": "******************************11",  # SOL
            "amount": "1000000",  # 1 USDC (6 decimals)
            "slippage": "0.030"
        },
        {
            "name": "Fartcoin → SOL", 
            "from_token": "9BB6NFEcaD8RgUQq1X2Ts7j6CZzF1bmS1xMChZPApump",  # Fartcoin
            "to_token": "******************************11",  # SOL
            "amount": "890679",  # 测试数量
            "slippage": "0.030"
        },
        {
            "name": "Fartcoin → USDC (对比测试)",
            "from_token": "9BB6NFEcaD8RgUQq1X2Ts7j6CZzF1bmS1xMChZPApump",  # Fartcoin
            "to_token": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",  # USDC
            "amount": "890679",  # 测试数量
            "slippage": "0.030"
        }
    ]
    
    # 测试健康检查
    print("\n🔍 1. API健康检查")
    health_result = client.health_check()
    print(f"健康状态: {health_result}")
    
    if not health_result.get('success'):
        print("❌ API服务不可用，无法继续测试")
        return False
    
    print("✅ API服务正常")
    
    # 测试每个案例
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n🧪 {i}. 测试: {test_case['name']}")
        print("-" * 30)
        
        # 创建报价请求
        quote_request = QuoteRequest(
            chain_id="501",
            from_token_address=test_case["from_token"],
            to_token_address=test_case["to_token"],
            amount=test_case["amount"],
            slippage=test_case["slippage"]
        )
        
        print(f"📤 请求参数:")
        print(f"  链ID: {quote_request.chain_id}")
        print(f"  源代币: {quote_request.from_token_address}")
        print(f"  目标代币: {quote_request.to_token_address}")
        print(f"  数量: {quote_request.amount}")
        print(f"  滑点: {quote_request.slippage}")
        
        # 发送报价请求
        quote_result = client.get_quote(quote_request)
        
        print(f"📥 响应结果:")
        if quote_result.get('success'):
            print("✅ 报价获取成功")
            if quote_result.get('data'):
                data = quote_result['data']
                to_amount = data.get('toTokenAmount', '0')
                print(f"  预期输出: {to_amount}")
                print(f"  路由信息: {data.get('routeName', 'Unknown')}")
            else:
                print("⚠️ 无数据返回")
        else:
            print("❌ 报价获取失败")
            error = quote_result.get('error', '未知错误')
            print(f"  错误信息: {error}")
        
        print(f"  完整响应: {json.dumps(quote_result, indent=2, ensure_ascii=False)}")
    
    # 特别测试SOL地址处理
    print("\n🎯 特别测试: SOL地址处理")
    print("-" * 30)
    
    sol_addresses_to_test = [
        "",  # 空地址（旧方式）
        "******************************11",  # 系统程序地址（新方式）
        "So****************************************2",  # Wrapped SOL
    ]
    
    for j, sol_addr in enumerate(sol_addresses_to_test, 1):
        print(f"\n🔬 {j}. 测试SOL地址: '{sol_addr}'")
        
        quote_request = QuoteRequest(
            chain_id="501",
            from_token_address="EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",  # USDC
            to_token_address=sol_addr,
            amount="1000000",  # 1 USDC
            slippage="0.030"
        )
        
        quote_result = client.get_quote(quote_request)
        
        if quote_result.get('success'):
            print(f"✅ 地址 '{sol_addr}' 有效")
        else:
            error = quote_result.get('error', '未知错误')
            print(f"❌ 地址 '{sol_addr}' 失败: {error}")
    
    print("\n🎉 测试完成!")


if __name__ == "__main__":
    test_sol_swap_api() 