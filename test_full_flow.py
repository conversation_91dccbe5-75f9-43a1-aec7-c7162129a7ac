#!/usr/bin/env python
"""
完整流程测试脚本 - 测试从API获取数据到显示在UI的完整流程
"""

import sys
import logging
import time
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QPushButton
from PyQt5.QtCore import QTimer

from api_service import APIService
from async_worker import AsyncTask

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('full_flow_test')

class TestWindow(QMainWindow):
    """测试窗口类"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("API流程测试")
        self.setGeometry(100, 100, 600, 400)
        
        # 创建中央组件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        
        # 创建状态标签
        self.status_label = QLabel("准备测试...")
        layout.addWidget(self.status_label)
        
        # 创建测试按钮
        self.test_trend_button = QPushButton("测试趋势榜单API")
        self.test_trend_button.clicked.connect(self.test_trend_api)
        layout.addWidget(self.test_trend_button)
        
        self.test_ohlcv_button = QPushButton("测试OHLCV API")
        self.test_ohlcv_button.clicked.connect(self.test_ohlcv_api)
        layout.addWidget(self.test_ohlcv_button)
        
        self.test_async_button = QPushButton("测试异步获取趋势榜单")
        self.test_async_button.clicked.connect(self.test_async_trend)
        layout.addWidget(self.test_async_button)
        
        # 创建结果标签
        self.result_label = QLabel("")
        layout.addWidget(self.result_label)
        
        # 自动开始测试
        QTimer.singleShot(500, self.run_all_tests)
    
    def run_all_tests(self):
        """运行所有测试"""
        self.status_label.setText("正在运行所有测试...")
        
        # 测试API连接
        self.test_connection()
        
        # 等待1秒后测试趋势榜单API
        QTimer.singleShot(1000, self.test_trend_api)
        
        # 等待2秒后测试OHLCV API
        QTimer.singleShot(2000, self.test_ohlcv_api)
        
        # 等待3秒后测试异步获取趋势榜单
        QTimer.singleShot(3000, self.test_async_trend)
    
    def test_connection(self):
        """测试API连接"""
        self.status_label.setText("正在测试API连接...")
        
        try:
            # 测试API连接
            results = APIService.test_api_connection()
            
            # 显示结果
            status_text = "API连接测试结果:\n"
            for api, status in results.items():
                status_text += f"{api}: {'成功' if status else '失败'}\n"
            
            self.result_label.setText(status_text)
            self.status_label.setText("API连接测试完成")
            
            logger.info(f"API连接测试结果: {results}")
        except Exception as e:
            self.status_label.setText(f"API连接测试失败: {str(e)}")
            logger.error(f"API连接测试失败: {str(e)}")
    
    def test_trend_api(self):
        """测试趋势榜单API"""
        self.status_label.setText("正在测试趋势榜单API...")
        
        try:
            # 直接获取趋势榜单数据
            tokens = APIService.get_trending_tokens()
            
            # 显示结果
            if tokens:
                self.status_label.setText(f"趋势榜单API测试成功，获取到 {len(tokens)} 个代币")
                self.result_label.setText(f"第一个代币: {tokens[0]['name']} ({tokens[0]['symbol']})")
                logger.info(f"趋势榜单API测试成功，获取到 {len(tokens)} 个代币")
                logger.info(f"第一个代币: {tokens[0]['name']} ({tokens[0]['symbol']})")
            else:
                self.status_label.setText("趋势榜单API测试失败，未获取到数据")
                logger.error("趋势榜单API测试失败，未获取到数据")
        except Exception as e:
            self.status_label.setText(f"趋势榜单API测试失败: {str(e)}")
            logger.error(f"趋势榜单API测试失败: {str(e)}")
    
    def test_ohlcv_api(self):
        """测试OHLCV API"""
        self.status_label.setText("正在测试OHLCV API...")
        
        try:
            # 使用一个测试代币地址
            token_address = "DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263"
            
            # 直接获取OHLCV数据
            ohlcv_data = APIService.get_ohlcv_data(token_address, timeframe='1m', days=1)
            
            # 显示结果
            if ohlcv_data:
                self.status_label.setText(f"OHLCV API测试成功，获取到 {len(ohlcv_data)} 条数据")
                self.result_label.setText(f"第一条数据: 时间={ohlcv_data[0]['datetime']}, 开盘价={ohlcv_data[0]['open']}")
                logger.info(f"OHLCV API测试成功，获取到 {len(ohlcv_data)} 条数据")
                logger.info(f"第一条数据: {ohlcv_data[0]}")
            else:
                self.status_label.setText("OHLCV API测试失败，未获取到数据")
                logger.error("OHLCV API测试失败，未获取到数据")
        except Exception as e:
            self.status_label.setText(f"OHLCV API测试失败: {str(e)}")
            logger.error(f"OHLCV API测试失败: {str(e)}")
    
    def test_async_trend(self):
        """测试异步获取趋势榜单"""
        self.status_label.setText("正在测试异步获取趋势榜单...")
        
        # 定义成功回调函数
        def on_success(tokens):
            if tokens:
                self.status_label.setText(f"异步获取趋势榜单成功，获取到 {len(tokens)} 个代币")
                self.result_label.setText(f"第一个代币: {tokens[0]['name']} ({tokens[0]['symbol']})")
                logger.info(f"异步获取趋势榜单成功，获取到 {len(tokens)} 个代币")
                logger.info(f"第一个代币: {tokens[0]['name']} ({tokens[0]['symbol']})")
            else:
                self.status_label.setText("异步获取趋势榜单失败，未获取到数据")
                logger.error("异步获取趋势榜单失败，未获取到数据")
        
        # 定义错误回调函数
        def on_error(error_msg):
            self.status_label.setText(f"异步获取趋势榜单失败: {error_msg}")
            logger.error(f"异步获取趋势榜单失败: {error_msg}")
        
        # 异步获取趋势榜单数据
        APIService.get_trending_tokens_async(on_success, on_error)

def main():
    """主函数"""
    app = QApplication(sys.argv)
    window = TestWindow()
    window.show()
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
