"""
测试HeadlessChartWidget和ChartWidget信号一致性
确保两种组件计算出的交易信号完全一致
"""

import sys
import os
import time
import pandas as pd
import numpy as np
from datetime import datetime
from typing import Dict, List, Tuple
import logging

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QEventLoop, QTimer

from api_service import APIService
from ui.chart_widget import ChartWidget
from ui.headless_chart_widget import HeadlessChartWidget
from strategies import StrategyFactory
from indicators import TechnicalIndicators

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SignalConsistencyTester:
    """信号一致性测试器"""
    
    def __init__(self):
        self.app = QApplication.instance() or QApplication(sys.argv)
        self.api_service = APIService()
        self.test_results = []
        
    def test_signal_consistency(self, test_tokens: List[Dict], strategies_to_test: List[str], 
                              timeframes: List[str] = None) -> Dict:
        """
        测试信号一致性
        
        参数:
            test_tokens: 测试代币列表
            strategies_to_test: 要测试的策略列表
            timeframes: 时间周期列表
        
        返回:
            测试结果摘要
        """
        if timeframes is None:
            timeframes = ['1m', '5m', '15m']
            
        logger.info(f"🧪 开始信号一致性测试")
        logger.info(f"   测试代币: {len(test_tokens)}个")
        logger.info(f"   测试策略: {len(strategies_to_test)}个")
        logger.info(f"   测试时间周期: {timeframes}")
        
        total_tests = len(test_tokens) * len(strategies_to_test) * len(timeframes)
        current_test = 0
        consistent_count = 0
        inconsistent_details = []
        
        for token in test_tokens:
            for strategy_name in strategies_to_test:
                for timeframe in timeframes:
                    current_test += 1
                    logger.info(f"📊 测试进度: {current_test}/{total_tests}")
                    
                    try:
                        is_consistent, details = self._test_single_combination(
                            token, strategy_name, timeframe
                        )
                        
                        if is_consistent:
                            consistent_count += 1
                            logger.info(f"✅ 一致: {token['symbol']} - {strategy_name} - {timeframe}")
                        else:
                            inconsistent_details.append(details)
                            logger.warning(f"❌ 不一致: {token['symbol']} - {strategy_name} - {timeframe}")
                            logger.warning(f"   差异详情: {details}")
                            
                    except Exception as e:
                        logger.error(f"🔥 测试失败: {token['symbol']} - {strategy_name} - {timeframe}: {e}")
                        inconsistent_details.append({
                            'token': token['symbol'],
                            'strategy': strategy_name,
                            'timeframe': timeframe,
                            'error': str(e),
                            'type': 'exception'
                        })
        
        # 生成测试报告
        consistency_rate = consistent_count / total_tests * 100
        
        result = {
            'total_tests': total_tests,
            'consistent_count': consistent_count,
            'inconsistent_count': len(inconsistent_details),
            'consistency_rate': consistency_rate,
            'inconsistent_details': inconsistent_details,
            'test_timestamp': datetime.now().isoformat()
        }
        
        self._print_test_summary(result)
        return result
    
    def _test_single_combination(self, token: Dict, strategy_name: str, timeframe: str) -> Tuple[bool, Dict]:
        """测试单个组合的信号一致性"""
        
        # 获取相同的数据
        token_address = token.get('tokenAddress') or token.get('address')
        ohlcv_data = self.api_service.get_ohlcv_data_sync(token_address, timeframe, days=1)
        
        if not ohlcv_data or len(ohlcv_data) < 50:
            return False, {
                'token': token.get('symbol', 'Unknown'),
                'strategy': strategy_name,
                'timeframe': timeframe,
                'error': 'insufficient_data',
                'data_points': len(ohlcv_data) if ohlcv_data else 0
            }
        
        # 准备相同的代币数据
        token_data = {
            'tokenAddress': token_address,
            'symbol': token.get('symbol', 'TEST'),
            'name': token.get('name', 'Test Token'),
            'strategy_name': strategy_name,
            'timeframe': timeframe,
            'source': 'test'
        }
        
        # 创建两个组件实例
        chart_widget = ChartWidget(api_service=self.api_service)
        headless_widget = HeadlessChartWidget(api_service=self.api_service, widget_id=f"test_{int(time.time())}")
        
        try:
            # 设置相同的策略
            chart_widget.current_strategy_name = strategy_name
            headless_widget.current_strategy_name = strategy_name
            
            # 设置相同的代币
            chart_widget.set_token(token_data)
            headless_widget.set_token(token_data)
            
            # 使用相同的数据进行处理
            chart_widget.display_provided_ohlcv(ohlcv_data, timeframe, "consistency_test")
            headless_widget.display_provided_ohlcv(ohlcv_data, timeframe, "consistency_test")
            
            # 等待处理完成
            self._wait_for_processing()
            
            # 比较结果
            return self._compare_widgets(chart_widget, headless_widget, token, strategy_name, timeframe)
            
        finally:
            # 清理资源
            chart_widget.deleteLater()
            # headless_widget 会自动清理
    
    def _wait_for_processing(self, timeout_ms: int = 5000):
        """等待组件处理完成"""
        loop = QEventLoop()
        timer = QTimer()
        timer.timeout.connect(loop.quit)
        timer.start(timeout_ms)
        loop.exec_()
    
    def _compare_widgets(self, chart_widget: ChartWidget, headless_widget: HeadlessChartWidget, 
                        token: Dict, strategy_name: str, timeframe: str) -> Tuple[bool, Dict]:
        """比较两个组件的结果"""
        
        details = {
            'token': token.get('symbol', 'Unknown'),
            'strategy': strategy_name,
            'timeframe': timeframe,
            'differences': []
        }
        
        # 1. 比较DataFrame形状
        chart_df = chart_widget.df
        headless_df = headless_widget.df
        
        if chart_df is None or headless_df is None:
            details['differences'].append({
                'type': 'dataframe_null',
                'chart_df_null': chart_df is None,
                'headless_df_null': headless_df is None
            })
            return False, details
        
        if chart_df.shape != headless_df.shape:
            details['differences'].append({
                'type': 'dataframe_shape',
                'chart_shape': chart_df.shape,
                'headless_shape': headless_df.shape
            })
            return False, details
        
        # 2. 比较技术指标
        indicator_columns = ['rsi', 'macd', 'macd_signal', 'vwap', 'sar', 'bb_high', 'bb_low']
        for col in indicator_columns:
            if col in chart_df.columns and col in headless_df.columns:
                if not self._compare_series(chart_df[col], headless_df[col]):
                    details['differences'].append({
                        'type': 'indicator_mismatch',
                        'column': col,
                        'sample_chart': chart_df[col].tail(3).tolist(),
                        'sample_headless': headless_df[col].tail(3).tolist()
                    })
        
        # 3. 比较信号列
        if 'signal' in chart_df.columns and 'signal' in headless_df.columns:
            if not self._compare_series(chart_df['signal'], headless_df['signal'], tolerance=0):
                # 获取信号差异的详细信息
                signal_diff = chart_df['signal'] != headless_df['signal']
                diff_indices = signal_diff[signal_diff].index.tolist()
                
                details['differences'].append({
                    'type': 'signal_mismatch',
                    'different_indices_count': len(diff_indices),
                    'sample_differences': [
                        {
                            'index': idx,
                            'chart_signal': chart_df.loc[idx, 'signal'],
                            'headless_signal': headless_df.loc[idx, 'signal'],
                            'timestamp': chart_df.loc[idx, 'timestamp'] if 'timestamp' in chart_df.columns else None
                        }
                        for idx in diff_indices[:5]  # 只显示前5个差异
                    ]
                })
        
        # 4. 比较买卖信号位置
        chart_buy_signals = getattr(chart_widget, 'current_buy_signals', [])
        chart_sell_signals = getattr(chart_widget, 'current_sell_signals', [])
        headless_buy_signals = getattr(headless_widget, 'current_buy_signals', [])
        headless_sell_signals = getattr(headless_widget, 'current_sell_signals', [])
        
        if len(chart_buy_signals) != len(headless_buy_signals):
            details['differences'].append({
                'type': 'buy_signals_count',
                'chart_count': len(chart_buy_signals),
                'headless_count': len(headless_buy_signals)
            })
        
        if len(chart_sell_signals) != len(headless_sell_signals):
            details['differences'].append({
                'type': 'sell_signals_count',
                'chart_count': len(chart_sell_signals),
                'headless_count': len(headless_sell_signals)
            })
        
        # 如果有任何差异，返回False
        is_consistent = len(details['differences']) == 0
        return is_consistent, details if not is_consistent else {}
    
    def _compare_series(self, series1: pd.Series, series2: pd.Series, tolerance: float = 1e-6) -> bool:
        """比较两个pandas Series是否相等（考虑浮点数误差）"""
        try:
            if len(series1) != len(series2):
                return False
            
            # 处理NaN值
            nan_mask1 = series1.isna()
            nan_mask2 = series2.isna()
            
            if not nan_mask1.equals(nan_mask2):
                return False
            
            # 比较非NaN值
            valid_mask = ~(nan_mask1 | nan_mask2)
            if valid_mask.any():
                diff = abs(series1[valid_mask] - series2[valid_mask])
                return diff.max() <= tolerance
            
            return True
        except Exception:
            return False
    
    def _print_test_summary(self, result: Dict):
        """打印测试摘要"""
        print("\n" + "="*80)
        print("🧪 信号一致性测试结果摘要")
        print("="*80)
        print(f"📊 总测试数: {result['total_tests']}")
        print(f"✅ 一致测试: {result['consistent_count']}")
        print(f"❌ 不一致测试: {result['inconsistent_count']}")
        print(f"📈 一致性率: {result['consistency_rate']:.2f}%")
        print(f"🕒 测试时间: {result['test_timestamp']}")
        
        if result['inconsistent_details']:
            print(f"\n❌ 不一致详情:")
            for i, detail in enumerate(result['inconsistent_details'][:10]):  # 只显示前10个
                print(f"   {i+1}. {detail.get('token', 'N/A')} - {detail.get('strategy', 'N/A')} - {detail.get('timeframe', 'N/A')}")
                if 'differences' in detail:
                    for diff in detail['differences'][:3]:  # 每个测试只显示前3个差异
                        print(f"      - {diff.get('type', 'unknown')}: {diff}")
        
        print("="*80)
    
    def run_comprehensive_test(self):
        """运行全面的一致性测试"""
        
        # 测试代币（使用一些知名代币）
        test_tokens = [
            {
                'symbol': 'SOL',
                'tokenAddress': 'So11111111111111111111111111111111111111112',
                'name': 'Solana'
            },
            {
                'symbol': 'USDC',
                'tokenAddress': 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',
                'name': 'USD Coin'
            }
        ]
        
        # 测试策略
        strategies_to_test = []
        try:
            all_strategies = StrategyFactory.get_all_strategies()
            strategies_to_test = [strategy.name for strategy in all_strategies[:3]]  # 测试前3个策略
        except Exception as e:
            logger.error(f"获取策略列表失败: {e}")
            strategies_to_test = ["VWAP 交叉策略"]  # 回退到默认策略
        
        # 执行测试
        result = self.test_signal_consistency(
            test_tokens=test_tokens,
            strategies_to_test=strategies_to_test,
            timeframes=['5m', '15m']
        )
        
        return result

def main():
    """主函数"""
    tester = SignalConsistencyTester()
    
    try:
        result = tester.run_comprehensive_test()
        
        # 保存测试结果
        import json
        with open(f"signal_consistency_test_{int(time.time())}.json", 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        
        # 根据结果给出建议
        if result['consistency_rate'] >= 95:
            print("\n🎉 恭喜！信号一致性测试通过，可以安全使用HeadlessChartWidget")
        elif result['consistency_rate'] >= 80:
            print("\n⚠️  信号一致性有一些问题，建议进一步调查")
        else:
            print("\n🚨 严重警告！信号一致性差，不建议在实盘交易中使用HeadlessChartWidget")
        
        return result['consistency_rate'] >= 95
        
    except Exception as e:
        logger.error(f"测试执行失败: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 