"""
This file contains stylesheet constants for the Live Trading Widget.
"""

# Wallet Button Style
WALLET_BUTTON_CONNECTED_STYLE = """
    QPushButton {
        background-color: #27ae60;
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 4px;
        font-weight: bold;
    }
    QPushButton:hover {
        background-color: #2ecc71;
    }
    QPushButton:pressed {
        background-color: #229954;
    }
"""

# Wallet Button Style - Default (Not Connected)
# Assuming LiveTradingStyles.BUTTON_WALLET from ui/styles.py is the default
# We will centralize this later if needed.
# For now, let's define a placeholder if it's used directly before connection.
WALLET_BUTTON_DEFAULT_STYLE = """
    QPushButton {
        background-color: #3498db; /* Example default color */
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 4px;
        font-weight: bold;
    }
    QPushButton:hover {
        background-color: #5dade2;
    }
    QPushButton:pressed {
        background-color: #2980b9;
    }
"""

# Token Balance Label Styles
TOKEN_BALANCE_LABEL_SUCCESS_STYLE = """
    QLabel {
        color: #27ae60;
        font-size: 9px;
        font-weight: bold;
        padding: 2px 6px;
        background: rgba(39, 174, 96, 0.1);
        border: 1px solid #27ae60;
        border-radius: 3px;
        min-width: 60px;
    }
"""

TOKEN_BALANCE_LABEL_ERROR_STYLE = """
    QLabel {
        color: #e74c3c;
        font-size: 9px;
        font-weight: bold;
        padding: 2px 6px;
        background: rgba(231, 76, 60, 0.1);
        border: 1px solid #e74c3c;
        border-radius: 3px;
        min-width: 60px;
    }
"""

TOKEN_BALANCE_LABEL_NEUTRAL_STYLE = """
    QLabel {
        color: #95a5a6;
        font-size: 9px;
        font-weight: normal;
        padding: 2px 6px;
        background: rgba(149, 165, 166, 0.1);
        border: 1px solid #95a5a6;
        border-radius: 3px;
        min-width: 60px;
    }
"""

TOKEN_BALANCE_LABEL_DEFAULT_STYLE = """
    QLabel {
        color: #f39c12;
        font-size: 9px;
        font-weight: bold;
        padding: 2px 6px;
        background: rgba(243, 156, 18, 0.1);
        border: 1px solid #f39c12;
        border-radius: 3px;
        min-width: 60px;
    }
"""

# Token Balance Label Initial Style
TOKEN_BALANCE_LABEL_INITIAL_STYLE = """
    QLabel {
        color: #f39c12;
        font-size: 9px;
        font-weight: bold;
        padding: 2px 6px;
        background: rgba(243, 156, 18, 0.1);
        border: 1px solid #f39c12;
        border-radius: 3px;
        min-width: 60px;
    }
"""

# Trading Toggle Button Styles
TRADING_TOGGLE_ON_STYLE = """
    QPushButton {
        background-color: #27ae60;
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 4px;
        font-weight: bold;
    }
    QPushButton:hover {
        background-color: #2ecc71;
    }
    QPushButton:pressed {
        background-color: #229954;
    }
"""

TRADING_TOGGLE_OFF_STYLE = """
    QPushButton {
        background-color: #e74c3c;
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 4px;
        font-weight: bold;
    }
    QPushButton:hover {
        background-color: #ec7063;
    }
    QPushButton:pressed {
        background-color: #c0392b;
    }
"""

# Parallel Analysis Button Style
PARALLEL_ANALYSIS_BUTTON_STYLE = """
    QPushButton {
        background-color: #e67e22;
        color: white;
        border: 1px solid #d35400;
        border-radius: 3px;
        padding: 4px 8px;
        font-size: 12px;
        font-weight: bold;
        max-height: 24px;
        min-width: 30px;
    }
    QPushButton:hover {
        background-color: #f39c12;
    }
    QPushButton:pressed {
        background-color: #d35400;
    }
    QPushButton:disabled {
        background-color: #7f8c8d;
        border: 1px solid #95a5a6;
        color: #bdc3c7;
    }
"""

# Refresh Balance Button Style
REFRESH_BALANCE_BUTTON_STYLE = """
    QPushButton {
        background-color: #3498db;
        color: white;
        border: 1px solid #2980b9;
        border-radius: 3px;
        padding: 2px 4px;
        font-size: 8px;
        max-width: 20px;
        max-height: 22px;
    }
    QPushButton:hover {
        background-color: #5dade2;
    }
"""

# Trend Coins Panel Refresh Button Style
TREND_COINS_REFRESH_BUTTON_STYLE = """
    QPushButton {
        background-color: #3498db;
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 4px;
        font-weight: bold;
    }
    QPushButton:hover {
        background-color: #2980b9;
    }
    QPushButton:pressed {
        background-color: #21618c;
    }
"""

# Trend Coins Panel Status Label Style
TREND_COINS_STATUS_LABEL_STYLE = """
    QLabel {
        color: #7f8c8d;
        font-size: 11px;
        padding: 5px;
    }
"""

# Trend Coins Panel Last Update Label Style
TREND_COINS_LAST_UPDATE_LABEL_STYLE = """
    QLabel {
        color: #95a5a6;
        font-size: 10px;
        font-weight: normal;
        padding: 2px 8px;
        background: rgba(52, 73, 94, 0.3);
        border: 1px solid #34495e;
        border-radius: 3px;
    }
"""

# Trend Text Display Style
TREND_TEXT_DISPLAY_STYLE = """
    QTextEdit {
        background-color: #2c3e50;
        color: #ecf0f1;
        border: 1px solid #34495e;
        border-radius: 5px;
        font-family: 'Consolas', 'Monaco', monospace;
        font-size: 12px;
        padding: 10px;
    }
    QScrollBar:vertical {
        background-color: #34495e;
        width: 12px;
        border-radius: 6px;
    }
    QScrollBar::handle:vertical {
        background-color: #7f8c8d;
        border-radius: 6px;
        min-height: 20px;
    }
    QScrollBar::handle:vertical:hover {
        background-color: #95a5a6;
    }
"""

# Test Chart Button Style
TEST_CHART_BUTTON_STYLE = """
    QPushButton {
        background-color: #27ae60;
        color: white;
        border: 1px solid #229954;
        border-radius: 3px;
        padding: 2px 8px;
        font-size: 9px;
        font-weight: bold;
        max-height: 22px;
    }
    QPushButton:hover {
        background-color: #2ecc71;
    }
""" 


# trade_records_table.setStyleSheet

TRADE_RECORDS_TABLE_STYLE = """
    QTableWidget {
        background-color: #2c3e50;
        border: none;
        gridline-color: #34495e;
        font-size: 9px;
        color: #ecf0f1;
        selection-background-color: #3498db;
    }
    QTableWidget::item {
        padding: 2px 4px;
        border-bottom: 1px solid #34495e;
    }
    QTableWidget::item:selected {
        background-color: #3498db;
    }
    QHeaderView::section {
        background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
            stop: 0 #4a5568, stop: 1 #3a4a5c);
        border: none;
        border-bottom: 2px solid #1a252f;
        border-right: 1px solid #2c3e50;
        padding: 4px 6px;
        font-size: 9px;
        font-weight: bold;
        color: #ecf0f1;
    }
"""