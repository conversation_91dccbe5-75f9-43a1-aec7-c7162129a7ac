"""
无GUI版本的K线图表组件 - 用于后台监控代币而不显示界面
专门用于Portfolio监控钱包持仓代币的策略信号
"""

import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import time

import pandas as pd
import numpy as np

from PyQt5.QtCore import QObject, QTimer, pyqtSignal, pyqtSlot

from api_service import APIService
from indicators import TechnicalIndicators
from config import (
    DEFAULT_TIMEFRAME, AVAILABLE_TIMEFRAMES,
    CHART_REFRESH_INTERVAL, PORTFOLIO_CONFIG,
    BACKGROUND_MONITORING_CONFIG
)
from strategies import StrategyFactory

# 配置日志
logger = logging.getLogger('headless_chart_widget')


class HeadlessChartWidget(QObject):
    """无GUI版本的K线图表组件 - 专门用于后台监控"""
    
    # 保持与原版相同的信号定义
    trade_signal_generated = pyqtSignal(str, float, int, str, int)  # 信号类型(buy/sell), 价格, 时间戳, 策略名, 索引
    strategy_analysis_completed = pyqtSignal(str, str, str, int)  # token_address, token_symbol, final_signal, chart_index
    
    def __init__(self, api_service: APIService, widget_id: str = None, parent=None):
        """
        初始化无GUI版本的K线图表组件
        
        参数:
            api_service (APIService): APIService的实例，用于获取数据
            widget_id (str): 组件标识符，用于日志区分
            parent (QObject, optional): 父对象
        """
        super().__init__(parent)
        self.api_service = api_service
        self.widget_id = widget_id or f"HeadlessChart_{id(self)}"
        
        # 🔥 标识为后台并行组件
        self.is_background_parallel = True
        
        # 🔥🔥 性能模式配置
        self.performance_config = BACKGROUND_MONITORING_CONFIG.get('performance_mode', {})
        self.refresh_interval = BACKGROUND_MONITORING_CONFIG.get('refresh_interval', CHART_REFRESH_INTERVAL)
        self.enable_detailed_logs = not self.performance_config.get('skip_detailed_logs', True)
        self.minimal_signals = self.performance_config.get('minimal_signals', True)
        
        # 数据相关属性
        self.token_data = None
        self.ohlcv_data = None
        self.df = None
        self.timeframe = DEFAULT_TIMEFRAME
        self.days = 1
        self.auto_refresh = True  # 后台组件默认开启自动刷新
        self.current_strategy_name: Optional[str] = None
        
        # 上下文数据（用于导航，虽然无GUI版本不需要，但保持接口一致）
        self.context_token_list: Optional[List[Dict]] = None
        self.current_token_index_in_context: int = -1
        
        # 存储当前策略的买卖信号位置数据
        self.current_buy_signals = []  # 存储买入信号的位置: [(index, price, timestamp), ...]
        self.current_sell_signals = [] # 存储卖出信号的位置: [(index, price, timestamp), ...]
        
        # 连接APIService的信号
        if self.api_service:
            self.api_service.ohlcv_data_ready.connect(self.on_ohlcv_data_received)
            self.api_service.ohlcv_data_error.connect(self.on_ohlcv_fetch_error)
            logger.info(f"HeadlessChartWidget[{self.widget_id}]: APIService信号连接成功")
            self.verify_api_connections()
        else:
            logger.error(f"HeadlessChartWidget[{self.widget_id}]: APIService instance is None")
        
        # 自动刷新定时器
        self.refresh_timer = QTimer(self)
        self.refresh_timer.timeout.connect(self.refresh_data)
        

        
        logger.info(f"HeadlessChartWidget[{self.widget_id}]: 初始化完成（后台模式）")
    
    def verify_api_connections(self):
        """验证API连接状态"""
        try:
            if not hasattr(self.api_service, 'ohlcv_data_ready'):
                logger.error(f"HeadlessChartWidget[{self.widget_id}]: APIService缺少ohlcv_data_ready信号")
                return False
            
            if not hasattr(self.api_service, 'ohlcv_data_error'):
                logger.error(f"HeadlessChartWidget[{self.widget_id}]: APIService缺少ohlcv_data_error信号")
                return False
                
            logger.info(f"HeadlessChartWidget[{self.widget_id}]: API连接验证通过")
            return True
        except Exception as e:
            logger.error(f"HeadlessChartWidget[{self.widget_id}]: API连接验证失败: {e}")
            return False
    
    def set_token(self, token_data):
        """
        设置当前监控的代币
        
        参数:
            token_data (Dict): 代币数据, 可能包含 'strategy_name' 和 'timeframe'
        """
        # 清空上一个代币的信号位置数据
        self.current_buy_signals.clear()
        self.current_sell_signals.clear()
        
        # 🔥 创建token_data的深拷贝，避免共享引用问题
        import copy
        self.token_data = copy.deepcopy(token_data) if token_data else None
        
        if not self.token_data:
            logger.warning(f"HeadlessChartWidget[{self.widget_id}]: token_data为空")
            return
        
        # 获取代币基本信息
        symbol = self.token_data.get('symbol', 'Unknown')
        name = self.token_data.get('name', 'Unknown')
        price = self.token_data.get('price', 0)
        token_address = self.token_data.get('tokenAddress', 'Unknown')
        
        logger.info(f"HeadlessChartWidget[{self.widget_id}]: 设置代币 {name} ({symbol}) - ${price:.8f}")
        logger.info(f"HeadlessChartWidget[{self.widget_id}]: 代币地址: {token_address}")
        
        # 优先使用token_data中指定的timeframe
        new_timeframe = self.token_data.get('timeframe', self.timeframe)
        if new_timeframe != self.timeframe:
            self.timeframe = new_timeframe
            logger.info(f"HeadlessChartWidget[{self.widget_id}]: 时间周期设置为 {self.timeframe}")
        
        # 根据数据来源调整天数
        token_source = self.token_data.get('source', 'unknown')
        if token_source == 'historical':
            self.days = 1
            logger.info(f"HeadlessChartWidget[{self.widget_id}]: 数据源为自定义API，天数设置为1")
        elif token_source == 'holdings':  # 🔥🔥 新增：持仓来源支持
            logger.info(f"HeadlessChartWidget[{self.widget_id}]: 数据源为持仓代币，使用Birdeye API")
        
        # 处理外部指定的策略名称
        external_strategy_name = self.token_data.get('strategy_name')
        if external_strategy_name:
            self.current_strategy_name = external_strategy_name
            logger.info(f"HeadlessChartWidget[{self.widget_id}]: 策略设置为 '{self.current_strategy_name}'")
        else:
            # 如果外部没有指定策略，使用config中的默认策略
            self.current_strategy_name = PORTFOLIO_CONFIG.get("default_strategy", "VWAP 交叉策略")
            logger.info(f"HeadlessChartWidget[{self.widget_id}]: 使用默认策略 '{self.current_strategy_name}'")
        
        # 开始数据刷新
        self.refresh_data()
        
        # 更新上下文索引
        if self.context_token_list and self.token_data:
            found_idx = -1
            key_to_compare = 'tokenAddress' if self.token_data.get('source') == 'trend' else 'address'
            for idx, t in enumerate(self.context_token_list):
                if t.get(key_to_compare) == self.token_data.get(key_to_compare):
                    found_idx = idx
                    break
            if found_idx != -1:
                self.current_token_index_in_context = found_idx
    
    @pyqtSlot()
    def refresh_data(self):
        """触发异步刷新K线数据"""
        if not self.token_data:
            logger.warning(f"HeadlessChartWidget[{self.widget_id}]: 没有设置代币数据")
            return
        
        token_address = self.token_data.get('tokenAddress')
        token_source = self.token_data.get('source', 'unknown')
        symbol = self.token_data.get('symbol', 'Unknown')
        
        # 🔥 性能模式：减少日志输出
        if self.enable_detailed_logs:
            logger.info(f"HeadlessChartWidget[{self.widget_id}]({symbol}): refresh_data开始")
            logger.info(f"HeadlessChartWidget[{self.widget_id}]({symbol}): token_address = {token_address}")
            logger.info(f"HeadlessChartWidget[{self.widget_id}]({symbol}): token_data keys = {list(self.token_data.keys()) if self.token_data else 'None'}")
        
        if not token_address:
            logger.error(f"HeadlessChartWidget[{self.widget_id}]: {symbol} 无效的代币地址")
            return
        
        if token_source == 'unknown':
            logger.warning(f"HeadlessChartWidget[{self.widget_id}]: {symbol} 代币来源未知")
            return
        
        # 🔥🔥 映射holdings来源到trend，因为都使用Birdeye API
        api_source = token_source
        if token_source == 'holdings':
            api_source = 'trend'  # holdings代币使用Birdeye API获取数据
        
        # 🔥🔥 使用后台监控管理器（如果可用）
        parent_widget = self.parent()
        if (hasattr(parent_widget, 'background_monitor_manager') and 
            parent_widget.background_monitor_manager):
            
            # 请求数据通过统一管理器
            strategy_name = self.current_strategy_name or 'default'
            has_cache = parent_widget.background_monitor_manager.request_token_data(
                self.token_data, strategy_name, self.timeframe
            )
            
            if has_cache:
                # 数据会通过信号异步到达
                return
            else:
                if self.enable_detailed_logs:
                    logger.info(f"HeadlessChartWidget[{self.widget_id}]: {symbol} 已加入统一管理器队列")
                return
        
        # 回退到原有的直接API调用方式
        cached_data = self.try_get_cached_ohlcv_data(token_address, self.timeframe)
        if cached_data:
            if self.enable_detailed_logs:
                logger.info(f"HeadlessChartWidget[{self.widget_id}]: {symbol} 使用缓存的OHLCV数据")
            self.display_provided_ohlcv(cached_data, self.timeframe, "缓存数据")
            return
        
        # 从API获取数据
        api_description = "自定义API" if api_source == 'historical' else "Birdeye API"
        if self.enable_detailed_logs:
            logger.info(f"HeadlessChartWidget[{self.widget_id}]: {symbol} 从{api_description}获取数据 [{self.timeframe}]")
            logger.info(f"HeadlessChartWidget[{self.widget_id}]({symbol}): 调用API - token_address={token_address}, timeframe={self.timeframe}, days={self.days}, source={api_source}")
        
        if self.api_service:
            self.api_service.get_ohlcv_data_async(
                token_address=token_address,
                timeframe=self.timeframe,
                days=self.days,
                source=api_source
            )
        else:
            logger.error(f"HeadlessChartWidget[{self.widget_id}]: APIService未初始化")
    
    @pyqtSlot(str, str, list)
    def on_ohlcv_data_received(self, received_token_address: str, received_timeframe: str, ohlcv_data: list):
        """处理从APIService收到的OHLCV数据（异步）"""
        symbol = self.token_data.get('symbol', 'Unknown') if self.token_data else 'Unknown'
        log_prefix = f"HeadlessChartWidget[{self.widget_id}]({symbol})"
        
        logger.info(f"{log_prefix}: 收到OHLCV数据 - 地址:{received_token_address}, 周期:{received_timeframe}")
        
        current_token_address = self.token_data.get('tokenAddress') if self.token_data else None
        
        logger.info(f"{log_prefix}: 当前代币地址: {current_token_address}")
        logger.info(f"{log_prefix}: 当前时间周期: {self.timeframe}")
        
        if not self.token_data or current_token_address != received_token_address:
            logger.debug(f"{log_prefix}: 忽略不同代币的数据 {received_token_address}")
            return
        
        if self.timeframe != received_timeframe:
            logger.debug(f"{log_prefix}: 忽略不同时间周期的数据 {received_timeframe}")
            return
        
        if not ohlcv_data:
            logger.warning(f"{log_prefix}: OHLCV数据为空")
            self.df = pd.DataFrame()
            
            # 🔥 发射一个"无数据"的分析完成信号，避免界面一直显示"分析中"
            token_address = self.token_data.get('tokenAddress', 'Unknown') if self.token_data else 'Unknown'
            token_symbol = self.token_data.get('symbol', 'Unknown') if self.token_data else 'Unknown'
            self.strategy_analysis_completed.emit(token_address, token_symbol, "无数据", -1)
            logger.info(f"{log_prefix}: 发射无数据信号完成")
            return
        
        logger.info(f"{log_prefix}: 处理OHLCV数据 ({len(ohlcv_data)} 条记录)")
        
        # 🔥 新增：显示数据样例
        if len(ohlcv_data) > 0:
            logger.info(f"{log_prefix}: 数据样例 - 第一条: {ohlcv_data[0]}")
            logger.info(f"{log_prefix}: 数据样例 - 最后一条: {ohlcv_data[-1]}")
        
        self.ohlcv_data = ohlcv_data
        try:
            self.df = pd.DataFrame(ohlcv_data)
            
            if self.df.empty:
                logger.warning(f"{log_prefix}: DataFrame为空")
                return
            
            logger.info(f"{log_prefix}: DataFrame创建成功，形状: {self.df.shape}")
            logger.info(f"{log_prefix}: DataFrame列: {list(self.df.columns)}")
            
            # 计算技术指标
            logger.info(f"{log_prefix}: 开始计算技术指标")
            self.df = self.calculate_indicators(self.df)
            logger.info(f"{log_prefix}: 技术指标计算完成，DataFrame列: {list(self.df.columns)}")
            
            # 处理策略信号（这是核心功能）
            logger.info(f"{log_prefix}: 开始处理策略信号")
            self.process_strategy_signals()
            logger.info(f"{log_prefix}: 策略信号处理完成")
            
            # 启动自动刷新定时器（后台组件始终保持运行）
            if self.auto_refresh and not self.refresh_timer.isActive():
                if self.enable_detailed_logs:
                    logger.info(f"{log_prefix}: 启动自动刷新定时器 (间隔: {self.refresh_interval/1000}秒)")
                self.refresh_timer.start(self.refresh_interval)
            
            logger.info(f"{log_prefix}: 数据处理完成，加载了 {len(self.df)} 条K线数据")
            
        except Exception as e:
            logger.error(f"{log_prefix}: 处理K线数据时出错: {str(e)}", exc_info=True)
            self.df = pd.DataFrame()
    
    @pyqtSlot(str)
    def on_ohlcv_fetch_error(self, error_msg: str):
        """处理从APIService收到的OHLCV数据获取错误（异步）"""
        symbol = self.token_data.get('symbol', 'Unknown') if self.token_data else 'Unknown'
        log_prefix = f"HeadlessChartWidget[{self.widget_id}]({symbol})"
        
        logger.error(f"{log_prefix}: 获取K线数据失败: {error_msg}")
        self.df = pd.DataFrame()
        
        # 后台组件即使出错也要继续尝试
        if self.auto_refresh and not self.refresh_timer.isActive():
            if self.enable_detailed_logs:
                logger.info(f"{log_prefix}: 错误后启动自动刷新定时器继续尝试")
            self.refresh_timer.start(self.refresh_interval)
    
    def process_strategy_signals(self):
        """处理策略信号分析"""
        try:
            symbol = self.token_data.get('symbol', 'Unknown') if self.token_data else 'Unknown'
            
            # 🔥 添加详细的调试信息
            logger.info(f"HeadlessChartWidget[{self.widget_id}]({symbol}): 开始处理策略信号")
            logger.info(f"HeadlessChartWidget[{self.widget_id}]({symbol}): DataFrame状态 - 是否为None: {self.df is None}, 是否为空: {self.df.empty if self.df is not None else 'N/A'}")
            logger.info(f"HeadlessChartWidget[{self.widget_id}]({symbol}): 当前策略: {self.current_strategy_name}")
            
            # 🔥 修复 DataFrame 布尔判断错误
            if self.df is None or self.df.empty:
                logger.warning(f"HeadlessChartWidget[{self.widget_id}]({symbol}): 没有K线数据，跳过信号分析")
                return
            
            if not self.current_strategy_name or self.current_strategy_name == "不显示":
                logger.warning(f"HeadlessChartWidget[{self.widget_id}]({symbol}): 策略为不显示，跳过信号分析")
                return
            
            logger.info(f"HeadlessChartWidget[{self.widget_id}]({symbol}): 开始处理策略信号 - 策略: {self.current_strategy_name}, 数据: {len(self.df)} 条")
            

            
            # 获取策略实例
            strategy = StrategyFactory.get_strategy_by_name(self.current_strategy_name)
            if not strategy:
                logger.error(f"HeadlessChartWidget[{self.widget_id}]({symbol}): 找不到策略: {self.current_strategy_name}")
                return
            
            logger.info(f"HeadlessChartWidget[{self.widget_id}]({symbol}): 成功获取策略实例: {type(strategy).__name__}")
            
            # 计算信号
            try:
                
                # 策略的generate_signals方法返回带有signal列的DataFrame
                df_with_signals = strategy.generate_signals(self.df)
                
                logger.info(f"HeadlessChartWidget[{self.widget_id}]({symbol}): 策略计算完成")
                logger.info(f"HeadlessChartWidget[{self.widget_id}]({symbol}): 返回数据列: {list(df_with_signals.columns) if df_with_signals is not None else 'None'}")
                
                # 从signal列中提取买入和卖出信号的位置
                buy_signals = []
                sell_signals = []
                
                if df_with_signals is None or df_with_signals.empty:
                    logger.warning(f"HeadlessChartWidget[{self.widget_id}]({symbol}): 策略返回空数据")
                    return
                
                if 'signal' not in df_with_signals.columns:
                    logger.warning(f"HeadlessChartWidget[{self.widget_id}]({symbol}): 策略返回数据缺少signal列")
                    return
                
                signal_counts = df_with_signals['signal'].value_counts().to_dict()
                logger.info(f"HeadlessChartWidget[{self.widget_id}]({symbol}): 信号统计: {signal_counts}")
                
                for i in range(len(df_with_signals)):
                    signal = df_with_signals['signal'].iloc[i]
                    if signal == 1:  # 买入信号
                        timestamp = int(df_with_signals['timestamp'].iloc[i]) if 'timestamp' in df_with_signals.columns else int(time.time())
                        price = df_with_signals['close'].iloc[i]
                        buy_signals.append((i, price, timestamp))
                    elif signal == -1:  # 卖出信号
                        timestamp = int(df_with_signals['timestamp'].iloc[i]) if 'timestamp' in df_with_signals.columns else int(time.time())
                        price = df_with_signals['close'].iloc[i]
                        sell_signals.append((i, price, timestamp))
                
                logger.info(f"HeadlessChartWidget[{self.widget_id}]({symbol}): 计算完成 - 买入信号: {len(buy_signals)}, 卖出信号: {len(sell_signals)}")
            except Exception as e:
                logger.error(f"HeadlessChartWidget[{self.widget_id}]({symbol}): 计算策略信号失败: {e}", exc_info=True)
                return
            
            # 更新信号
            self.current_buy_signals = buy_signals
            self.current_sell_signals = sell_signals
            
            logger.info(f"HeadlessChartWidget[{self.widget_id}]({symbol}): 更新信号列表完成")
            
            # 发射最新信号（如果有的话）
            logger.info(f"HeadlessChartWidget[{self.widget_id}]({symbol}): 准备发射信号")
            self.emit_latest_signals()
            
            # 生成分析完成信号
            logger.info(f"HeadlessChartWidget[{self.widget_id}]({symbol}): 解释最新信号状态")
            latest_signal, signal_index = self.interpret_latest_signal(df_with_signals, self.current_strategy_name)
            
            # 获取代币信息
            token_address = self.token_data.get('tokenAddress', 'Unknown') if self.token_data else 'Unknown'
            token_symbol = self.token_data.get('symbol', 'Unknown') if self.token_data else 'Unknown'
            
            logger.info(f"HeadlessChartWidget[{self.widget_id}]({symbol}): 准备发射分析完成信号: {latest_signal}")
            
            # 发射分析完成信号
            self.strategy_analysis_completed.emit(token_address, token_symbol, latest_signal, signal_index)
            
            logger.info(f"HeadlessChartWidget[{self.widget_id}]({symbol}): 策略分析完成 - {token_symbol} -> {latest_signal}")
            
        except Exception as e:
            logger.error(f"HeadlessChartWidget[{self.widget_id}]({symbol}): 处理策略信号时出错: {e}", exc_info=True)
    
    def calculate_indicators(self, df):
        """计算技术指标"""
        if df.empty:
            return df
        
        # 🔥🔥 性能模式：减少指标计算
        if self.performance_config.get('reduce_indicator_calc', True):
            # 只计算策略必需的核心指标
            df_with_indicators = TechnicalIndicators.add_essential_indicators(df.copy())
        else:
            df_with_indicators = TechnicalIndicators.add_all_indicators(df.copy())
        return df_with_indicators
    
    def interpret_latest_signal(self, df: pd.DataFrame, strategy_name: str) -> tuple:
        """解释最新的策略信号状态"""
        try:
            symbol = self.token_data.get('symbol', 'Unknown') if self.token_data else 'Unknown'
            
            if df is None or df.empty:
                logger.debug(f"HeadlessChartWidget[{self.widget_id}]({symbol}): DataFrame为空，返回观察")
                return ("观察", -1)
                
            if 'signal' not in df.columns:
                logger.debug(f"HeadlessChartWidget[{self.widget_id}]({symbol}): DataFrame缺少signal列，返回观察")
                return ("观察", -1)
            
            # 获取最近的信号
            signals = df['signal'].values
            if len(signals) == 0:
                return ("观察", -1)
            
            # 找到所有非零信号的位置
            buy_positions = []
            sell_positions = []
            
            for i in range(len(signals)):
                if signals[i] == 1:  # 买入信号
                    buy_positions.append(i)
                elif signals[i] == -1:  # 卖出信号
                    sell_positions.append(i)
            
            # 如果没有任何信号
            if not buy_positions and not sell_positions:
                return ("观察", -1)
            
            # 确定最后一个信号
            last_buy_pos = buy_positions[-1] if buy_positions else -1
            last_sell_pos = sell_positions[-1] if sell_positions else -1
            
            if last_buy_pos > last_sell_pos:
                # 最后一个信号是买入
                signals_after_buy = signals[last_buy_pos + 1:]
                if len(signals_after_buy) >= 10 and all(s == 0 for s in signals_after_buy[-10:]):
                    return ("持有", last_buy_pos)
                else:
                    return ("买入", last_buy_pos)
            elif last_sell_pos > last_buy_pos:
                # 最后一个信号是卖出
                return ("卖出", last_sell_pos)
            else:
                return ("观察", -1)
                
        except Exception as e:
            symbol = self.token_data.get('symbol', 'Unknown') if self.token_data else 'Unknown'
            logger.error(f"HeadlessChartWidget[{self.widget_id}]({symbol}): 解释信号状态时出错: {e}")
            return ("观察", -1)
    
    def display_provided_ohlcv(self, ohlcv_data: list, timeframe: str, source_description: str):
        """显示外部提供的OHLCV数据"""
        symbol = self.token_data.get('symbol', 'Unknown') if self.token_data else 'Unknown'
        log_prefix = f"HeadlessChartWidget[{self.widget_id}]({symbol})"
        
        if not self.token_data:
            logger.warning(f"{log_prefix}: 没有设置代币数据")
            return
        
        try:
            if not ohlcv_data or len(ohlcv_data) == 0:
                logger.warning(f"{log_prefix}: 提供的OHLCV数据为空")
                self.df = pd.DataFrame()
                return
            
            # 转换为DataFrame并计算指标
            self.df = pd.DataFrame(ohlcv_data)
            if not self.df.empty:
                self.df = self.calculate_indicators(self.df)
                self.process_strategy_signals()
                logger.info(f"{log_prefix}: 使用提供的数据，加载了 {len(self.df)} 条K线数据 (来源: {source_description})")
            
            # 后台组件保持自动刷新
            if self.auto_refresh and not self.refresh_timer.isActive():
                self.refresh_timer.start(CHART_REFRESH_INTERVAL)
                
        except Exception as e:
            logger.error(f"{log_prefix}: 处理提供的K线数据时出错: {str(e)}", exc_info=True)
            self.df = pd.DataFrame()
    
    def try_get_cached_ohlcv_data(self, token_address: str, timeframe: str) -> Optional[List[Dict]]:
        """尝试从APIService获取缓存的OHLCV数据"""
        try:
            if self.api_service and hasattr(self.api_service, 'get_cached_ohlcv_data'):
                cached_data = self.api_service.get_cached_ohlcv_data(token_address, timeframe)
                if cached_data:
                    return cached_data
            return None
        except Exception as e:
            logger.error(f"HeadlessChartWidget[{self.widget_id}]: 获取缓存数据失败: {e}")
            return None
    
    def on_timeframe_changed(self, timeframe):
        """处理时间周期变更事件"""
        self.timeframe = timeframe
        logger.info(f"HeadlessChartWidget[{self.widget_id}]: 时间周期变更为 {timeframe}")
        self.refresh_data()
    
    def on_days_changed(self, days):
        """处理天数变更事件"""
        self.days = days
        logger.info(f"HeadlessChartWidget[{self.widget_id}]: 天数变更为 {days}")
        self.refresh_data()
    
    def on_auto_refresh_changed(self, state):
        """处理自动刷新选项变更事件（后台组件通常保持开启）"""
        # 对于后台组件，通常不关闭自动刷新
        self.auto_refresh = True
        if not self.refresh_timer.isActive():
            self.refresh_timer.start(CHART_REFRESH_INTERVAL)
    
    def on_strategy_changed(self, strategy_name: str):
        """处理策略选择变化事件"""
        logger.info(f"HeadlessChartWidget[{self.widget_id}]: 策略变更为: {strategy_name}")
        self.current_strategy_name = strategy_name
        
        # 检查新策略是否需要辅助时间周期数据
        if strategy_name and strategy_name != "不显示" and self.token_data:
            try:
                strategy_instance = StrategyFactory.get_strategy_by_name(strategy_name)
                if strategy_instance:
                    primary_timeframe = strategy_instance.get_primary_timeframe()
                    aux_requirements = strategy_instance.get_auxiliary_timeframes_and_indicators()
                    
                    if self.timeframe != primary_timeframe:
                        logger.info(f"HeadlessChartWidget[{self.widget_id}]: 策略 '{strategy_name}' 建议使用 {primary_timeframe} 时间周期")
                    
                    # 预加载辅助时间周期数据
                    if aux_requirements and self.api_service:
                        token_address = self.token_data.get('tokenAddress')
                        token_source = self.token_data.get('source', 'unknown')
                        
                        for aux_tf in aux_requirements.keys():
                            cached_aux_data = self.api_service.get_cached_ohlcv_data(token_address, aux_tf)
                            if not cached_aux_data:
                                logger.info(f"HeadlessChartWidget[{self.widget_id}]: 预加载 {aux_tf} 时间周期数据")
                                self.api_service.get_ohlcv_data_async(
                                    token_address=token_address,
                                    timeframe=aux_tf,
                                    days=self.days,
                                    source=token_source
                                )
            except Exception as e:
                logger.error(f"HeadlessChartWidget[{self.widget_id}]: 处理策略变化时出错: {e}")
        
        # 如果有数据，重新处理策略信号
        if self.df is not None and not self.df.empty:
            self.process_strategy_signals()
    
    def set_context_data(self, token_list: List[Dict], current_index: int):
        """设置上下文数据（保持接口一致性）"""
        self.context_token_list = token_list
        self.current_token_index_in_context = current_index
    
    def get_current_token_data(self) -> Optional[Dict]:
        """返回当前监控的代币数据"""
        return self.token_data
    
    def get_current_timeframe(self) -> str:
        """获取当前时间周期"""
        return self.timeframe
    
    def get_current_ohlcv_data(self) -> Optional[List[Dict]]:
        """获取当前存储的OHLCV数据
        
        返回:
            Optional[List[Dict]]: 当前的OHLCV数据列表，如果没有数据则返回None
        """
        return self.ohlcv_data if self.ohlcv_data else None
    
    def get_current_dataframe(self) -> Optional[pd.DataFrame]:
        """获取当前的DataFrame（包含技术指标）
        
        返回:
            Optional[pd.DataFrame]: 当前的DataFrame，如果没有数据则返回None
        """
        return self.df if self.df is not None and not self.df.empty else None
    
    def clear_data(self):
        """清除所有数据并重置到干净状态"""
        symbol = self.token_data.get('symbol', 'Unknown') if self.token_data else 'Unknown'
        logger.info(f"HeadlessChartWidget[{self.widget_id}]({symbol}): 清除数据")
        
        # 停止定时器
        if self.refresh_timer.isActive():
            self.refresh_timer.stop()
        
        # 清除数据
        self.token_data = None
        self.ohlcv_data = None
        self.df = None
        self.current_buy_signals.clear()
        self.current_sell_signals.clear()
    
    def stop_activity(self):
        """停止所有活动并准备删除"""
        logger.info(f"HeadlessChartWidget[{self.widget_id}]: 停止活动")
        if self.refresh_timer.isActive():
            self.refresh_timer.stop()
    
    def __del__(self):
        """析构函数"""
        try:
            widget_id = getattr(self, 'widget_id', 'Unknown')
            logger.debug(f"HeadlessChartWidget[{widget_id}]: 删除实例")
            self.stop_activity()
        except Exception as e:
            logger.debug(f"HeadlessChartWidget: 删除实例时出错: {e}")
    
    def emit_latest_signals(self):
        """发射最新的交易信号"""
        try:
            symbol = self.token_data.get('symbol', 'Unknown') if self.token_data else 'Unknown'
            
            logger.info(f"HeadlessChartWidget[{self.widget_id}]({symbol}): 开始发射信号")
            logger.info(f"HeadlessChartWidget[{self.widget_id}]({symbol}): 买入信号数量: {len(self.current_buy_signals)}")
            logger.info(f"HeadlessChartWidget[{self.widget_id}]({symbol}): 卖出信号数量: {len(self.current_sell_signals)}")
            
            # 发射最新买入信号（如果有的话）
            if self.current_buy_signals:
                latest_buy = self.current_buy_signals[-1]  # 获取最后一个买入信号
                index, price, timestamp = latest_buy
                logger.info(f"HeadlessChartWidget[{self.widget_id}]({symbol}): 发射买入信号 - 索引:{index}, 价格:${price:.6f}, 时间戳:{timestamp}")
                self.trade_signal_generated.emit('buy', price, timestamp, self.current_strategy_name, index)
                logger.info(f"HeadlessChartWidget[{self.widget_id}]({symbol}): 买入信号发射完成")
            else:
                logger.info(f"HeadlessChartWidget[{self.widget_id}]({symbol}): 没有买入信号需要发射")
            
            # 发射最新卖出信号（如果有的话）
            if self.current_sell_signals:
                latest_sell = self.current_sell_signals[-1]  # 获取最后一个卖出信号
                index, price, timestamp = latest_sell
                logger.info(f"HeadlessChartWidget[{self.widget_id}]({symbol}): 发射卖出信号 - 索引:{index}, 价格:${price:.6f}, 时间戳:{timestamp}")
                self.trade_signal_generated.emit('sell', price, timestamp, self.current_strategy_name, index)
                logger.info(f"HeadlessChartWidget[{self.widget_id}]({symbol}): 卖出信号发射完成")
            else:
                logger.info(f"HeadlessChartWidget[{self.widget_id}]({symbol}): 没有卖出信号需要发射")
                
        except Exception as e:
            symbol = self.token_data.get('symbol', 'Unknown') if self.token_data else 'Unknown'
            logger.error(f"HeadlessChartWidget[{self.widget_id}]({symbol}): 发射信号时出错: {e}", exc_info=True)
    
    def get_latest_signal_status(self) -> str:
        """获取最新的信号状态"""
        try:
            if not self.current_buy_signals and not self.current_sell_signals:
                return "无信号"
            
            # 比较最新买入和卖出信号的时间
            latest_buy_time = 0
            latest_sell_time = 0
            
            if self.current_buy_signals:
                _, _, latest_buy_time = self.current_buy_signals[-1]
            
            if self.current_sell_signals:
                _, _, latest_sell_time = self.current_sell_signals[-1]
            
            if latest_buy_time > latest_sell_time:
                return "买入信号"
            elif latest_sell_time > latest_buy_time:
                return "卖出信号"
            else:
                return "无信号"
                
        except Exception as e:
            logger.error(f"HeadlessChartWidget[{self.widget_id}]: 获取信号状态时出错: {e}")
            return "错误" 