"""
监控弹窗 - 实时显示趋势币数据和策略分析结果
"""

import logging
from datetime import datetime
from typing import Dict, List, Optional
import time

from PyQt5.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QTableWidget, QTableWidgetItem,
    QLabel, QPushButton, QHeaderView, QWidget, QTextEdit, QSplitter,
    QGroupBox, QCheckBox
)
from PyQt5.QtCore import Qt, pyqtSlot, QTimer
from PyQt5.QtGui import QFont, QColor

logger = logging.getLogger(__name__)


class MonitorDialog(QDialog):
    """监控弹窗 - 显示实时趋势币数据和策略分析结果"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("🔍 实时监控 - 趋势币数据流")
        self.setWindowFlags(Qt.Window | Qt.WindowStaysOnTopHint)
        self.resize(1200, 600)
        
        # 数据存储
        self.trend_tokens_data = []  # 趋势币数据
        self.analysis_results = {}   # 策略分析结果 {token_address: result}
        self.message_count = 0
        
        # 🔥 新增：日志频率控制
        self.log_rate_limit = 2.0  # 每秒最多2条日志
        self.last_log_time = {}    # 记录每个代币最后日志时间
        self.signal_buffer = {}    # 信号缓冲区
        self.enable_signal_logs = True  # 是否启用信号日志
        
        self.init_ui()
        self.setup_timer()
        
    def init_ui(self):
        """初始化UI"""
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # 顶部控制栏
        control_layout = QHBoxLayout()
        
        # 标题和统计
        self.title_label = QLabel("📊 实时数据监控")
        self.title_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #ecf0f1;
            }
        """)
        control_layout.addWidget(self.title_label)
        
        control_layout.addStretch()
        
        # 统计信息
        self.stats_label = QLabel("接收消息: 0 | 代币数: 0 | 分析结果: 0")
        self.stats_label.setStyleSheet("""
            QLabel {
                color: #95a5a6;
                font-size: 12px;
                padding: 5px 10px;
                background: rgba(52, 73, 94, 0.3);
                border-radius: 3px;
            }
        """)
        control_layout.addWidget(self.stats_label)
        
        # 选项
        self.auto_scroll_checkbox = QCheckBox("自动滚动")
        self.auto_scroll_checkbox.setChecked(True)
        self.auto_scroll_checkbox.setStyleSheet("""
            QCheckBox {
                color: #ecf0f1;
                font-size: 12px;
            }
        """)
        control_layout.addWidget(self.auto_scroll_checkbox)
        
        # 🔥 新增：信号日志开关
        self.signal_logs_checkbox = QCheckBox("信号日志")
        self.signal_logs_checkbox.setChecked(True)
        self.signal_logs_checkbox.setStyleSheet("""
            QCheckBox {
                color: #ecf0f1;
                font-size: 12px;
            }
        """)
        self.signal_logs_checkbox.toggled.connect(self.toggle_signal_logs)
        control_layout.addWidget(self.signal_logs_checkbox)
        
        # 清空按钮
        clear_button = QPushButton("🗑️ 清空")
        clear_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 5px 15px;
                border-radius: 3px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        clear_button.clicked.connect(self.clear_data)
        control_layout.addWidget(clear_button)
        
        main_layout.addLayout(control_layout)
        
        # 主要内容区域（分割器）
        splitter = QSplitter(Qt.Horizontal)
        
        # 左侧：代币列表
        token_group = QGroupBox("📈 代币列表")
        token_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                color: #ecf0f1;
                border: 1px solid #34495e;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        
        token_layout = QVBoxLayout()
        
        # 代币表格
        self.token_table = QTableWidget()
        self.token_table.setColumnCount(8)
        self.token_table.setHorizontalHeaderLabels([
            "代币", "策略信号", "市值", "价格变化", "持有者", "讨论数", "更新时间", "状态"
        ])
        
        # 设置表格样式
        self.token_table.setStyleSheet("""
            QTableWidget {
                background-color: #2c3e50;
                border: none;
                gridline-color: #34495e;
                font-size: 11px;
                color: #ecf0f1;
            }
            QTableWidget::item {
                padding: 5px;
                border-bottom: 1px solid #34495e;
            }
            QTableWidget::item:selected {
                background-color: #3498db;
            }
            QHeaderView::section {
                background-color: #34495e;
                color: #ecf0f1;
                padding: 5px;
                border: none;
                font-weight: bold;
            }
        """)
        
        # 设置列宽
        header = self.token_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Stretch)  # 代币名称
        header.setSectionResizeMode(1, QHeaderView.Fixed)
        header.setSectionResizeMode(2, QHeaderView.Fixed)
        header.setSectionResizeMode(3, QHeaderView.Fixed)
        header.setSectionResizeMode(4, QHeaderView.Fixed)
        header.setSectionResizeMode(5, QHeaderView.Fixed)
        header.setSectionResizeMode(6, QHeaderView.Fixed)
        header.setSectionResizeMode(7, QHeaderView.Fixed)
        
        self.token_table.setColumnWidth(1, 80)   # 策略信号
        self.token_table.setColumnWidth(2, 80)   # 市值
        self.token_table.setColumnWidth(3, 80)   # 价格变化
        self.token_table.setColumnWidth(4, 60)   # 持有者
        self.token_table.setColumnWidth(5, 60)   # 讨论数
        self.token_table.setColumnWidth(6, 120)  # 更新时间
        self.token_table.setColumnWidth(7, 60)   # 状态
        
        self.token_table.setAlternatingRowColors(True)
        self.token_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.token_table.verticalHeader().setVisible(False)
        
        token_layout.addWidget(self.token_table)
        token_group.setLayout(token_layout)
        
        # 右侧：消息日志
        log_group = QGroupBox("📝 消息日志")
        log_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                color: #ecf0f1;
                border: 1px solid #34495e;
                border-radius: 5px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        
        log_layout = QVBoxLayout()
        
        # 消息文本框
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setStyleSheet("""
            QTextEdit {
                background-color: #1a252f;
                color: #ecf0f1;
                border: none;
                font-family: Consolas, Monaco, monospace;
                font-size: 10px;
                padding: 5px;
            }
        """)
        
        log_layout.addWidget(self.log_text)
        log_group.setLayout(log_layout)
        
        # 添加到分割器
        splitter.addWidget(token_group)
        splitter.addWidget(log_group)
        splitter.setSizes([800, 400])  # 设置初始比例
        
        main_layout.addWidget(splitter)
        
        # 底部状态栏
        self.status_label = QLabel("等待数据...")
        self.status_label.setStyleSheet("""
            QLabel {
                color: #95a5a6;
                font-size: 11px;
                padding: 5px;
                background: rgba(52, 73, 94, 0.3);
                border-radius: 3px;
            }
        """)
        main_layout.addWidget(self.status_label)
        
        self.setLayout(main_layout)
        
        # 设置窗口样式
        self.setStyleSheet("""
            QDialog {
                background-color: #1a252f;
            }
        """)
    
    def setup_timer(self):
        """设置定时器"""
        # 定时更新时间显示
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_time_display)
        self.update_timer.start(1000)  # 每秒更新
        
        # 🔥 新增：批量处理日志的定时器
        self.log_batch_timer = QTimer()
        self.log_batch_timer.timeout.connect(self.process_signal_buffer)
        self.log_batch_timer.start(500)  # 每500ms批量处理一次缓冲的日志
    
    @pyqtSlot(list)
    def on_trend_data_received(self, trend_tokens: List[Dict]):
        """接收趋势币数据"""
        try:
            self.message_count += 1
            self.trend_tokens_data = trend_tokens
            
            # 记录日志
            timestamp = datetime.now().strftime("%H:%M:%S")
            self.log_text.append(f"[{timestamp}] 📊 收到趋势币数据: {len(trend_tokens)} 个代币")
            
            # 更新表格
            self.update_token_table()
            
            # 更新统计
            self.update_statistics()
            
            # 自动滚动
            if self.auto_scroll_checkbox.isChecked():
                self.log_text.verticalScrollBar().setValue(
                    self.log_text.verticalScrollBar().maximum()
                )
            
            logger.info(f"监控弹窗：收到 {len(trend_tokens)} 个趋势币数据")
            
        except Exception as e:
            logger.error(f"处理趋势币数据失败: {e}")
    
    @pyqtSlot(dict)
    def on_analysis_result_received(self, analysis_result: Dict):
        """接收策略分析结果"""
        try:
            self.message_count += 1
            
            token_address = analysis_result.get('token_address')
            symbol = analysis_result.get('symbol', 'Unknown')
            signal = analysis_result.get('signal', 'Unknown')
            strategy = analysis_result.get('strategy_name', 'Unknown')
            
            # 存储分析结果
            if token_address:
                self.analysis_results[token_address] = analysis_result
            
            # 记录日志
            timestamp = datetime.now().strftime("%H:%M:%S")
            self.log_text.append(
                f"[{timestamp}] 🎯 策略分析: {symbol} -> {signal} "
                f"(策略: {strategy})"
            )
            
            # 高亮重要信号
            if signal == "买入":
                self.log_text.append(f"    ✅ <b style='color: #27ae60;'>买入信号！</b>")
            elif signal == "卖出":
                self.log_text.append(f"    ❌ <b style='color: #e74c3c;'>卖出信号！</b>")
            
            # 更新表格中对应的行
            self.update_token_analysis(token_address, analysis_result)
            
            # 更新统计
            self.update_statistics()
            
            # 自动滚动
            if self.auto_scroll_checkbox.isChecked():
                self.log_text.verticalScrollBar().setValue(
                    self.log_text.verticalScrollBar().maximum()
                )
            
            logger.info(f"监控弹窗：收到策略分析结果 {symbol} -> {signal}")
            
        except Exception as e:
            logger.error(f"处理策略分析结果失败: {e}")
    
    @pyqtSlot(dict)
    def add_signal(self, signal_data: Dict):
        """接收并显示自定义信号日志（频率限制版本）"""
        try:
            # 检查是否启用信号日志
            if not self.enable_signal_logs:
                return
            
            token_symbol = signal_data.get('symbol', 'Unknown')
            signal_type = signal_data.get('signal_type', 'Unknown')
            current_time = time.time()
            
            # 频率限制：检查这个代币的最后日志时间
            token_key = f"{token_symbol}_{signal_type}"
            last_time = self.last_log_time.get(token_key, 0)
            
            if current_time - last_time < (1.0 / self.log_rate_limit):
                # 太频繁，缓存到buffer中
                self.signal_buffer[token_key] = {
                    'signal_data': signal_data,
                    'time': current_time,
                    'count': self.signal_buffer.get(token_key, {}).get('count', 0) + 1
                }
                return
            
            # 可以显示日志
            self._add_signal_to_log(signal_data)
            self.last_log_time[token_key] = current_time
            
        except Exception as e:
            logger.error(f"添加信号日志失败: {e}")
    
    def _add_signal_to_log(self, signal_data: Dict, is_buffered: bool = False, count: int = 1):
        """实际添加日志到显示区域"""
        try:
            self.message_count += 1
            
            # 提取信号信息
            token_symbol = signal_data.get('symbol', 'Unknown')
            signal_type = signal_data.get('signal_type', 'Unknown')
            price = signal_data.get('price', 0)
            strategy_name = signal_data.get('strategy_name', 'Unknown')
            source = signal_data.get('source', 'Unknown')
            
            # 记录到日志
            timestamp = datetime.now().strftime("%H:%M:%S")
            
            if is_buffered and count > 1:
                # 显示合并的日志
                self.log_text.append(
                    f"[{timestamp}] 🔄 更新最新信号: {token_symbol} -> {signal_type} "
                    f"(价格: ${price:.6f}, 策略: {strategy_name}, 合并: {count}次)"
                )
            else:
                self.log_text.append(
                    f"[{timestamp}] 🔄 更新最新信号: {token_symbol} -> {signal_type} "
                    f"(价格: ${price:.6f}, 策略: {strategy_name}, 来源: {source})"
                )
            
            # 根据信号类型设置不同颜色（但不要太频繁）
            if not is_buffered:
                if signal_type.lower() in ['buy', '买入']:
                    self.log_text.append(f"    ✅ <b style='color: #27ae60;'>买入信号更新</b>")
                elif signal_type.lower() in ['sell', '卖出']:
                    self.log_text.append(f"    ❌ <b style='color: #e74c3c;'>卖出信号更新</b>")
                elif signal_type.lower() in ['hold', '持有']:
                    self.log_text.append(f"    ⏸️ <b style='color: #f39c12;'>持有信号更新</b>")
            
            # 更新统计
            self.update_statistics()
            
            # 自动滚动（但不要太频繁）
            if self.auto_scroll_checkbox.isChecked():
                self.log_text.verticalScrollBar().setValue(
                    self.log_text.verticalScrollBar().maximum()
                )
            
        except Exception as e:
            logger.error(f"添加信号到日志失败: {e}")
    
    def process_signal_buffer(self):
        """批量处理缓冲的信号日志"""
        try:
            if not self.signal_buffer:
                return
            
            # 处理缓冲区中的信号
            for token_key, buffer_data in list(self.signal_buffer.items()):
                signal_data = buffer_data['signal_data']
                count = buffer_data['count']
                
                # 添加到日志（标注为合并的）
                self._add_signal_to_log(signal_data, is_buffered=True, count=count)
                
                # 更新最后日志时间
                token_symbol = signal_data.get('symbol', 'Unknown')
                signal_type = signal_data.get('signal_type', 'Unknown')
                self.last_log_time[f"{token_symbol}_{signal_type}"] = time.time()
            
            # 清空缓冲区
            self.signal_buffer.clear()
            
        except Exception as e:
            logger.error(f"处理信号缓冲区失败: {e}")
    
    def toggle_signal_logs(self, enabled: bool):
        """切换信号日志开关"""
        self.enable_signal_logs = enabled
        if enabled:
            timestamp = datetime.now().strftime("%H:%M:%S")
            self.log_text.append(f"[{timestamp}] ✅ 信号日志已启用")
        else:
            timestamp = datetime.now().strftime("%H:%M:%S")
            self.log_text.append(f"[{timestamp}] ❌ 信号日志已禁用")
            # 清空缓冲区
            self.signal_buffer.clear()
    
    def update_token_table(self):
        """更新代币表格"""
        try:
            # 清空现有数据
            self.token_table.setRowCount(0)
            
            # 添加新数据
            for token in self.trend_tokens_data:
                row = self.token_table.rowCount()
                self.token_table.insertRow(row)
                
                # 代币信息
                symbol = token.get('symbol', 'Unknown')
                name = token.get('name', '')
                token_text = f"{symbol} ({name[:20]}...)" if len(name) > 20 else f"{symbol} ({name})"
                token_item = QTableWidgetItem(token_text)
                token_item.setToolTip(f"{symbol}\n{name}")
                self.token_table.setItem(row, 0, token_item)
                
                # 策略信号（从分析结果获取）
                token_address = token.get('tokenAddress')
                analysis = self.analysis_results.get(token_address, {})
                signal = analysis.get('signal', '等待分析')
                signal_item = QTableWidgetItem(signal)
                
                # 设置信号颜色
                if signal == "买入":
                    signal_item.setForeground(QColor('#27ae60'))
                    signal_item.setFont(QFont("Arial", 10, QFont.Bold))
                elif signal == "卖出":
                    signal_item.setForeground(QColor('#e74c3c'))
                    signal_item.setFont(QFont("Arial", 10, QFont.Bold))
                elif signal == "持有":
                    signal_item.setForeground(QColor('#f39c12'))
                    signal_item.setFont(QFont("Arial", 10, QFont.Bold))
                else:
                    signal_item.setForeground(QColor('#95a5a6'))
                
                self.token_table.setItem(row, 1, signal_item)
                
                # 市值
                market_cap = token.get('marketCap', 0)
                if market_cap >= 1000000:
                    cap_text = f"${market_cap/1000000:.1f}M"
                elif market_cap >= 1000:
                    cap_text = f"${market_cap/1000:.1f}K"
                else:
                    cap_text = f"${market_cap:.0f}"
                self.token_table.setItem(row, 2, QTableWidgetItem(cap_text))
                
                # 价格变化
                price_change = token.get('priceChange5m', 0)
                change_text = f"{price_change:+.1f}%" if isinstance(price_change, (int, float)) else "N/A"
                change_item = QTableWidgetItem(change_text)
                if isinstance(price_change, (int, float)):
                    if price_change > 0:
                        change_item.setForeground(QColor('#27ae60'))
                    elif price_change < 0:
                        change_item.setForeground(QColor('#e74c3c'))
                self.token_table.setItem(row, 3, change_item)
                
                # 持有者
                holders = token.get('holders', 0)
                self.token_table.setItem(row, 4, QTableWidgetItem(str(holders)))
                
                # 讨论数
                tweet_count = token.get('tweetCount', 0)
                self.token_table.setItem(row, 5, QTableWidgetItem(str(tweet_count)))
                
                # 更新时间
                update_time = datetime.now().strftime("%m-%d %H:%M:%S")
                self.token_table.setItem(row, 6, QTableWidgetItem(update_time))
                
                # 状态
                status_text = "活跃" if tweet_count > 10 else "普通"
                status_item = QTableWidgetItem(status_text)
                if status_text == "活跃":
                    status_item.setForeground(QColor('#3498db'))
                self.token_table.setItem(row, 7, status_item)
                
        except Exception as e:
            logger.error(f"更新代币表格失败: {e}")
    
    def update_token_analysis(self, token_address: str, analysis_result: Dict):
        """更新特定代币的分析结果"""
        try:
            # 在表格中查找对应的代币
            for row in range(self.token_table.rowCount()):
                # 需要从趋势数据中匹配
                if row < len(self.trend_tokens_data):
                    token = self.trend_tokens_data[row]
                    if token.get('tokenAddress') == token_address:
                        # 更新策略信号列
                        signal = analysis_result.get('signal', 'Unknown')
                        signal_item = QTableWidgetItem(signal)
                        
                        # 设置颜色
                        if signal == "买入":
                            signal_item.setForeground(QColor('#27ae60'))
                            signal_item.setFont(QFont("Arial", 10, QFont.Bold))
                        elif signal == "卖出":
                            signal_item.setForeground(QColor('#e74c3c'))
                            signal_item.setFont(QFont("Arial", 10, QFont.Bold))
                        elif signal == "持有":
                            signal_item.setForeground(QColor('#f39c12'))
                            signal_item.setFont(QFont("Arial", 10, QFont.Bold))
                        else:
                            signal_item.setForeground(QColor('#95a5a6'))
                        
                        self.token_table.setItem(row, 1, signal_item)
                        
                        # 更新时间
                        update_time = datetime.now().strftime("%m-%d %H:%M:%S")
                        self.token_table.setItem(row, 6, QTableWidgetItem(update_time))
                        
                        break
                        
        except Exception as e:
            logger.error(f"更新代币分析结果失败: {e}")
    
    def update_statistics(self):
        """更新统计信息"""
        try:
            token_count = len(self.trend_tokens_data)
            analysis_count = len(self.analysis_results)
            
            self.stats_label.setText(
                f"接收消息: {self.message_count} | "
                f"代币数: {token_count} | "
                f"分析结果: {analysis_count}"
            )
            
            # 更新状态
            self.status_label.setText(
                f"最后更新: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} | "
                f"监控中..."
            )
            
        except Exception as e:
            logger.error(f"更新统计信息失败: {e}")
    
    def update_time_display(self):
        """更新时间显示"""
        # 更新表格中的相对时间等
        pass
    
    @pyqtSlot()
    def clear_data(self):
        """清空数据"""
        self.trend_tokens_data.clear()
        self.analysis_results.clear()
        self.message_count = 0
        self.token_table.setRowCount(0)
        self.log_text.clear()
        self.update_statistics()
        logger.info("监控弹窗：已清空所有数据")
    
    def closeEvent(self, event):
        """关闭事件"""
        self.update_timer.stop()
        self.log_batch_timer.stop()  # 🔥 新增：停止批量处理定时器
        event.accept() 