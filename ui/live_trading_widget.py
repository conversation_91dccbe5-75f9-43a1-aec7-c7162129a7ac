"""
实盘交易组件 - 趋势币监控和交易执行
"""

import logging
import requests
import time
import json
import threading
from typing import Dict, List, Optional
from datetime import datetime, timedelta
import os

# 🔥 修改日志配置逻辑，使其响应 ENABLE_LOGGING
logger = logging.getLogger(__name__) # 获取logger实例，__name__ 通常是 'ui.live_trading_widget'

enable_logging_env = os.getenv('ENABLE_LOGGING', '0')

if enable_logging_env == '1':
    logger.setLevel(logging.INFO) # 或 logging.DEBUG 如果需要更详细的日志
    # 我们假设根logger已由main.py中的basicConfig配置了handler和合适的级别
    # logger.info(f"Logger for '{__name__}' set to INFO level based on ENABLE_LOGGING=1")
else:
    # 如果不开启日志，可以将此特定logger的级别设置得很高，或者完全禁用它
    logger.setLevel(logging.CRITICAL + 1) 
    # 或者 logger.addHandler(logging.NullHandler()) # 另一种不输出的方式

logger = logging.getLogger(__name__) 
enable_logging_env = os.getenv('ENABLE_LOGGING', '0')
if enable_logging_env == '1':
    logger.setLevel(logging.INFO) 
else:
    logger.setLevel(logging.CRITICAL + 1) 

# 移除或注释掉下面这些全局修改根logger或强制设置本模块logger为CRITICAL的行
# logging.getLogger().setLevel(logging.CRITICAL)
# logger.setLevel(logging.CRITICAL) # 这行也注释掉，因为上面已经根据环境变量设置了

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QComboBox,
    QTableWidget, QTableWidgetItem, QHeaderView, QCheckBox, QSplitter,
    QGroupBox, QFormLayout, QSpacerItem, QSizePolicy, QMessageBox,
    QProgressBar, QTextEdit, QFrame, QDoubleSpinBox, QGridLayout,
    QDialog, QLineEdit, QDialogButtonBox, QMenu, QAction, QApplication,
    QShortcut, QSpinBox
)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QThread, pyqtSlot, QDateTime, QUrl
from PyQt5.QtGui import QFont, QColor, QCursor, QPixmap, QPainter, QPainterPath, QIcon, QKeySequence
from PyQt5.QtNetwork import QNetworkAccessManager, QNetworkRequest, QNetworkReply
from PyQt5.QtGui import QPixmapCache

# 导入策略和指标模块
from strategies import StrategyFactory
from indicators import TechnicalIndicators
from api_service import APIService
from ui.chart_widget import ChartWidget
from ui.styles import LiveTradingStyles
from ui.live_trading_styles import (
    WALLET_BUTTON_CONNECTED_STYLE, WALLET_BUTTON_DEFAULT_STYLE,
    TOKEN_BALANCE_LABEL_SUCCESS_STYLE, TOKEN_BALANCE_LABEL_ERROR_STYLE,
    TOKEN_BALANCE_LABEL_NEUTRAL_STYLE, TOKEN_BALANCE_LABEL_DEFAULT_STYLE,
    TRADING_TOGGLE_ON_STYLE, TRADING_TOGGLE_OFF_STYLE,
    PARALLEL_ANALYSIS_BUTTON_STYLE,
    TOKEN_BALANCE_LABEL_INITIAL_STYLE,
    REFRESH_BALANCE_BUTTON_STYLE,
    TREND_COINS_REFRESH_BUTTON_STYLE, 
    TREND_COINS_STATUS_LABEL_STYLE,
    TREND_COINS_LAST_UPDATE_LABEL_STYLE,
    TREND_TEXT_DISPLAY_STYLE,
    TEST_CHART_BUTTON_STYLE, TRADE_RECORDS_TABLE_STYLE # ADDED
) # ADDED
from ui.monitor_dialog import MonitorDialog  # 🔥 新增：导入监控弹窗
from ui.holdings_panel_widget import HoldingsPanelWidget 
from .widgets.token_image_label import TokenImageLabel # ADDED
from .widgets.token_info_widget import TokenInfoWidget # ADDED
from .widgets.sell_confirm_dialog import SellConfirmDialog # ADDED
from .widgets.numeric_table_widget_item import NumericTableWidgetItem # ADDED

from .threads.trend_data_thread import TrendDataThread
from .threads.token_balance_query_thread import TokenBalanceQueryThread

import pandas as pd

from okx_dex_client import OKXDexClient, QuoteRequest, SwapRequest
from config import PORTFOLIO_CONFIG
from services.wallet_service import WalletService
from utils import normalize_holdings_for_background_charts  # 🔥 新增：导入持仓数据标准化工具
REAL_TRADING_AVAILABLE = True

# 🔥🔥 新增：导入HeadlessChartWidget
from .headless_chart_widget import HeadlessChartWidget


class LiveTradingWidget(QWidget):
    """实盘交易主组件"""
    
    # 🔥🔥 常量定义
    MAX_DISPLAY_TOKENS = 20  # 固定显示的最大代币数量（按讨论数排序的前30个）
    APPEND_TOKENS_COUNT = 10  # 每次加载的代币数量
    TOTAL_TOKENS_COUNT = MAX_DISPLAY_TOKENS + APPEND_TOKENS_COUNT   # 总代币数量
    BACKGROUND_REFRESH_INTERVAL = 30000 # 后台图表刷新间隔 (ms), e.g., 30 seconds
    
    # 🔥 添加信号，用于共享趋势币数据
    trend_data_shared = pyqtSignal(list)  # 共享趋势币数据给监控弹窗
    strategy_analysis_shared = pyqtSignal(dict)  # 🆕新增：策略分析结果共享
    
    # 🔥🔥 新增：统一信号收集器信号
    all_signals_collected = pyqtSignal(dict)  # 所有信号的汇总数据
    new_signal_received = pyqtSignal(dict)    # 新收到的单个信号
    signal_statistics_updated = pyqtSignal(dict)  # 信号统计更新

    def __init__(self, parent=None):
        super().__init__(parent)
                            
        self.wallet_service: Optional[WalletService] = None
        self.dex_client: Optional[OKXDexClient] = None
        # Ensure self.logger is initialized before this block if it's not already
        if not hasattr(self, 'logger') or self.logger is None:
            self.logger = logging.getLogger(__name__) # Basic fallback

        try:
            # print("DEBUG: LiveTradingWidget.__init__ - CHECKPOINT 2: Entered WalletService initialization TRY block.") # REMOVED
            from config import PORTFOLIO_CONFIG
            from okx_dex_client import OKXDexClient
            # from services.wallet_service import WalletService # Already imported at class level if needed, or keep if only here.

            if "okx_dex_api_url" not in PORTFOLIO_CONFIG:
                # print("DEBUG: LiveTradingWidget.__init__ - ERROR: 'okx_dex_api_url' not in PORTFOLIO_CONFIG.") # REMOVED
                raise KeyError("'okx_dex_api_url' not in PORTFOLIO_CONFIG")

            # print(f"DEBUG: LiveTradingWidget.__init__ - CHECKPOINT 2.1: PORTFOLIO_CONFIG['okx_dex_api_url'] = {PORTFOLIO_CONFIG.get('okx_dex_api_url')}") # REMOVED

            self.dex_client = OKXDexClient(PORTFOLIO_CONFIG["okx_dex_api_url"])
            
            # print("DEBUG: LiveTradingWidget.__init__ - CHECKPOINT 2.2: OKXDexClient initialized.") # REMOVED
            self.wallet_service = WalletService(dex_client=self.dex_client) # WalletService should be imported
            # print("DEBUG: LiveTradingWidget.__init__ - CHECKPOINT 2.3: WalletService initialized.") # REMOVED
            
            log_message_success = "WalletService 和 OKXDexClient 初始化成功。"
            if hasattr(self, 'add_message_to_log'):
                self.add_message_to_log(log_message_success, "info")
            else:
                self.logger.info(log_message_success)
            # print(f"DEBUG: LiveTradingWidget.__init__ - SUCCESS: {log_message_success}") # REMOVED

        except ImportError as e:
            # print(f"DEBUG: LiveTradingWidget.__init__ - CHECKPOINT 3: Caught ImportError: {e}") # REMOVED
            self.dex_client = None
            self.wallet_service = None
            log_message_error = f"初始化 WalletService 失败: 无法导入模块 - {e}"
            if hasattr(self, 'add_message_to_log'):
                self.add_message_to_log(log_message_error, "error")
            else:
                self.logger.error(log_message_error)
        except KeyError as e:
            # print(f"DEBUG: LiveTradingWidget.__init__ - CHECKPOINT 4: Caught KeyError: {e}") # REMOVED
            self.dex_client = None
            self.wallet_service = None
            log_message_error = f"初始化 WalletService 失败: PORTFOLIO_CONFIG 中缺少键 - {e}"
            if hasattr(self, 'add_message_to_log'):
                self.add_message_to_log(log_message_error, "error")
            else:
                self.logger.error(log_message_error)
        except Exception as e:
            # print(f"DEBUG: LiveTradingWidget.__init__ - CHECKPOINT 5: Caught generic Exception: {e}") # REMOVED
            self.dex_client = None
            self.wallet_service = None
            log_message_error = f"初始化 WalletService 时发生未知错误: {e}"
            if hasattr(self, 'add_message_to_log'):
                self.add_message_to_log(log_message_error, "error")
            else:
                self.logger.error(log_message_error, exc_info=True)


        # 🔥🔥 新增：跟踪每个代币的最新交易信号
        self.latest_token_signals = {}  # {token_address: {'signal': 'buy/sell', 'timestamp': int, 'price': float, 'update_time': str}}
        
        # 🔥🔥 新增：统一信号收集器
        self.all_chart_signals = {}  # 所有ChartWidget的信号历史
        self.signal_statistics = {
            'total_signals': 0,
            'buy_signals': 0,
            'sell_signals': 0,
            'active_tokens': 0,
            'last_update': None
        }

        self.holdings_panel = HoldingsPanelWidget(self) # 新增持仓面板实例
        
        # 🔥🔥 唯一信号管理（防止重复信号干扰）
        self.unique_signals = {}  # {unique_key: {'signal_type': str, 'price': float, 'timestamp': int, 'processed': bool}}
        
        # 🔥🔥 新增：消息去重机制
        self.last_strategy_analysis = {}  # {token_address: {'signal': str, 'timestamp': float}}
        self.last_signal_log = {}  # {unique_key: timestamp} 用于控制新信号日志频率
        self.signal_log_cooldown = 30  # 同一个信号的日志冷却时间（秒）
        self.strategy_analysis_cooldown = 60  # 同一个策略分析结果的日志冷却时间（秒）
        
        # 📊 创建 APIService 实例
        self.api_service = APIService()
        
        # 钱包连接状态
        self.wallet_connected = False
        self.wallet_address = ""
        
        self.trend_data_thread = None
        self.trend_tokens = []
        
        # 🔥 持仓列表变量 - 供其他地方使用
        self.holdings_list_raw = []  # 原始持仓数据（未处理）
        self.holdings_list_full = []  # 完整持仓数据（包含所有字段）
        self.holdings_list_normalized = []  # 标准化后的持仓数据（用于后台图表）
        self.holdings_list_filtered = []  # 过滤后的持仓数据（例如去除小额持仓）
        
        self.selected_strategy = None
        self.trading_enabled = False
        self.market_monitoring = True
        self.current_chart_token = None  # 当前图表显示的代币
        self.current_token_balance = 0  # 当前代币余额
        
        # 🔥🔥 修改：用于存储后台运行的 HeadlessChartWidget 实例
        # self.background_chart_widgets = {}  # {token_address: ChartWidget} - OLD
        self.background_chart_pool: List[HeadlessChartWidget] = [] # Pool of HeadlessChartWidget instances
        self.active_background_charts: Dict[str, HeadlessChartWidget] = {} # token_address -> HeadlessChartWidget_instance
        
        # 🔥🔥 移除旧的并行分析定时器，ChartWidget 将自行管理
        # self.parallel_analysis_timer = QTimer() 
        
        self.execution_thread = None
        self.current_execution_result = None
        
        self.trade_service = None
        
        self.monitor_dialog = None
        
        self.trade_records = []
        self.trade_record_id_counter = 0
        
        # self.okx_client 已被 self.dex_client 替代，避免重复
        
        # 🔥 新增：初始化消息日志相关属性
        self.message_log_count = 0
        self.enable_local_signal_logs = True
        
        # 🔥 新增：Webhook管理器
        from .webhook_manager import WebhookManager
        self.webhook_manager = WebhookManager(self)
        self.webhook_manager.status_updated.connect(self.update_webhook_status_display)
        
        # 🔥🔥 新增：后台监控管理器
        from .background_monitor_manager import BackgroundMonitorManager
        self.background_monitor_manager = BackgroundMonitorManager(self.api_service, self)
        self.background_monitor_manager.data_batch_ready.connect(self.on_background_data_ready)
        
        # 🔥🔥 新增：性能监控器
        from .performance_monitor import PerformanceMonitor
        self.performance_monitor = PerformanceMonitor(self)
        
        # 🔥🔥 连接性能监控器到APIService
        if self.api_service:
            self.api_service.performance_monitor = self.performance_monitor
        
        self.init_ui()
        self.setup_timer() # This timer is for LiveTradingWidget's own trend data refresh
        self.load_initial_data()
        self._initialize_background_chart_pool() # 🔥 Initialize the pool

        # 🔥🔥 新增：连接持仓面板代币选择信号
        self.holdings_panel.holding_selected.connect(self.on_holding_panel_token_selected)

        # 🔥 强制设置线程优先级
        import threading
        threading.current_thread().setName("MainUI")
    
    def _initialize_background_chart_pool(self):
        """Creates and initializes the pool of background HeadlessChartWidget instances."""
        logger.info(f"🚀 Initializing background HeadlessChartWidget pool with {self.TOTAL_TOKENS_COUNT} instances...")
        for i in range(self.TOTAL_TOKENS_COUNT):
            chart_widget = HeadlessChartWidget(api_service=self.api_service, widget_id=f"BackgroundChart_Pool_{i}", parent=self)
            chart_widget.setObjectName(f"BackgroundChart_Pool_{i}")
            chart_widget.is_background_parallel = True
            chart_widget.auto_refresh = True # Enable auto-refresh within HeadlessChartWidget if desired
            
            # 🔥🔥 新增：强制启用并保持自动刷新，防止被意外停止
            chart_widget.force_auto_refresh = True  # 添加标记，防止定时器被停止
            
            # Connect signals
            chart_widget.trade_signal_generated.connect(self.on_background_chart_signal)
            # 🔥🔥 重要：连接策略分析完成信号，否则界面会一直显示"分析中..."
            chart_widget.strategy_analysis_completed.connect(self.on_strategy_analysis_completed)
            # chart_widget.status_updated.connect(lambda msg, i=i: self.on_background_chart_status_pool(i, msg))
            
            self.background_chart_pool.append(chart_widget)
        logger.info(f"✅ Background HeadlessChartWidget pool initialized with {len(self.background_chart_pool)} instances.")

    def init_ui(self):
        """初始化UI"""
        main_layout = QVBoxLayout()
        main_layout.setContentsMargins(5, 5, 5, 5)
        main_layout.setSpacing(5)
        
        # 🔥🔥 新增：添加快捷键支持
        from PyQt5.QtWidgets import QShortcut
        from PyQt5.QtGui import QKeySequence
        
        # Ctrl+Shift+P: 获取未处理信号
        unprocessed_signals_shortcut = QShortcut(QKeySequence("Ctrl+Shift+P"), self)
        unprocessed_signals_shortcut.activated.connect(self.show_latest_unprocessed_signals)

        # Ctrl+Shift+K: 查看钱包前代币 地址 符号 余额 rawBalance
        wallet_balance_shortcut = QShortcut(QKeySequence("Ctrl+Shift+K"), self)
        wallet_balance_shortcut.activated.connect(self.show_wallet_balance_shortcut)
        
        # 顶部控制面板
        control_panel = self.create_control_panel()
        main_layout.addWidget(control_panel)
        
        # 主要内容区域（左右分割）
        content_splitter = QSplitter(Qt.Horizontal)
        content_splitter.setStyleSheet(LiveTradingStyles.SPLITTER_HORIZONTAL)
        
        # 左侧：趋势币列表 和 未处理信号列表 (使用新的垂直分割器)
        left_vertical_splitter = QSplitter(Qt.Vertical)
        left_vertical_splitter.setStyleSheet(LiveTradingStyles.SPLITTER_VERTICAL)

        trend_group = self.create_trend_coins_panel()
        left_vertical_splitter.addWidget(trend_group)

        unprocessed_signals_group = self.create_unprocessed_signals_panel() # 🔥 新增调用
        left_vertical_splitter.addWidget(unprocessed_signals_group)

        # 设置左侧垂直分割器的比例 (例如，趋势币 65%，未处理信号 35%)
        left_vertical_splitter.setSizes([650, 350]) # 调整这些值以适应内容
        
        content_splitter.addWidget(left_vertical_splitter) # 🔥 将左侧垂直分割器添加到主水平分割器
        
        # 右侧：创建垂直分割器
        right_splitter = QSplitter(Qt.Vertical)
        right_splitter.setStyleSheet(LiveTradingStyles.SPLITTER_VERTICAL)
        
        # 右侧上部：K线图表
        chart_group = self.create_chart_panel()
        
        right_splitter.addWidget(chart_group)
        
        # 右侧下部：信号记录与消息日志的水平分割
        bottom_splitter = QSplitter(Qt.Horizontal)
        bottom_splitter.setStyleSheet(LiveTradingStyles.SPLITTER_HORIZONTAL if hasattr(LiveTradingStyles, 'SPLITTER_HORIZONTAL') else LiveTradingStyles.SPLITTER_VERTICAL)
        
        # 左侧：策略信号记录
        trade_records_group = self.create_trade_records_panel()
        bottom_splitter.addWidget(trade_records_group)
        
        # 右侧：消息日志
        message_log_group = self.create_message_log_panel()
        bottom_splitter.addWidget(message_log_group)
        
        # 设置水平分割比例：策略信号记录 2/3，消息日志 1/3
        bottom_splitter.setSizes([400, 200])  # 2:1 的比例
        
        right_splitter.addWidget(bottom_splitter)
        
        # 设置右侧垂直分割比例（60:40）
        right_splitter.setSizes([600, 400])
        
        # 将右侧分割器添加到主分割器
        content_splitter.addWidget(right_splitter)
        
        # 设置主分割比例：左侧趋势币45%，右侧55%
        # 使用固定像素值而不是百分比，确保趋势币表格有足够宽度
        content_splitter.setSizes([500, 700])  # 左侧500px，右侧700px
        content_splitter.setStretchFactor(0, 1)  # 左侧可拉伸
        content_splitter.setStretchFactor(1, 2)  # 右侧拉伸因子更大
        main_layout.addWidget(content_splitter)
        
        # 🔥🔥 旧的并行图表容器不再需要，ChartWidget实例不可见，但有父级
        # self.parallel_charts_container = QWidget()
        # self.parallel_charts_container.setVisible(False)  # 隐藏容器
        # self.parallel_charts_container.setParent(self)  # 设置父组件
        
        # 底部状态栏
        status_container = QWidget()
        status_layout = QHBoxLayout(status_container)
        status_layout.setContentsMargins(0, 0, 0, 0)
        status_layout.setSpacing(10)
        
        # 状态标签
        self.status_label = QLabel("准备就绪")
        self.status_label.setStyleSheet(LiveTradingStyles.STATUS_LABEL)
        self.status_label.setMaximumHeight(20)  # 确保只有一行字的高度
        status_layout.addWidget(self.status_label)
        
        status_layout.addStretch()
        
        # 🔥 最后数据更新时间标签
        self.last_update_label = QLabel("数据更新: 未更新")
        self.last_update_label.setStyleSheet("""
            QLabel {
                color: #95a5a6;
                font-size: 10px;
                font-weight: normal;
                padding: 2px 8px;
                background: rgba(52, 73, 94, 0.3);
                border: 1px solid #34495e;
                border-radius: 3px;
            }
        """)
        self.last_update_label.setToolTip("最后一次获取趋势币数据的时间")
        status_layout.addWidget(self.last_update_label)
        
        # 🔥🔥 并行分析状态标签
        self.parallel_analysis_label = QLabel("并行分析: 未启动")
        self.parallel_analysis_label.setStyleSheet("""
            QLabel {
                color: #e67e22;
                font-size: 10px;
                font-weight: bold;
                padding: 2px 8px;
                background: rgba(230, 126, 34, 0.1);
                border: 1px solid #e67e22;
                border-radius: 3px;
            }
        """)
        self.parallel_analysis_label.setToolTip("并行图表分析系统状态")
        status_layout.addWidget(self.parallel_analysis_label)
        
        main_layout.addWidget(status_container)
        
        self.setLayout(main_layout)
        
        # 🔥🔥 关键修复：初始化timeframe属性，从timeframe_combo获取当前值
        self.timeframe = self.timeframe_combo.currentText()
        print(f"🔥 初始化timeframe属性: {self.timeframe}")
        
        # 在UI完全初始化后设置默认策略
        self.set_default_strategy()
        
        # 在UI初始化完成后，触发默认策略选择
        if self.strategy_combo.currentText() != "选择策略...":
            self.on_strategy_changed()
        
        # 🔥 新增：更新Webhook状态显示
        self.update_webhook_status_display(self.webhook_manager.get_status_text(), "normal")
    
    def set_default_strategy(self):
        """设置默认策略"""
        # 🔥 设置默认策略为VWAP交叉策略
        default_strategy_name = "📊 VWAP 交叉策略"
        if self.strategy_combo.findText(default_strategy_name) != -1:
            self.strategy_combo.setCurrentText(default_strategy_name)
            # 移除立即触发策略选择事件，改为在init_ui完成后调用
            # self.on_strategy_changed()
        
        self.strategy_combo.currentTextChanged.connect(self.on_strategy_changed)
    
    def create_control_panel(self) -> QWidget:
        """创建顶部控制面板"""
        panel = QWidget()
        panel.setStyleSheet(LiveTradingStyles.CONTROL_PANEL)
        panel.setMaximumHeight(40)  # 限制控制面板高度
        
        layout = QHBoxLayout()
        layout.setContentsMargins(8, 5, 8, 5)
        layout.setSpacing(12)
        
        # 策略选择
        strategy_label = QLabel("策略:")
        strategy_label.setStyleSheet(LiveTradingStyles.LABEL_CONTROL)
        self.strategy_combo = QComboBox()
        self.strategy_combo.setStyleSheet(LiveTradingStyles.COMBOBOX_DEFAULT)
        
        # 添加策略选项
        self.strategy_combo.addItem("选择策略...")
        
        # 从StrategyFactory获取所有可用策略
        available_strategies = StrategyFactory.get_all_strategies()
        for strategy in available_strategies:
            self.strategy_combo.addItem(f"📊 {strategy.name}")
        
        # 注意：不在这里连接信号，因为set_default_strategy方法会处理
        
        layout.addWidget(strategy_label)
        layout.addWidget(self.strategy_combo)
        
        # 添加分隔符
        separator1 = self.create_separator()
        layout.addWidget(separator1)
        
        # 关注市场选项
        self.market_monitor_checkbox = QCheckBox("关注热榜")
        self.market_monitor_checkbox.setStyleSheet(LiveTradingStyles.CHECKBOX_MARKET_MONITOR)
        self.market_monitor_checkbox.setChecked(True)
        self.market_monitor_checkbox.stateChanged.connect(self.on_market_monitoring_changed)
        layout.addWidget(self.market_monitor_checkbox)
        
        # 添加分隔符
        separator2 = self.create_separator()
        layout.addWidget(separator2)
        
        # 交易开关
        self.trading_toggle = QPushButton("🔴 交易：关")
        self.trading_toggle.setCheckable(True)
        self.trading_toggle.setStyleSheet(TRADING_TOGGLE_OFF_STYLE)
        self.trading_toggle.clicked.connect(self.on_trading_toggle)
        layout.addWidget(self.trading_toggle)
        
        # 手动刷新按钮
        refresh_button = QPushButton("🔄")
        refresh_button.setStyleSheet(LiveTradingStyles.BUTTON_REFRESH)
        refresh_button.clicked.connect(self.refresh_trend_data)
        refresh_button.setToolTip("手动刷新趋势数据")
        layout.addWidget(refresh_button)
        
        # 🔥🔥 新增：手动触发并行分析按钮
        parallel_analysis_button = QPushButton("⚡")
        parallel_analysis_button.setStyleSheet(PARALLEL_ANALYSIS_BUTTON_STYLE)
        parallel_analysis_button.clicked.connect(self.manual_trigger_parallel_analysis)
        parallel_analysis_button.setToolTip("手动触发所有代币的并行图表分析")
        layout.addWidget(parallel_analysis_button)
        
        # 添加分隔符
        separator3 = self.create_separator()
        layout.addWidget(separator3)
        
        # 策略执行相关控件
        self.initial_capital_spin = QDoubleSpinBox()
        self.initial_capital_spin.setRange(100.0, 1_000_000.0)
        self.initial_capital_spin.setValue(10000.0)
        self.initial_capital_spin.setDecimals(0)
        self.initial_capital_spin.setSuffix(" $")
        self.initial_capital_spin.setStyleSheet(LiveTradingStyles.SPINBOX_CAPITAL)
        self.initial_capital_spin.setToolTip("设置策略执行的初始资金")
        layout.addWidget(self.initial_capital_spin)
        
        # 添加分隔符
        separator4 = self.create_separator()
        layout.addWidget(separator4)
        
        # SOL金额选择
        sol_amount_label = QLabel("SOL:")
        sol_amount_label.setStyleSheet(LiveTradingStyles.LABEL_CONTROL)
        self.sol_amount_combo = QComboBox()
        self.sol_amount_combo.setStyleSheet(LiveTradingStyles.COMBOBOX_SOL_AMOUNT)
        
        # 添加SOL金额预选项
        self.sol_amount_combo.addItems(["0.1", "0.3", "0.5", "1.0"])
        self.sol_amount_combo.setCurrentText("0.1")
        self.sol_amount_combo.setToolTip("选择买入的SOL数量")
        
        # 买入按钮
        self.buy_button = QPushButton("💰 买入")
        self.buy_button.setStyleSheet(LiveTradingStyles.BUTTON_BUY)
        self.buy_button.setEnabled(False)
        
        # 🔥 添加详细的事件处理和调试
        def debug_mouse_press(event):
            logger.info(f"🔥 买入按钮鼠标点击事件: enabled={self.buy_button.isEnabled()}")
            logger.info(f"🔥 当前chart_token: {self.current_chart_token}")
            logger.info(f"🔥 钱包连接状态: {self.wallet_connected}")
            return QPushButton.mousePressEvent(self.buy_button, event)
        
        # 绑定鼠标事件
        self.buy_button.mousePressEvent = debug_mouse_press
        
        # 连接点击信号
        self.buy_button.clicked.connect(self.buy_token)
        self.buy_button.setToolTip("使用指定SOL数量买入当前选择的代币")
        
        # 卖出按钮（支持下拉菜单）
        self.sell_button = QPushButton("💸 卖出")
        self.sell_button.setStyleSheet(LiveTradingStyles.BUTTON_SELL)
        self.sell_button.setEnabled(False)
        self.sell_button.clicked.connect(self.sell_token)
        self.sell_button.setToolTip("点击选择卖出比例（需要有余额）")
        
        # 创建卖出菜单
        self.sell_menu = QMenu(self.sell_button)
        
        # 添加卖出比例选项
        percentages = [10, 25, 50, 75, 100]
        for percentage in percentages:
            action = QAction(f"卖出 {percentage}%", self.sell_button)
            action.triggered.connect(
                lambda checked, p=percentage: self.sell_token_percentage(p)
            )
            self.sell_menu.addAction(action)
        
        # 设置按钮菜单
        self.sell_button.setMenu(self.sell_menu)
        
        # 钱包连接按钮
        self.wallet_button = QPushButton("🔗 连接钱包")
        self.wallet_button.setStyleSheet(LiveTradingStyles.BUTTON_WALLET)
        self.wallet_button.clicked.connect(self.connect_wallet)
        self.wallet_button.setToolTip("连接 Solana 钱包进行真实交易")
        
        # 💰 代币余额显示
        self.token_balance_label = QLabel("余额: -")
        self.token_balance_label.setStyleSheet(TOKEN_BALANCE_LABEL_INITIAL_STYLE)
        self.token_balance_label.setToolTip("当前选中代币的钱包余额")
        
        # 余额刷新按钮
        self.refresh_balance_button = QPushButton("🔄")
        self.refresh_balance_button.setStyleSheet(REFRESH_BALANCE_BUTTON_STYLE)
        self.refresh_balance_button.clicked.connect(self.refresh_token_balance)
        self.refresh_balance_button.setToolTip("刷新代币余额")
        
        # 🔥 调试按钮 - 检查买入按钮状态
        self.debug_buy_button = QPushButton("🔍")
        self.debug_buy_button.setStyleSheet("""
            QPushButton {
                background-color: #9b59b6;
                color: #ecf0f1;
                border: 1px solid #8e44ad;
                border-radius: 3px;
                padding: 4px 8px;
                font-size: 11px;
                font-weight: bold;
                max-width: 30px;
                min-height: 22px;
            }
            QPushButton:hover {
                background-color: #8e44ad;
                border-color: #7d3c98;
            }
        """)
        self.debug_buy_button.clicked.connect(self.debug_buy_button_state)
        self.debug_buy_button.setToolTip("调试买入按钮状态")
        
        layout.addWidget(self.wallet_button)
        layout.addWidget(sol_amount_label)
        layout.addWidget(self.sol_amount_combo)
        layout.addWidget(self.buy_button)
        layout.addWidget(self.sell_button)
        layout.addWidget(self.token_balance_label)
        layout.addWidget(self.refresh_balance_button)
        layout.addWidget(self.debug_buy_button)
        
        # 🔥🔥 性能监控快捷按钮
        self.perf_quick_button = QPushButton("📊")
        self.perf_quick_button.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                border: 1px solid #e67e22;
                border-radius: 3px;
                padding: 4px 8px;
                font-size: 11px;
                font-weight: bold;
                max-width: 30px;
                min-height: 22px;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        self.perf_quick_button.clicked.connect(self.show_performance_dialog)
        self.perf_quick_button.setToolTip("查看实时性能监控")
        layout.addWidget(self.perf_quick_button)
        
        layout.addStretch()
        panel.setLayout(layout)
        return panel
    
    def create_separator(self) -> QFrame:
        """创建分隔符"""
        separator = QFrame()
        separator.setFrameShape(QFrame.VLine)
        separator.setStyleSheet("color: #5a6c7d; background-color: #5a6c7d;")
        separator.setMaximumWidth(2)
        return separator
    
    def create_trend_coins_panel(self) -> QWidget:
        """创建趋势币面板"""
        trend_panel = QGroupBox("📈 趋势币监控")
        trend_panel.setFont(QFont("Microsoft YaHei", 10, QFont.Bold))
        trend_layout = QVBoxLayout(trend_panel)
        
        # 控制按钮行
        controls_layout = QHBoxLayout()
        
        # 刷新按钮
        self.refresh_btn = QPushButton("🔄 刷新")
        self.refresh_btn.clicked.connect(self.refresh_trend_data)
        self.refresh_btn.setStyleSheet(TREND_COINS_REFRESH_BUTTON_STYLE)
        controls_layout.addWidget(self.refresh_btn)
        
        # 添加一些空间
        controls_layout.addSpacing(10)
        
        # 状态标签
        self.status_label = QLabel("初始化中...")
        self.status_label.setStyleSheet("""
            QLabel {
                color: #7f8c8d;
                font-size: 11px;
                padding: 5px;
            }
        """)
        controls_layout.addWidget(self.status_label)
        
        # 添加弹性空间
        controls_layout.addStretch()
        
        # 最后更新时间标签
        self.last_update_label = QLabel("未更新")
        self.last_update_label.setStyleSheet("""
            QLabel {
                color: #95a5a6;
                font-size: 10px;
                font-weight: normal;
                padding: 2px 8px;
                background: rgba(52, 73, 94, 0.3);
                border: 1px solid #34495e;
                border-radius: 3px;
            }
        """)
        controls_layout.addWidget(self.last_update_label)
        
        trend_layout.addLayout(controls_layout)
        
        # 🔥🔥 新方案：用富文本框替换表格
        self.trend_text_display = QTextEdit()
        self.trend_text_display.setReadOnly(True)
        self.trend_text_display.setMinimumHeight(400)
        
        # 设置富文本框样式
        self.trend_text_display.setStyleSheet(TREND_TEXT_DISPLAY_STYLE)
        
        # 🔥 添加点击事件处理
        self.trend_text_display.mousePressEvent = self.on_trend_text_clicked
        
        # 存储当前显示的代币数据，供点击事件使用
        self.current_trend_tokens = []
        
        trend_layout.addWidget(self.trend_text_display)
        
        return trend_panel
    
    # 在 LiveTradingWidget 类中
    def create_chart_panel(self) -> QWidget:
        """创建K线图表面板"""
        group = QGroupBox("📈 K线图表 & 持仓") # 您可以修改标题以反映包含持仓
        group.setStyleSheet(LiveTradingStyles.GROUP_BOX_CHART)
        group.setMinimumHeight(300)

        # 主垂直布局
        main_panel_layout = QVBoxLayout()
        main_panel_layout.setContentsMargins(5, 10, 5, 5)
        
        # 图表控制栏 (保持不变)
        chart_controls = QHBoxLayout()
        self.selected_token_label = QLabel("请选择代币查看K线图")
        self.selected_token_label.setStyleSheet(LiveTradingStyles.LABEL_SELECTED_TOKEN)
        chart_controls.addWidget(self.selected_token_label)
        chart_controls.addStretch()
        timeframe_label = QLabel("周期:")
        timeframe_label.setStyleSheet(LiveTradingStyles.LABEL_CHART_CONTROL)
        self.timeframe_combo = QComboBox()
        self.timeframe_combo.setStyleSheet(LiveTradingStyles.COMBOBOX_SMALL)
        self.timeframe_combo.addItems(["1m", "5m", "15m", "1h", "4h", "1d"])
        self.timeframe_combo.setCurrentText("1m")
        self.timeframe_combo.currentTextChanged.connect(self.on_timeframe_changed)
        chart_controls.addWidget(timeframe_label)
        chart_controls.addWidget(self.timeframe_combo)
        
        # 测试按钮 (保持不变)
        test_chart_btn = QPushButton("🧪 测试")
        test_chart_btn.setStyleSheet(TEST_CHART_BUTTON_STYLE)
        test_chart_btn.setToolTip("点击测试图表功能")
        test_chart_btn.clicked.connect(self.test_chart_display)
        chart_controls.addWidget(test_chart_btn)
        
        main_panel_layout.addLayout(chart_controls) # 添加图表控制栏
        
        # 创建 K线图表 和 持仓面板 的水平布局容器
        chart_and_holdings_container = QWidget()
        chart_holdings_h_layout = QHBoxLayout(chart_and_holdings_container)
        chart_holdings_h_layout.setContentsMargins(0, 0, 0, 0)
        chart_holdings_h_layout.setSpacing(5)

        # K线图表区域
        self.chart_widget = self.create_chart_display() # 这行应该已经存在
        chart_holdings_h_layout.addWidget(self.chart_widget, 3) # K线图，占据3份空间
        
        # self.holdings_panel 应该已经在 __init__ 中被创建
        chart_holdings_h_layout.addWidget(self.holdings_panel, 1) # 持仓面板，占据1份空间 (您可以调整比例)
        
        main_panel_layout.addWidget(chart_and_holdings_container) # 添加K线图和持仓的容器
        
        # 技术指标摘要 (保持不变)
        self.indicators_summary = QLabel("技术指标：等待数据...")
        self.indicators_summary.setStyleSheet("""
            QLabel {
                color: #95a5a6;
                font-size: 9px;
                padding: 3px;
                background-color: rgba(52, 73, 94, 0.3);
                border-radius: 3px;
                margin: 2px;
            }
        """)
        main_panel_layout.addWidget(self.indicators_summary)
        
        group.setLayout(main_panel_layout)
        return group
    
    
    def create_chart_display(self) -> QWidget:
        """创建图表显示区域 - 现在使用真正的ChartWidget"""
        # 创建并返回 ChartWidget 实例
        # self.actual_chart_widget = ChartWidget(api_service=self.api_service, parent=self)
        # return self.actual_chart_widget
        
        # 直接在LiveTradingWidget中创建并管理ChartWidget实例
        # 它将作为K线图表面板的子组件
        # 注意：这里的 self.api_service 是 LiveTradingWidget 的实例变量
        if not hasattr(self, 'api_service') or self.api_service is None:
            logger.error("LiveTradingWidget: APIService 未初始化，无法创建ChartWidget")
            # 返回一个占位符或错误提示的QWidget
            error_widget = QWidget()
            error_layout = QVBoxLayout(error_widget)
            error_label = QLabel("错误：API服务未初始化，无法加载图表。")
            error_label.setAlignment(Qt.AlignCenter)
            error_layout.addWidget(error_label)
            return error_widget

        self.actual_chart_widget = ChartWidget(api_service=self.api_service, parent=self)
        # 连接图表的买卖信号
        self.actual_chart_widget.trade_signal_generated.connect(self.on_chart_signal_generated)
        # ChartWidget的背景等由其自身init_ui设置，这里不需要额外设置
        # self.actual_chart_widget.setMinimumHeight(250) # ChartWidget自身会处理大小
        return self.actual_chart_widget
    
    def create_trade_records_panel(self) -> QWidget:
        """创建信号记录面板"""
        group = QGroupBox("📋 策略信号记录")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 12px;
                color: #ecf0f1;
                border: 1px solid #1a252f;
                border-radius: 4px;
                margin-top: 10px;
                padding-top: 5px;
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #34495e, stop: 1 #2c3e50);
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                color: #e67e22;
            }
        """)
        
        layout = QVBoxLayout()
        layout.setContentsMargins(5, 10, 5, 5)
        
        # 控制栏
        control_layout = QHBoxLayout()
        
        # 过滤选项
        filter_layout = QHBoxLayout()
        filter_layout.setSpacing(8)
        
        # 策略过滤
        strategy_filter_label = QLabel("策略:")
        strategy_filter_label.setStyleSheet("color: #ecf0f1; font-size: 10px;")
        self.strategy_filter_combo = QComboBox()
        self.strategy_filter_combo.setStyleSheet("""
            QComboBox {
                background-color: #3a4a5c;
                color: #ecf0f1;
                border: 1px solid #5a6c7d;
                border-radius: 3px;
                padding: 2px 6px;
                font-size: 9px;
                min-width: 80px;
                max-height: 22px;
            }
        """)
        self.strategy_filter_combo.addItems(["全部策略", "当前策略"])
        self.strategy_filter_combo.currentTextChanged.connect(self.filter_trade_records)
        
        # 时间过滤
        time_filter_label = QLabel("时间:")
        time_filter_label.setStyleSheet("color: #ecf0f1; font-size: 10px;")
        self.time_filter_combo = QComboBox()
        self.time_filter_combo.setStyleSheet("""
            QComboBox {
                background-color: #3a4a5c;
                color: #ecf0f1;
                border: 1px solid #5a6c7d;
                border-radius: 3px;
                padding: 2px 6px;
                font-size: 9px;
                min-width: 60px;
                max-height: 22px;
            }
        """)
        self.time_filter_combo.addItems(["全部", "今日", "本周", "本月"])
        self.time_filter_combo.currentTextChanged.connect(self.filter_trade_records)
        
        # 类型过滤
        type_filter_label = QLabel("类型:")
        type_filter_label.setStyleSheet("color: #ecf0f1; font-size: 10px;")
        self.type_filter_combo = QComboBox()
        self.type_filter_combo.setStyleSheet("""
            QComboBox {
                background-color: #3a4a5c;
                color: #ecf0f1;
                border: 1px solid #5a6c7d;
                border-radius: 3px;
                padding: 2px 6px;
                font-size: 9px;
                min-width: 50px;
                max-height: 22px;
            }
        """)
        self.type_filter_combo.addItems(["全部", "买入", "卖出"])
        self.type_filter_combo.currentTextChanged.connect(self.filter_trade_records)
        
        # 状态过滤
        status_filter_label = QLabel("状态:")
        status_filter_label.setStyleSheet("color: #ecf0f1; font-size: 10px;")
        self.status_filter_combo = QComboBox()
        self.status_filter_combo.setStyleSheet("""
            QComboBox {
                background-color: #3a4a5c;
                color: #ecf0f1;
                border: 1px solid #5a6c7d;
                border-radius: 3px;
                padding: 2px 6px;
                font-size: 9px;
                min-width: 60px;
                max-height: 22px;
            }
        """)
        self.status_filter_combo.addItems(["全部", "信号", "已执行", "成功", "模拟"])
        self.status_filter_combo.currentTextChanged.connect(self.filter_trade_records)
        
        filter_layout.addWidget(strategy_filter_label)
        filter_layout.addWidget(self.strategy_filter_combo)
        filter_layout.addWidget(time_filter_label)
        filter_layout.addWidget(self.time_filter_combo)
        filter_layout.addWidget(type_filter_label)
        filter_layout.addWidget(self.type_filter_combo)
        filter_layout.addWidget(status_filter_label)
        filter_layout.addWidget(self.status_filter_combo)
        
        control_layout.addLayout(filter_layout)
        control_layout.addStretch()
        
        # 统计信息
        self.trade_stats_label = QLabel("交易: 0 | 胜率: 0% | 盈亏: $0")
        self.trade_stats_label.setStyleSheet("""
            color: #bdc3c7;
            font-size: 10px;
            padding: 3px 8px;
            background: rgba(52, 73, 94, 0.5);
            border-radius: 3px;
        """)
        control_layout.addWidget(self.trade_stats_label)
        
        # 操作按钮
        clear_button = QPushButton("🗑️ 清空")
        clear_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: 1px solid #c0392b;
                border-radius: 3px;
                padding: 2px 8px;
                font-size: 9px;
                font-weight: bold;
                max-height: 22px;
            }
            QPushButton:hover {
                background-color: #ec7063;
            }
        """)
        clear_button.clicked.connect(self.clear_trade_records)
        control_layout.addWidget(clear_button)
        
        layout.addLayout(control_layout)
        
        # 🔥🔥 添加性能监控切换按钮
        perf_toggle_layout = QHBoxLayout()
        self.show_performance_button = QPushButton("📊 显示性能监控")
        self.show_performance_button.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                border: 1px solid #e67e22;
                border-radius: 3px;
                padding: 3px 8px;
                font-size: 10px;
                font-weight: bold;
                max-height: 24px;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
            QPushButton:checked {
                background-color: #d35400;
            }
        """)
        self.show_performance_button.setCheckable(True)
        self.show_performance_button.clicked.connect(self.toggle_performance_monitor)
        perf_toggle_layout.addWidget(self.show_performance_button)
        perf_toggle_layout.addStretch()
        layout.addLayout(perf_toggle_layout)
        
        # 🔥🔥 性能监控面板（默认隐藏）
        self.performance_widget = None
        self.create_performance_monitor_widget()
        if self.performance_widget:
            self.performance_widget.setVisible(False)
            layout.addWidget(self.performance_widget)
        
        # 信号记录表格
        self.trade_records_table = QTableWidget()
        self.trade_records_table.setColumnCount(9)
        self.trade_records_table.setHorizontalHeaderLabels([
            "时间", "代币", "类型", "策略", "价格", "数量", "价值", "状态", "盈亏"
        ])
        
        # 设置表格样式
        self.trade_records_table.setStyleSheet(TRADE_RECORDS_TABLE_STYLE)
        
        # 设置表格属性
        header = self.trade_records_table.horizontalHeader()
        header.setDefaultSectionSize(70)
        header.setSectionResizeMode(0, QHeaderView.Fixed)  # 时间
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # 代币
        header.setSectionResizeMode(2, QHeaderView.Fixed)  # 类型
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)  # 策略
        header.setSectionResizeMode(4, QHeaderView.Fixed)  # 价格
        header.setSectionResizeMode(5, QHeaderView.Fixed)  # 数量
        header.setSectionResizeMode(6, QHeaderView.Fixed)  # 价值
        header.setSectionResizeMode(7, QHeaderView.Fixed)  # 状态
        header.setSectionResizeMode(8, QHeaderView.Fixed)  # 盈亏
        
        self.trade_records_table.setColumnWidth(0, 120)  # 时间
        self.trade_records_table.setColumnWidth(2, 45)   # 类型
        self.trade_records_table.setColumnWidth(4, 80)   # 价格
        self.trade_records_table.setColumnWidth(5, 80)   # 数量
        self.trade_records_table.setColumnWidth(6, 80)   # 价值
        self.trade_records_table.setColumnWidth(7, 50)   # 状态
        self.trade_records_table.setColumnWidth(8, 70)   # 盈亏
        
        self.trade_records_table.setAlternatingRowColors(False)
        self.trade_records_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.trade_records_table.setSortingEnabled(True)
        self.trade_records_table.setShowGrid(False)
        self.trade_records_table.verticalHeader().setVisible(False)
        
        # 添加右键菜单
        self.trade_records_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.trade_records_table.customContextMenuRequested.connect(self.show_trade_context_menu)
        
        # 添加选择事件处理
        self.trade_records_table.itemSelectionChanged.connect(self.on_trade_record_selected)
        
        # 设置固定高度，不随窗口自适应
        self.trade_records_table.setFixedHeight(200)  # 固定高度250像素
        self.trade_records_table.setMinimumHeight(150)  # 最小高度250像素
        self.trade_records_table.setMaximumHeight(200)  # 最大高度250像素
        
        layout.addWidget(self.trade_records_table)
        
        # 底部摘要栏
        summary_layout = QHBoxLayout()
        
        self.trade_summary_label = QLabel("选择信号记录可自动缩放图表到最佳展示区间 📊")
        self.trade_summary_label.setStyleSheet("""
            color: #95a5a6;
            font-size: 9px;
            padding: 3px;
        """)
        summary_layout.addWidget(self.trade_summary_label)
        
        summary_layout.addStretch()
        
        self.quick_stats_label = QLabel("今日: $0 | 本周: $0")
        self.quick_stats_label.setStyleSheet("""
            color: #bdc3c7;
            font-size: 9px;
            padding: 3px;
        """)
        summary_layout.addWidget(self.quick_stats_label)
        
        layout.addLayout(summary_layout)
        
        group.setLayout(layout)
        return group
    
    def create_performance_monitor_widget(self):
        """创建性能监控组件"""
        try:
            from .performance_monitor import PerformanceWidget
            self.performance_widget = PerformanceWidget(self.performance_monitor, self)
            self.performance_widget.setMaximumHeight(200)  # 限制高度
            logger.info("性能监控组件创建成功")
        except Exception as e:
            logger.error(f"创建性能监控组件失败: {e}")
            self.performance_widget = None
    
    def toggle_performance_monitor(self, checked: bool):
        """切换性能监控面板显示/隐藏"""
        if self.performance_widget:
            self.performance_widget.setVisible(checked)
            if checked:
                self.show_performance_button.setText("📊 隐藏性能监控")
                logger.info("性能监控面板已显示")
            else:
                self.show_performance_button.setText("📊 显示性能监控")
                logger.info("性能监控面板已隐藏")
        else:
            logger.warning("性能监控组件未创建")
    
    def show_performance_dialog(self):
        """显示性能监控对话框"""
        try:
            from PyQt5.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QPushButton
            from .performance_monitor import PerformanceWidget
            
            # 创建对话框
            dialog = QDialog(self)
            dialog.setWindowTitle("🔥 实时性能监控")
            dialog.setWindowFlags(dialog.windowFlags() | Qt.WindowStaysOnTopHint)
            dialog.resize(400, 350)
            
            # 设置对话框样式
            dialog.setStyleSheet("""
                QDialog {
                    background-color: #2c3e50;
                    color: #ecf0f1;
                    border: 2px solid #34495e;
                    border-radius: 8px;
                }
            """)
            
            layout = QVBoxLayout(dialog)
            
            # 创建性能监控组件
            perf_widget = PerformanceWidget(self.performance_monitor, dialog)
            layout.addWidget(perf_widget)
            
            # 按钮栏
            button_layout = QHBoxLayout()
            
            # 缓存统计按钮
            cache_stats_button = QPushButton("📊 缓存统计")
            cache_stats_button.setStyleSheet("""
                QPushButton {
                    background-color: #3498db;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 4px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #2980b9;
                }
            """)
            cache_stats_button.clicked.connect(lambda: self.show_cache_stats_dialog(dialog))
            button_layout.addWidget(cache_stats_button)
            
            button_layout.addStretch()
            
            # 关闭按钮
            close_button = QPushButton("关闭")
            close_button.setStyleSheet("""
                QPushButton {
                    background-color: #95a5a6;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 4px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #7f8c8d;
                }
            """)
            close_button.clicked.connect(dialog.close)
            button_layout.addWidget(close_button)
            
            layout.addLayout(button_layout)
            
            # 显示对话框
            dialog.exec_()
            
        except Exception as e:
            logger.error(f"显示性能监控对话框失败: {e}")
    
    def show_cache_stats_dialog(self, parent_dialog):
        """显示缓存统计对话框"""
        try:
            from PyQt5.QtWidgets import QDialog, QVBoxLayout, QTextEdit, QPushButton
            
            dialog = QDialog(parent_dialog)
            dialog.setWindowTitle("📈 缓存与后台监控统计")
            dialog.resize(450, 300)
            dialog.setStyleSheet("""
                QDialog {
                    background-color: #2c3e50;
                    color: #ecf0f1;
                    border: 2px solid #34495e;
                    border-radius: 8px;
                }
            """)
            
            layout = QVBoxLayout(dialog)
            
            # 统计信息文本框
            stats_text = QTextEdit()
            stats_text.setReadOnly(True)
            stats_text.setStyleSheet("""
                QTextEdit {
                    background-color: #34495e;
                    color: #ecf0f1;
                    border: 1px solid #5a6c7d;
                    border-radius: 4px;
                    font-family: monospace;
                    font-size: 11px;
                    padding: 10px;
                }
            """)
            
            # 获取统计数据
            cache_stats = self.background_monitor_manager.get_cache_stats()
            perf_summary = self.performance_monitor.get_performance_summary()
            
            stats_info = f"""🔥 后台监控与缓存统计

📊 缓存统计:
• 缓存数据条目: {cache_stats['cache_size']}
• 待处理请求: {cache_stats['pending_requests']}
• 请求队列长度: {cache_stats['request_queue_size']}
• 正在批处理: {"是" if cache_stats['processing_batch'] else "否"}

📈 性能统计:
• 平均 CPU 使用率: {perf_summary['cpu_avg']:.1f}%
• 当前内存使用率: {perf_summary['memory_current']:.1f}%
• API 请求总数: {perf_summary['api_requests']}
• 缓存命中次数: {perf_summary['cache_hits']}
• 缓存命中率: {(perf_summary['cache_hits'] / max(1, perf_summary['api_requests'])) * 100:.1f}%
• 运行时长: {perf_summary['uptime'] / 60:.1f} 分钟

🔧 后台图表池:
• 图表实例数: {len(self.background_chart_pool)}
• 活跃图表数: {len(self.active_background_charts)}
• 配置刷新间隔: {self.background_monitor_manager.config.get('refresh_interval', 60000) / 1000} 秒

💡 优化建议:
{"• ✅ 缓存工作正常" if cache_stats['cache_size'] > 0 else "• ⚠️ 缓存未生效，检查配置"}
{"• ✅ 缓存命中率良好" if (perf_summary['cache_hits'] / max(1, perf_summary['api_requests'])) > 0.5 else "• ⚠️ 缓存命中率较低"}
{"• ✅ CPU 使用率正常" if perf_summary['cpu_avg'] < 50 else "• ⚠️ CPU 使用率偏高"}
{"• ✅ 内存使用率正常" if perf_summary['memory_current'] < 80 else "• ⚠️ 内存使用率偏高"}
"""
            
            stats_text.setPlainText(stats_info)
            layout.addWidget(stats_text)
            
            # 关闭按钮
            close_button = QPushButton("关闭")
            close_button.setStyleSheet("""
                QPushButton {
                    background-color: #95a5a6;
                    color: white;
                    border: none;
                    padding: 8px 16px;
                    border-radius: 4px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #7f8c8d;
                }
            """)
            close_button.clicked.connect(dialog.close)
            layout.addWidget(close_button)
            
            dialog.exec_()
            
        except Exception as e:
            logger.error(f"显示缓存统计对话框失败: {e}")
    
    def create_message_log_panel(self) -> QWidget:
        """创建消息日志面板"""
        group = QGroupBox("📝 消息日志")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 12px;
                color: #ecf0f1;
                border: 1px solid #1a252f;
                border-radius: 4px;
                margin-top: 10px;
                padding-top: 5px;
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #34495e, stop: 1 #2c3e50);
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px 0 8px;
                color: #3498db;
            }
        """)
        
        layout = QVBoxLayout()
        layout.setContentsMargins(5, 10, 5, 5)
        
        # 控制栏
        control_layout = QHBoxLayout()
        
        # 自动滚动选项
        self.auto_scroll_log_checkbox = QCheckBox("自动滚动")
        self.auto_scroll_log_checkbox.setChecked(True)
        self.auto_scroll_log_checkbox.setStyleSheet("""
            QCheckBox {
                color: #ecf0f1;
                font-size: 10px;
            }
        """)
        control_layout.addWidget(self.auto_scroll_log_checkbox)
        
        # 信号日志开关
        self.enable_signal_logs_checkbox = QCheckBox("信号日志")
        self.enable_signal_logs_checkbox.setChecked(True)
        self.enable_signal_logs_checkbox.setStyleSheet("""
            QCheckBox {
                color: #ecf0f1;
                font-size: 10px;
            }
        """)
        self.enable_signal_logs_checkbox.toggled.connect(self.toggle_local_signal_logs)
        control_layout.addWidget(self.enable_signal_logs_checkbox)
        
        control_layout.addStretch()
        
        # 清空日志按钮
        clear_log_button = QPushButton("🗑️ 清空")
        clear_log_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: 1px solid #c0392b;
                border-radius: 3px;
                padding: 2px 8px;
                font-size: 9px;
                font-weight: bold;
                max-height: 22px;
            }
            QPushButton:hover {
                background-color: #ec7063;
            }
        """)
        clear_log_button.clicked.connect(self.clear_message_log)
        control_layout.addWidget(clear_log_button)
        
        layout.addLayout(control_layout)
        
        # 消息日志文本框
        self.message_log_text = QTextEdit()
        self.message_log_text.setReadOnly(True)
        self.message_log_text.setStyleSheet("""
            QTextEdit {
                background-color: #1a252f;
                color: #ecf0f1;
                border: 1px solid #34495e;
                border-radius: 3px;
                font-family: Consolas, Monaco, monospace;
                font-size: 9px;
                padding: 5px;
            }
        """)
        
        # 设置固定高度与策略信号记录表格保持一致
        self.message_log_text.setFixedHeight(200)
        self.message_log_text.setMinimumHeight(150)
        self.message_log_text.setMaximumHeight(200)
        
        layout.addWidget(self.message_log_text)
        
        # 底部统计信息
        self.log_stats_label = QLabel("消息: 0 | 最后更新: 未更新")
        self.log_stats_label.setStyleSheet("""
            color: #95a5a6;
            font-size: 9px;
            padding: 3px;
        """)
        layout.addWidget(self.log_stats_label)
        
        group.setLayout(layout)
        
        # 初始化消息计数器
        self.message_log_count = 0
        self.enable_local_signal_logs = True
        
        return group
    
    def setup_timer(self):
        """设置定时器 - 作为唯一的数据源刷新器"""
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.refresh_trend_data)
        # 🔥 统一的数据刷新：1分钟刷新一次，为所有组件提供数据
        self.refresh_timer.start(60000)  # 每1分钟刷新一次
        
        # 🔥🔥 新增：相对时间更新定时器
        self.relative_time_timer = QTimer()
        self.relative_time_timer.timeout.connect(self.update_all_relative_times)
        self.relative_time_timer.start(15000)  # 🔥 修改为每30秒更新一次相对时间显示
        logger.info("相对时间更新定时器已启动（每15秒）")
        
        # 🔥🔥 新增：未处理信号更新定时器
        self.unprocessed_signals_timer = QTimer()
        self.unprocessed_signals_timer.timeout.connect(self.update_unprocessed_signals_table)
        self.unprocessed_signals_timer.start(5000) # 每5秒刷新一次
        logger.info("未处理信号表格更新定时器已启动（每5秒）")
        
        # 🔥🔥 移除并行分析定时器启动，ChartWidget 将自行管理
        # self.parallel_analysis_timer.start(60000) 
        # logger.info("并行图表分析定时器已启动（每60秒）")
    
    # 🔥🔥 修改：管理后台 ChartWidget 的方法
    
    def update_background_charts(self, display_tokens: List[Dict]):
        """更新后台图表池 - 使用provided的代币列表，针对显示的代币启动后台分析"""
        
        # 🔥 ADDED: Blacklist for tokens to ignore in background processing
        IGNORED_TOKEN_ADDRESSES = {
            "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",  # USDC
            "So11111111111111111111111111111111111111112",  # WSOL
            # 你可以在这里添加其他不想跟踪的代币地址
        }

        print(f"🔥🔥 UPDATE_BACKGROUND_CHARTS called with {len(display_tokens) if display_tokens else 0} tokens")
        
        if not display_tokens:
            print("❌ LiveTradingWidget: 没有代币需要后台分析")
            return
        
        # 🔥🔥 修复：为持仓代币添加source字段，避免unknown source警告
        # 检查代币是否来自持仓数据，如果是则添加holdings source
        enriched_tokens_intermediate = []
        for token in display_tokens:
            token_copy = token.copy()
            
            # 🔥🔥 改进source判断逻辑
            if 'source' not in token_copy or not token_copy.get('source'):
                # 首先检查代币是否在持仓列表中
                is_in_holdings = False
                if hasattr(self, 'holdings_list_normalized') and self.holdings_list_normalized:
                    holdings_addresses = [h.get('tokenAddress') or h.get('address') for h in self.holdings_list_normalized]
                    token_address_check = token_copy.get('tokenAddress') or token_copy.get('address')
                    is_in_holdings = token_address_check in holdings_addresses
                
                if is_in_holdings:
                    token_copy['source'] = 'holdings'  # 🔥 真正的持仓代币
                    logger.debug(f"确认持仓代币: {token_copy.get('symbol', 'N/A')} -> source=holdings")
                else:
                    # 检查是否有持仓特征字段，但不在持仓列表中（可能是旧数据）
                    if ('quantity' in token_copy or 'value_usd' in token_copy or 
                        'balance' in token_copy or 'raw_balance' in token_copy):
                        token_copy['source'] = 'holdings'  # 有持仓特征的代币
                        logger.debug(f"持仓特征代币: {token_copy.get('symbol', 'N/A')} -> source=holdings")
                    else:
                        # 其他情况默认为trend（从趋势榜单来的）
                        token_copy['source'] = 'trend'
                        logger.debug(f"趋势代币: {token_copy.get('symbol', 'N/A')} -> source=trend")
            
            # 🔥🔥 确保代币地址字段统一
            if 'address' in token_copy and 'tokenAddress' not in token_copy:
                token_copy['tokenAddress'] = token_copy['address']
            elif 'tokenAddress' in token_copy and 'address' not in token_copy:
                token_copy['address'] = token_copy['tokenAddress']
            
            enriched_tokens_intermediate.append(token_copy)
        
        # Filter out ignored tokens before further processing
        enriched_tokens = [
            token for token in enriched_tokens_intermediate
            if (token.get('tokenAddress') not in IGNORED_TOKEN_ADDRESSES and
                token.get('address') not in IGNORED_TOKEN_ADDRESSES)
        ]

        if len(enriched_tokens) < len(enriched_tokens_intermediate):
            ignored_count = len(enriched_tokens_intermediate) - len(enriched_tokens)
            logger.info(f"ℹ️ 已忽略 {ignored_count} 个在黑名单中的代币进行后台分析。")
        
        if not enriched_tokens:
            logger.info("ℹ️ 所有代币都在黑名单中或列表为空，没有可供后台分析的代币。")
            self.update_background_analysis_status("无代币进行分析 (已过滤)")
            return

        total_charts = len(enriched_tokens)
        print(f"   LiveTradingWidget: 开始更新 {total_charts} 个后台图表 (已过滤黑名单)")
        
        # 🔥 停止旧的refresh timer避免冲突
        if hasattr(self, 'background_refresh_timer') and self.background_refresh_timer.isActive():
            self.background_refresh_timer.stop()
        
        # 获取当前策略
        current_strategy_raw = self.strategy_combo.currentText() if hasattr(self, 'strategy_combo') else None
        current_strategy = current_strategy_raw.replace("📊 ", "") if current_strategy_raw and current_strategy_raw != "选择策略..." else None
        print(f"   当前策略: {current_strategy} (原始: {current_strategy_raw})")
        
        # 🔥🔥 NEW: 分批处理，避免UI线程阻塞
        def process_tokens_in_batches():
            """分批处理代币，避免一次性处理30个导致UI阻塞"""
            # 🔥🔥 使用配置中的性能优化参数
            from config import BACKGROUND_MONITORING_CONFIG
            batch_config = BACKGROUND_MONITORING_CONFIG.get('batch_processing', {})
            batch_size = batch_config.get('batch_size', 3)  # 降低到3个代币
            current_batch = 0
            
            # Use the filtered enriched_tokens list
            tokens_to_process_in_batches = enriched_tokens 

            def process_single_batch():
                nonlocal current_batch
                
                start_idx = current_batch * batch_size
                end_idx = min(start_idx + batch_size, len(tokens_to_process_in_batches))
                end_idx = min(start_idx + batch_size, len(enriched_tokens))
                
                if start_idx >= len(enriched_tokens):
                    # 所有批次处理完成
                    print(f"   ✅ 所有批次处理完成，共处理 {len(enriched_tokens)} 个代币")
                    
                    # 启动定时刷新
                    if hasattr(self, 'background_refresh_timer'):
                        self.background_refresh_timer.start(self.BACKGROUND_REFRESH_INTERVAL)
                    return
                
                print(f"   📊 处理批次 {current_batch + 1}, 代币 {start_idx + 1}-{end_idx}")
                
                # 处理当前批次的代币
                for i in range(start_idx, end_idx):
                    if i < len(self.background_chart_pool):
                        try:
                            chart_widget = self.background_chart_pool[i]
                            enriched_token = enriched_tokens[i]
                            
                            # 添加策略和时间周期信息
                            enriched_token['strategy_name'] = current_strategy
                            enriched_token['timeframe'] = self.timeframe
                            
                            # 设置代币数据（现在包含正确的source）
                            chart_widget.set_token(enriched_token)
                            
                            # 🔥 设置后台模式以跳过某些UI相关操作
                            if hasattr(chart_widget, 'is_background_parallel'):
                                chart_widget.is_background_parallel = True
                            
                            # 🔥🔥 延迟触发数据刷新，避免同时启动多个请求
                            item_delay = batch_config.get('item_delay', 1000)  # 使用配置的延迟
                            delay_ms = (i - start_idx) * item_delay
                            QTimer.singleShot(delay_ms, chart_widget.refresh_data)
                            
                            print(f"     设置代币 {i}: {enriched_token.get('symbol', 'Unknown')} (延迟{delay_ms}ms)")
                        
                        except Exception as e:
                            print(f"   ❌ 设置后台图表 {i} 失败: {e}")
                
                # 让UI响应，防止阻塞
                QApplication.processEvents()
                
                # 准备处理下一批次
                current_batch += 1
                
                # 批次间延迟，进一步避免阻塞  
                batch_delay = batch_config.get('batch_delay', 2000)  # 使用配置的批次延迟
                QTimer.singleShot(batch_delay, process_single_batch)
            
            # 开始处理第一批
            process_single_batch()
        
        # 异步开始分批处理
        QTimer.singleShot(0, process_tokens_in_batches)
    
    def trigger_background_analysis(self): # Renamed from trigger_parallel_analysis
        """触发所有后台 ChartWidget 的数据刷新和策略分析（如果它们不是自动的）"""
        try:
            print(f"🔥🔥 TRIGGER_BACKGROUND_ANALYSIS called")
            print(f"   后台图表池大小: {len(self.background_chart_pool)}")
            print(f"   活跃后台图表数: {len(self.active_background_charts)}")
            print(f"   当前策略: {self.selected_strategy.name if self.selected_strategy else None}")
            
            if not self.background_chart_pool:
                print("🔵 没有后台 ChartWidget，跳过触发分析。")
                return
            
            if not self.selected_strategy:
                print("🔵 没有选择策略，延迟启动分析...")
                # 使用定时器延迟检查，给策略选择一些时间
                from PyQt5.QtCore import QTimer
                QTimer.singleShot(1000, self.auto_enable_trading_monitor_delayed)
                return
            
            # 自动开启交易开关
            self.trading_toggle.setChecked(True)
            self.trading_enabled = True
            
            # 更新交易开关样式和文本
            self.trading_toggle.setText("🟢 交易：开")
            self.trading_toggle.setStyleSheet(TRADING_TOGGLE_ON_STYLE)
            
            # 显示实盘交易监控弹窗
            self.show_live_trading_monitor()
            
            # 更新状态
            self.status_label.setText(f"🚀 实盘交易已自动开启 - 策略: {self.selected_strategy.name}")
            logger.info(f"自动开启实盘交易监控 - 策略: {self.selected_strategy.name}")
            
        except Exception as e:
            logger.error(f"自动开启实盘交易监控失败: {e}")
            # 如果自动开启失败，不影响正常使用
    
    @pyqtSlot(dict)
    def on_background_data_ready(self, data_batch: Dict):
        """处理后台监控管理器提供的批量数据"""
        try:
            token_address = data_batch.get('token_address', '')
            timeframe = data_batch.get('timeframe', '')
            ohlcv_data = data_batch.get('ohlcv_data', [])
            from_cache = data_batch.get('from_cache', False)
            
            if not token_address or not ohlcv_data:
                return
            
            # 查找对应的HeadlessChartWidget
            matching_widget = None
            for widget in self.background_chart_pool:
                if (widget.token_data and 
                    widget.token_data.get('tokenAddress') == token_address and
                    widget.timeframe == timeframe):
                    matching_widget = widget
                    break
            
            if matching_widget:
                symbol = matching_widget.token_data.get('symbol', 'Unknown')
                cache_note = " (缓存)" if from_cache else ""
                logger.info(f"后台数据就绪: {symbol}{cache_note}")
                
                # 直接处理数据，跳过API请求
                matching_widget.display_provided_ohlcv(ohlcv_data, timeframe, f"统一管理器{cache_note}")
                
        except Exception as e:
            logger.error(f"处理后台批量数据失败: {e}")
    
    def auto_enable_trading_monitor_delayed(self):
        """延迟自动开启实盘交易监控（给策略选择时间）"""
        try:
            if self.selected_strategy:
                # 策略已选择，开启监控
                self.auto_enable_trading_monitor()
            else:
                logger.info("策略尚未选择完成，跳过自动开启实盘交易监控")
                # 可选：继续等待或提示用户手动开启
                
        except Exception as e:
            logger.error(f"延迟自动开启实盘交易监控失败: {e}")
    
    @pyqtSlot()
    def refresh_trend_data(self):
        """刷新趋势币数据 - 统一的数据源"""
        logger.info("🔥 REFRESH_TREND_DATA_CALLED.") # Log when method is called
        # 检查是否已有线程在运行，避免重复
        if self.trend_data_thread and self.trend_data_thread.isRunning():
            logger.info("  TREND_DATA_THREAD is already running, skipping duplicate refresh.")
            return
        
        self.status_label.setText("正在获取最新趋势币数据...")

        # 🔥 修复：只有在钱包已连接时才同步刷新持仓数据，避免无必要的清空操作
        if self.wallet_connected and self.wallet_address:
            self.refresh_holdings_data() # 同步刷新持仓数据
        else:
            logger.info("  钱包未连接，跳过持仓数据刷新")
        
        self.trend_data_thread = TrendDataThread(self)
        self.trend_data_thread.data_updated.connect(self.update_trend_table)
        self.trend_data_thread.error_occurred.connect(self.handle_trend_data_error)
        self.trend_data_thread.finished.connect(self.trend_data_finished)
        
        logger.info("  🔥 REFRESH_TREND_DATA: Attempting to start TrendDataThread.")
        self.trend_data_thread.start()
        logger.info(f"  🔥 REFRESH_TREND_DATA: TrendDataThread.start() called. isRunning: {self.trend_data_thread.isRunning()}")
    
    @pyqtSlot(list)
    def update_trend_table(self, trend_tokens: List[Dict], trigger_background_refresh: bool = True):
        """更新趋势币显示 - 使用富文本替代表格"""
        logger.info(f"🔥 UPDATE_TREND_DISPLAY_CALLED with {len(trend_tokens)} trend_tokens, trigger_bg_refresh={trigger_background_refresh}.")
        
        if not trend_tokens:
            self.trend_text_display.setHtml("""
            <div style='text-align: center; color: #e74c3c; font-size: 16px; margin-top: 50px;'>
                <h3>❌ 暂无趋势币数据</h3>
                <p style='color: #95a5a6;'>请点击刷新按钮获取最新数据</p>
            </div>
            """)
            return
        
        # 🔥 去重和排序（保持原有逻辑）
        unique_tokens = {}
        for token in trend_tokens:
            token_address = token.get('tokenAddress')
            if token_address and token_address not in unique_tokens:
                unique_tokens[token_address] = token
        
        deduplicated_tokens = list(unique_tokens.values())
        sorted_tokens = sorted(deduplicated_tokens, key=lambda x: x.get('tweetCount', 0), reverse=True)
        display_tokens = sorted_tokens[:self.MAX_DISPLAY_TOKENS]
        
        # 🔥 保存数据供点击事件使用
        self.current_trend_tokens = display_tokens
        self.trend_tokens = display_tokens
        
        print(f"   🎯 显示 {len(display_tokens)} 个代币 (限制: {self.MAX_DISPLAY_TOKENS})")
        
        # 🔥🔥 生成HTML内容 - 超快速更新
        html_content = """
        <style>
            .token-row { 
                padding: 8px 12px; 
                margin: 2px 0; 
                border-left: 3px solid #3498db;
                background: rgba(52, 73, 94, 0.3);
                border-radius: 4px;
                cursor: pointer;
            }
            .token-row:hover { 
                background: rgba(52, 152, 219, 0.2);
                border-left-color: #e74c3c;
            }
            .token-symbol { 
                color: #f1c40f; 
                font-weight: bold; 
                font-size: 14px;
            }
            .token-name { 
                color: #bdc3c7; 
                font-size: 11px;
            }
            .price-up { color: #27ae60; font-weight: bold; }
            .price-down { color: #e74c3c; font-weight: bold; }
            .price-neutral { color: #95a5a6; }
            .metric { color: #ecf0f1; }
            .metric-label { color: #7f8c8d; font-size: 10px; }
            .strategy-buy { color: #27ae60; font-weight: bold; }
            .strategy-sell { color: #e74c3c; font-weight: bold; }
            .strategy-hold { color: #f39c12; font-weight: bold; }
            .strategy-loading { color: #95a5a6; }
            .rank { color: #8e44ad; font-weight: bold; }
        </style>
        
        <div style='margin-bottom: 15px;'>
            <h4 style='color: #3498db; margin: 0; font-size: 16px;'>
                📈 热门趋势币排行 (按讨论数排序)
            </h4>
            <p style='color: #7f8c8d; font-size: 11px; margin: 5px 0;'>
                点击任意代币加载图表分析 • 实时策略信号监控
            </p>
        </div>
        """
        
        for i, token in enumerate(display_tokens):
            rank = i + 1
            symbol = token.get('symbol', 'Unknown')
            name = token.get('name', 'Unknown')
            
            # 截断过长的名称
            if len(name) > 15:
                display_name = name[:12] + "..."
            else:
                display_name = name
            
            # 价格变化
            price_change = token.get('priceChange5m', 0)
            if isinstance(price_change, (int, float)):
                if price_change > 0:
                    change_html = f"<span class='price-up'>+{price_change:.1f}%</span>"
                elif price_change < 0:
                    change_html = f"<span class='price-down'>{price_change:.1f}%</span>"
                else:
                    change_html = f"<span class='price-neutral'>0.0%</span>"
            else:
                change_html = "<span class='price-neutral'>N/A</span>"
            
            # 市值格式化
            market_cap = token.get('marketCap', 0)
            try:
                market_cap = float(market_cap) if market_cap is not None else 0
                if market_cap >= 1000000000:
                    cap_text = f"${market_cap/1000000000:.1f}B"
                elif market_cap >= 1000000:
                    cap_text = f"${market_cap/1000000:.1f}M"
                elif market_cap >= 1000:
                    cap_text = f"${market_cap/1000:.1f}K"
                else:
                    cap_text = f"${market_cap:.0f}" if market_cap > 0 else "N/A"
            except:
                cap_text = "N/A"
            
            # 交易量格式化
            volume_24h = token.get('volume24h', 0)
            try:
                volume_24h = float(volume_24h) if volume_24h is not None else 0
                if volume_24h >= 1000000000:
                    vol_text = f"${volume_24h/1000000000:.1f}B"
                elif volume_24h >= 1000000:
                    vol_text = f"${volume_24h/1000000:.1f}M"
                elif volume_24h >= 1000:
                    vol_text = f"${volume_24h/1000:.1f}K"
                else:
                    vol_text = f"${volume_24h:.0f}" if volume_24h > 0 else "N/A"
            except:
                vol_text = "N/A"
            
            # 其他指标
            holders = token.get('holders', 0)
            tweet_count = token.get('tweetCount', 0)
            total_tweets = token.get('totalTweets', 0)
            smart_buy = token.get('smartBuyStats', 0)
            
            # V/M比率
            vm_ratio = token.get('vmRatio', 0)
            try:
                vm_ratio_text = f"{float(vm_ratio):.1f}" if vm_ratio else "0.0"
            except:
                vm_ratio_text = "0.0"
            
            # 策略信号（使用缓存的结果）
            strategy_html = "<span class='strategy-loading'>分析中...</span>"
            if hasattr(self, 'strategy_signals_cache') and token.get('tokenAddress') in self.strategy_signals_cache:
                cached_signal = self.strategy_signals_cache[token.get('tokenAddress')]
                signal_text = cached_signal['signal']
                signal_time = cached_signal['timestamp']
                
                # 标准化信号文本
                if signal_text.lower() == 'buy' or signal_text == '买入':
                    strategy_html = f"<span class='strategy-buy'>买入</span><span style='font-size:9px;color:#7f8c8d;'> {signal_time}</span>"
                elif signal_text.lower() == 'sell' or signal_text == '卖出':
                    strategy_html = f"<span class='strategy-sell'>卖出</span><span style='font-size:9px;color:#7f8c8d;'> {signal_time}</span>"
                elif signal_text.lower() == 'hold' or signal_text == '持有':
                    strategy_html = f"<span class='strategy-hold'>持有</span><span style='font-size:9px;color:#7f8c8d;'> {signal_time}</span>"
                else:
                    strategy_html = f"<span class='strategy-loading'>{signal_text}</span><span style='font-size:9px;color:#7f8c8d;'> {signal_time}</span>"
            # else块被移除，因为strategy_html已经有默认值
            
            # 生成单行HTML - 改进的版本，添加更好的标识
            html_content += f"""
            <div class='token-row' data-index='{i}' data-symbol='{symbol}' onclick='selectToken({i})'>
                <div style='display: flex; justify-content: space-between; align-items: center;'>
                    <div style='flex: 1;'>
                        <span class='rank'>#{rank}</span>
                        <span class='token-symbol' data-symbol='{symbol}'>{symbol}</span>
                        <span class='token-name'>({display_name})</span>
                        {change_html}
                    </div>
                    <div style='flex: 2; display: flex; justify-content: space-between; font-size: 11px;'>
                        <span class='metric'>💰{cap_text}</span>
                        <span class='metric'>📊{vol_text}</span>
                        <span class='metric'>👥{holders}</span>
                        <span class='metric'>🗣️{tweet_count}</span>
                        <span class='metric'>V/M:{vm_ratio_text}</span>
                        {strategy_html}
                    </div>
                </div>
            </div>
            """
        
        # 🔥🔥 一次性设置HTML内容 - 超快速更新！
        self.trend_text_display.setHtml(html_content)
        
        # 更新状态
        self.status_label.setText(f"✅ 已加载 {len(display_tokens)} 个热门代币，实时分析中...")
        
        # 更新时间标签
        current_time = QDateTime.currentDateTime()
        self.last_update_label.setText(f"数据更新: {current_time.toString('MM-dd HH:mm:ss')}")
        self.last_update_label.setStyleSheet("""
            QLabel {
                color: #27ae60;
                font-size: 10px;
                font-weight: bold;
                padding: 2px 8px;
                background: rgba(39, 174, 96, 0.1);
                border: 1px solid #27ae60;
                border-radius: 3px;
            }
        """)
        
        # 🔥🔥 启动后台分析（保持原有逻辑）
        if trigger_background_refresh: # 🔥🔥 新增条件控制
            if self.selected_strategy and display_tokens:
                combined_tokens = display_tokens.copy()
                
                # 添加持仓数据
                if hasattr(self, 'holdings_list_normalized') and self.holdings_list_normalized:
                    print(f"🔥🔥 DEBUG: 添加 {len(self.holdings_list_normalized)} 个持仓代币到后台监控")
                    for i, holding in enumerate(self.holdings_list_normalized[:3]):  # 只打印前3个
                        print(f"    持仓 {i+1}: {holding.get('symbol', 'Unknown')} ({holding.get('tokenAddress', 'No Address')[:10]}...)")
                    combined_tokens.extend(self.holdings_list_normalized)
                else:
                    print(f"🔥🔥 DEBUG: 没有持仓数据添加到后台监控")
                    print(f"    hasattr holdings_list_normalized: {hasattr(self, 'holdings_list_normalized')}")
                    if hasattr(self, 'holdings_list_normalized'):
                        print(f"    holdings_list_normalized 长度: {len(self.holdings_list_normalized)}")
                
                print(f"🔥🔥 DEBUG: combined_tokens 总数: {len(combined_tokens)} (趋势币: {len(display_tokens)}, 持仓: {len(self.holdings_list_normalized) if hasattr(self, 'holdings_list_normalized') else 0})")
                
                self.update_background_charts(combined_tokens)
                
                # 延迟触发分析
                QTimer.singleShot(1000, self.trigger_background_analysis)
        
        # 🔥 共享数据给监控弹窗
        if not hasattr(self, '_data_sharing_in_progress') or not self._data_sharing_in_progress:
            self._data_sharing_in_progress = True
            self.trend_data_shared.emit(display_tokens)
            QTimer.singleShot(2000, lambda: setattr(self, '_data_sharing_in_progress', False))
        
        logger.info("=== 趋势币富文本更新完成 ===")
    
    @pyqtSlot(str)
    def handle_trend_data_error(self, error_message: str):
        """处理趋势数据获取错误"""
        self.status_label.setText(f"❌ 获取趋势数据失败: {error_message}")
        # QMessageBox.warning(self, "数据获取失败", f"无法获取趋势币数据:\n{error_message}")
        logger.error(f"🔥🔥 趋势数据获取失败: {error_message}")

    @pyqtSlot()
    def trend_data_finished(self):
        """趋势数据获取完成"""
        if self.trend_data_thread:
            self.trend_data_thread = None
    
    @pyqtSlot()
    def on_strategy_changed(self):
        """LiveTradingWidget顶部的策略选择改变"""
        strategy_text_with_prefix = self.strategy_combo.currentText()
        print(f"🔥🔥 ON_STRATEGY_CHANGED called with: '{strategy_text_with_prefix}'")
        print(f"   当前selected_strategy: {self.selected_strategy.name if self.selected_strategy else None}")
        print(f"   策略组合框总数: {self.strategy_combo.count()}")
        for i in range(self.strategy_combo.count()):
            print(f"     {i}: {self.strategy_combo.itemText(i)}")
        
        if strategy_text_with_prefix and strategy_text_with_prefix != "选择策略...":
            strategy_name = strategy_text_with_prefix.replace("📊 ", "")
            strategy_instance = StrategyFactory.get_strategy_by_name(strategy_name)
            
            if strategy_instance:
                self.selected_strategy = strategy_instance
                self.status_label.setText(f"已选择策略: {strategy_name}")
                logger.info(f"LiveTradingWidget: 实际选中策略实例: {strategy_name}")
                
                # 启用策略执行按钮（如果已选择代币）
                # if self.current_chart_token:
                #     self.execute_strategy_button.setEnabled(True)
                #     logger.info(f"策略执行按钮已启用 - 策略: {strategy_name}")
                
                # 清空当前代币的旧策略信号记录
                if self.current_chart_token:
                    current_address = self.current_chart_token.get('tokenAddress') or self.current_chart_token.get('address')
                    current_symbol = self.current_chart_token.get('symbol')
                    
                    # 移除当前代币的信号记录（保留真实交易和模拟交易）
                    self.trade_records = [
                        record for record in self.trade_records
                        if not (
                            (record.get('address') == current_address or record.get('symbol') == current_symbol) and
                            record.get('status') in ['信号', '已执行'] and
                            record.get('is_signal', True)  # 修复：应该是True，表示是信号记录
                        )
                    ]
                    logger.info(f"清空了当前代币 {current_symbol} 的旧策略信号记录")
                
                # 当策略改变时，如果当前有显示的图表，则用新策略刷新它
                if self.current_chart_token and hasattr(self, 'actual_chart_widget') and self.actual_chart_widget:
                    logger.info(f"LiveTradingWidget: 使用新策略 '{strategy_name}' 刷新当前图表: {self.current_chart_token.get('symbol')}")
                    self.load_token_chart(self.current_chart_token) # 会传递新的策略名
                
                # 🔥🔥 新增：更新并行 ChartWidget 的策略设置
                self.update_parallel_chart_strategy(strategy_name)
                
                # 🔥🔥 新增：如果已有趋势代币数据，重新创建并行图表
                if self.trend_tokens:
                    logger.info(f"🔥🔥 策略变更，重新创建前 {len(self.trend_tokens)} 个热门代币的并行图表（最大{self.MAX_DISPLAY_TOKENS}个）")
                    self.update_background_charts(self.trend_tokens) # 🔥 NEW CALL
                    # 立即触发一次并行分析
                    QTimer.singleShot(2000, self.trigger_background_analysis)  # 延迟2秒等待图表创建完成
                
                # 更新交易记录过滤器（如果选择了"当前策略"）
                if self.strategy_filter_combo.currentText() == "当前策略":
                    self.update_trade_records_display()
                    self.update_trade_statistics()
                    
            else:
                logger.warning(f"LiveTradingWidget: 未找到策略实例: {strategy_name}")
                self.selected_strategy = None
                # self.execute_strategy_button.setEnabled(False)
        else:
            self.selected_strategy = None
            # self.execute_strategy_button.setEnabled(False)
            self.status_label.setText("请选择一个策略")
            
            # 清空当前代币的策略信号记录
            if self.current_chart_token:
                current_address = self.current_chart_token.get('tokenAddress') or self.current_chart_token.get('address')
                current_symbol = self.current_chart_token.get('symbol')
                
                self.trade_records = [
                    record for record in self.trade_records
                    if not (
                        (record.get('address') == current_address or record.get('symbol') == current_symbol) and
                        record.get('status') in ['信号', '已执行'] and
                        record.get('is_signal', True)  # 修复：应该是True，表示是信号记录
                    )
                ]
                logger.info(f"清空了当前代币 {current_symbol} 的策略信号记录（未选择策略）")
            
            # 如果取消选择策略，也应更新图表（不显示策略信号）
            if self.current_chart_token and hasattr(self, 'actual_chart_widget') and self.actual_chart_widget:
                logger.info(f"LiveTradingWidget: 清除当前图表策略信号: {self.current_chart_token.get('symbol')}")
                self.load_token_chart(self.current_chart_token) # 会传递 "不显示" 策略
            
            # 🔥🔥 新增：清理并行图表（未选择策略时）
            self.update_background_charts([]) # 🔥 NEW CALL to clear active charts
            self.update_background_analysis_status("等待策略选择")
            
            # 更新交易记录过滤器
            if self.strategy_filter_combo.currentText() == "当前策略":
                self.update_trade_records_display()
                self.update_trade_statistics()

    # 🔥🔥 新增：更新并行 ChartWidget 的策略设置
    def update_parallel_chart_strategy(self, strategy_name: str):
        """更新所有并行 HeadlessChartWidget 的策略设置"""
        try:
            # 🔥🔥 修复：使用 background_chart_widgets
            # if not self.background_chart_widgets: # OLD
            if not self.active_background_charts: # 🔥 NEW: Check active charts
                logger.info(f"🔥🔥 无活跃后台图表组件，跳过策略更新")
            return

            updated_count = 0
            # 🔥🔥 修复：使用 background_chart_widgets
            # for token_address, chart_widget in self.background_chart_widgets.items(): # 🔥 Iterate over actual ChartWidget instances
            for token_address, chart_widget in list(self.active_background_charts.items()): # Iterate over a copy of active charts
                try:
                    # chart_widget已经是 ChartWidget 实例，不再从 chart_info['widget'] 获取
                    # token = chart_info['token'] # token信息现在直接在chart_widget.token_data中
                    token_data_for_log = chart_widget.token_data if chart_widget.token_data else {}
                    
                    if chart_widget and hasattr(chart_widget, 'current_strategy_name'):
                        # 更新图表组件的策略名称
                        chart_widget.current_strategy_name = strategy_name
                        
                        # HeadlessChartWidget 不包含 GUI 组件，跳过 strategy_combo 的更新
                        
                        # 重新加载代币数据以应用新策略
                        if chart_widget.token_data: # 确保有token_data
                            token_for_chart = chart_widget.token_data.copy()
                            token_for_chart['source'] = 'trend' # Or chart_widget.source
                            token_for_chart['timeframe'] = chart_widget.timeframe or '1m'  # 直接使用 timeframe 属性
                            # 确保去除表情符号前缀
                            clean_strategy_name = strategy_name.replace("📊 ", "") if strategy_name and strategy_name != "选择策略..." else strategy_name
                            token_for_chart['strategy_name'] = clean_strategy_name # 使用清理后的策略名
                            
                            chart_widget.set_token(token_for_chart)
                            updated_count += 1
                            logger.debug(f"更新后台图表策略: {token_data_for_log.get('symbol', 'Unknown')} -> {strategy_name}")
                        else:
                            logger.warning(f"后台图表 {token_address[:6]} 无token_data，跳过策略更新")
                    
                except Exception as e:
                    logger.error(f"更新单个后台图表策略失败 ({token_address[:6]}...): {e}")
                    continue
            
            logger.info(f"🔥🔥 后台图表策略更新完成: {updated_count}/{len(self.active_background_charts)} 个")
            
            # 更新状态显示
            if updated_count > 0:
                # 🔥🔥 修复：使用 update_background_analysis_status
                self.update_background_analysis_status(f"策略已更新: {strategy_name} ({updated_count} 个组件)")
                
        except Exception as e:
            logger.error(f"更新后台图表策略失败: {e}")
            # 🔥🔥 修复：使用 update_background_analysis_status
            self.update_background_analysis_status("策略更新失败", error=True)

    @pyqtSlot(dict, list) 
    def on_chart_data_loaded(self, token_info: Dict, ohlcv_list: list):
        logger.info(f"LiveTradingWidget.on_chart_data_loaded (已废弃) for {token_info.get('symbol')}")
        # self.update_indicators_summary_from_df(df, symbol) # 需要df
        # 暂时只更新一个通用状态
        if hasattr(self, 'actual_chart_widget') and self.actual_chart_widget and self.actual_chart_widget.df is not None:
            self.update_indicators_summary(self.actual_chart_widget.df, token_info.get('symbol', "Unknown"))
        else:
            self.indicators_summary.setText(f"技术指标：等待 {token_info.get('symbol', 'Unknown')} 图表更新完成...")

    @pyqtSlot(str)
    def on_chart_data_error(self, error_msg: str):
        logger.warning(f"LiveTradingWidget.on_chart_data_error (已废弃): {error_msg}")
        self.indicators_summary.setText(f"技术指标：图表加载失败 - {error_msg}")

    def update_indicators_summary(self, df: pd.DataFrame, symbol: str):
        """根据DataFrame和当前选定策略更新技术指标摘要"""
        try:
            if df is None or df.empty:
                self.indicators_summary.setText(f"技术指标 ({symbol}): 无K线数据")
                return

            summary_parts = []
            latest_close = df['close'].iloc[-1]
            summary_parts.append(f"最新价: ${latest_close:.4f}") # 假设价格格式

            # 获取当前LiveTradingWidget选中的策略
            active_strategy_name = "无"
            strategy_signal_text = ""

            if self.selected_strategy: # 这是策略实例
                active_strategy_name = self.selected_strategy.name
                # 为摘要重新生成信号 (或者从ChartWidget获取已经带有信号的df)
                # 为了简单，我们假设ChartWidget的df已经包含了基于这个策略的信号列
                if 'signal' in df.columns:
                    latest_signal_val = df['signal'].iloc[-1]
                    if latest_signal_val == 1:
                        strategy_signal_text = "买入"
                    elif latest_signal_val == -1:
                        strategy_signal_text = "卖出"
                    else:
                        # 检查持有状态
                        recent_signals = df['signal'].tail(10) 
                        if any(recent_signals == 1) and not any(recent_signals == -1):
                            strategy_signal_text = "持有"
                        else:
                            strategy_signal_text = "观察"
                    summary_parts.append(f"策略({active_strategy_name}): {strategy_signal_text}")
                else:
                    summary_parts.append(f"策略({active_strategy_name}): 信号列缺失")
            else:
                summary_parts.append("策略: 未选")

            # 可以从df中提取其他通用指标 (如RSI, MACD状态)
            if 'rsi' in df.columns:
                rsi_val = df['rsi'].iloc[-1]
                rsi_status = "超买" if rsi_val > 70 else ("超卖" if rsi_val < 30 else "中性")
                summary_parts.append(f"RSI: {rsi_val:.1f} ({rsi_status})")
            
            if 'macd' in df.columns and 'macd_signal' in df.columns:
                macd_val = df['macd'].iloc[-1]
                signal_val = df['macd_signal'].iloc[-1]
                macd_status = "金叉区域" if macd_val > signal_val else ("死叉区域" if macd_val < signal_val else "缠绕")
                summary_parts.append(f"MACD: {macd_status}")

            self.indicators_summary.setText(f"指标摘要 ({symbol}): " + " | ".join(summary_parts))
            logger.info(f"LiveTradingWidget: 更新了 {symbol} 的指标摘要: {" | ".join(summary_parts)}")

        except Exception as e:
            logger.error(f"更新指标摘要失败 for {symbol}: {e}", exc_info=True)
            self.indicators_summary.setText(f"技术指标 ({symbol}): 处理错误")

    @pyqtSlot(int, int)
    def on_trend_token_clicked(self, row: int, column: int):
        """趋势币表格点击事件"""
        try:
            logger.info(f"点击代币表格：行{row}，列{column}")
            
            # 从代币信息组件中获取代币数据（已存储完整数据）
            token_widget = self.trend_table.cellWidget(row, 1)
            if not token_widget or not hasattr(token_widget, 'token_data') or not token_widget.token_data:
                logger.warning(f"无法获取行{row}的代币数据")
                return
            
            token = token_widget.token_data
            
            symbol = token.get('symbol', 'Unknown')
            logger.info(f"选择代币: {symbol}")
            
            # 清空所有现有的策略信号记录，为新选择的代币做准备
            self.trade_records.clear()
            self.trade_record_id_counter = 0
            logger.info(f"已清空所有信号记录，为新代币 {symbol} 做准备")
            
            # 加载图表
            self.load_token_chart(token)
            
            # 更新信号记录显示（只显示当前代币的记录）
            self.update_trade_records_display()
            self.update_trade_statistics()
            
            # 启用策略执行按钮（如果已选择策略）
            # if self.selected_strategy:
            #     self.execute_strategy_button.setEnabled(True)
            #     logger.info(f"策略执行按钮已启用 - 代币: {symbol}, 策略: {self.selected_strategy.name}")
            # else:
            #     self.execute_strategy_button.setEnabled(False)
            #     logger.info(f"策略执行按钮禁用 - 代币: {symbol}, 未选择策略")
            
            # 🔥 启用买入按钮（选中代币后即可买入）
            self.buy_button.setEnabled(True)

            # 重置代币余额并更新卖出按钮状态
            self.current_token_balance = 0
            self.update_sell_button_state()
            
            # 🔥 自动刷新代币余额
            if self.wallet_connected and hasattr(self, 'wallet_address'):
                self.refresh_token_balance()
            else:
                self.token_balance_label.setText("余额: 未连接钱包")
                self.update_balance_display("余额: 未连接钱包", error=True)
                
        except Exception as e:
            logger.error(f"处理代币点击事件失败: {e}")
            logger.error(f"🔥 买入按钮异常后状态: enabled={self.buy_button.isEnabled()}, text='{self.buy_button.text()}'")
    
    @pyqtSlot()
    def on_timeframe_changed(self):
        """时间周期变化"""
        if self.current_chart_token:
            self.load_token_chart(self.current_chart_token)
            # 时间周期改变时也更新信号记录显示
            self.update_trade_records_display()
    
    def load_token_chart(self, token: Dict):
        """加载代币图表"""
        try:
            self.current_chart_token = token
            symbol = token.get('symbol', 'Unknown')
            name = token.get('name', 'Unknown')
            price = token.get('price', 0)
            token_address = token.get('tokenAddress', '')
            
            # 🔥 增强代币信息显示 - 包含符号、名称、价格和地址
            price_text = f"${price:.8f}" if price < 0.00001 else f"${price:.6f}"
            short_address = f"{token_address[:6]}...{token_address[-4:]}" if token_address else "地址未知"
            
            # 多行显示更多信息
            token_info_html = f"""
            <div style="color: #ecf0f1; font-weight: bold;">
                📊 <span style="color: #3498db; font-size: 14px;">{symbol}</span> 
                <span style="color: #95a5a6; font-size: 11px;">({name})</span>
                <br/>
                <span style="color: #e67e22; font-size: 11px;">💰 {price_text}</span> 
                <span style="color: #7f8c8d; font-size: 9px;">📍 {short_address}</span>
            </div>
            """
            
            self.selected_token_label.setText(token_info_html)
            
            timeframe = self.timeframe_combo.currentText()
            self.indicators_summary.setText("技术指标：数据加载中...")
            
            if hasattr(self, 'actual_chart_widget') and self.actual_chart_widget:
                token_for_chart = token.copy()
                token_for_chart['source'] = 'trend'
                token_for_chart['timeframe'] = timeframe
                
                # 从 LiveTradingWidget 的 selected_strategy 获取策略名称
                current_live_strategy_name = "不显示" # 默认不显示
                if self.selected_strategy: # self.selected_strategy 是策略实例
                    current_live_strategy_name = self.selected_strategy.name
                elif self.strategy_combo.currentText() != "选择策略...":
                    # 如果self.selected_strategy未设置 (例如刚启动，或选择了"选择策略...")
                    # 但下拉框中有有效策略名 (去除了 "📊 " 前缀)
                    combo_text = self.strategy_combo.currentText().replace("📊 ", "")
                    if combo_text != "选择策略...": # 确保不是占位符
                         # 验证这个名称是否是真实策略
                        if StrategyFactory.get_strategy_by_name(combo_text):
                            current_live_strategy_name = combo_text
                        else:
                            logger.warning(f"LiveTradingWidget: 下拉框中的策略名 '{combo_text}' 无效。")
                
                token_for_chart['strategy_name'] = current_live_strategy_name
                
                self.actual_chart_widget.set_token(token_for_chart)
                # ChartWidget 内部的 timeframe_combo 和 strategy_combo 会在 set_token 中被同步
                logger.info(f"LiveTradingWidget: 已请求ChartWidget加载 {symbol} ({timeframe}) 数据, 策略: {current_live_strategy_name}")
            else:
                logger.error("LiveTradingWidget: actual_chart_widget 未初始化")
                self.indicators_summary.setText("技术指标：图表组件错误")

        except Exception as e:
            logger.error(f"加载代币图表失败: {e}", exc_info=True)
            self.indicators_summary.setText(f"技术指标：加载错误 - {str(e)}")
    
    @pyqtSlot()
    def on_market_monitoring_changed(self):
        """市场监控状态改变"""
        self.market_monitoring = self.market_monitor_checkbox.isChecked()
        status = "开启" if self.market_monitoring else "关闭"
        logger.info(f"市场监控: {status}")
        self.status_label.setText(f"市场监控已{status}")
    
    @pyqtSlot()
    def on_trading_toggle(self):
        """交易开关切换"""
        self.trading_enabled = self.trading_toggle.isChecked()
        
        if self.trading_enabled:
            # 检查是否已选择策略
            if not self.selected_strategy:
                QMessageBox.warning(self, "未选择策略", "请先选择交易策略才能开启实盘交易！")
                self.trading_toggle.setChecked(False)
                self.trading_enabled = False
                return
            
            # 开启实盘交易监控弹窗
            self.show_live_trading_monitor()
            
            self.trading_toggle.setText("🟢 交易：开")
            self.trading_toggle.setStyleSheet(TRADING_TOGGLE_ON_STYLE)
            self.status_label.setText(f"🚀 实盘交易已开启 - 策略: {self.selected_strategy.name}")
            logger.info(f"实盘交易开启 - 策略: {self.selected_strategy.name}")
        else:
            # 关闭实盘交易监控弹窗
            self.hide_live_trading_monitor()
            
            self.trading_toggle.setText("🔴 交易：关")
            self.trading_toggle.setStyleSheet(TRADING_TOGGLE_OFF_STYLE)
            self.status_label.setText("实盘交易已关闭")
            logger.info("实盘交易关闭")

    def show_live_trading_monitor(self):
        """显示实盘交易监控弹窗"""
        try:
            if not self.monitor_dialog:
                # 创建监控弹窗
                self.monitor_dialog = MonitorDialog(self)
                
                # 连接信号
                self.trend_data_shared.connect(self.monitor_dialog.on_trend_data_received)
                
                logger.info("创建并连接监控弹窗")
            
            # # 显示弹窗
            # self.monitor_dialog.show()
            
            # 立即发送当前数据
            if self.trend_tokens:
                self.trend_data_shared.emit(self.trend_tokens)
                logger.info(f"发送当前趋势币数据到监控弹窗: {len(self.trend_tokens)} 个代币")
            
            
        except Exception as e:
            logger.error(f"显示监控弹窗失败: {e}")
            QMessageBox.warning(self, "警告", f"无法显示监控弹窗: {str(e)}")
    
    def hide_live_trading_monitor(self):
        """隐藏实盘交易监控弹窗"""
        try:
            if self.monitor_dialog:
                self.monitor_dialog.hide()
                logger.info("隐藏监控弹窗")
        except Exception as e:
            logger.error(f"隐藏监控弹窗失败: {e}")

    @pyqtSlot()
    def test_chart_display(self):
        """测试图表显示功能"""
        try:
            # 创建模拟代币数据
            test_token = {
                'tokenAddress': 'test_address',
                'symbol': 'TEST',
                'name': 'Test Token',
                'price': 0.12345,
                'marketCap': 1000000
            }
            
            logger.info("测试图表显示功能")
            self.load_token_chart(test_token)
            
        except Exception as e:
            logger.error(f"测试图表功能失败: {e}")
     
    
    def closeEvent(self, event):
        """关闭事件处理"""
        try:
            logger.info("开始关闭LiveTradingWidget...")
            
            # 停止刷新定时器
            logger.info("停止刷新定时器...")
            if hasattr(self, 'refresh_timer') and self.refresh_timer:
                self.refresh_timer.stop()
            
            # 🔥🔥 停止相对时间更新定时器
            logger.info("停止相对时间更新定时器...")
            if hasattr(self, 'relative_time_timer') and self.relative_time_timer:
                self.relative_time_timer.stop()
            
            # 🔥🔥 停止并行分析定时器 (already removed, but good to double check if any other exists)
            # logger.info("停止并行分析定时器...")
            # if hasattr(self, 'parallel_analysis_timer') and self.parallel_analysis_timer:
            #     self.parallel_analysis_timer.stop()
            
            # 🔥🔥 清理所有并行 ChartWidget 实例 - NOW HANDLED BY POOL & QT PARENTING
            # Instead, explicitly stop activity on pooled charts if necessary before exit
            logger.info("Stopping activity on pooled background charts...")
            for chart_widget in self.background_chart_pool:
                chart_widget.stop_activity() # Call the new method
            self.background_chart_pool.clear()
            self.active_background_charts.clear()
            
            # 关闭实盘交易监控弹窗
            logger.info("正在关闭实盘交易监控弹窗...")
            if hasattr(self, 'monitor_dialog') and self.monitor_dialog:
                self.monitor_dialog.close()
            
            logger.info("所有组件已清理，关闭完成")
            
        except Exception as e:
            logger.error(f"关闭LiveTradingWidget时发生错误: {e}", exc_info=True)
        finally:
            event.accept()
    
    @pyqtSlot()
    def sell_token(self):
        """卖出代币 - 默认智能卖出"""
        try:
            # 检查钱包连接状态
            if not hasattr(self, 'wallet_address') or not self.wallet_address:
                QMessageBox.warning(self, "警告", "请先连接钱包！")
                return
            
            # 检查是否选择了代币
            if not self.current_chart_token:
                QMessageBox.warning(self, "警告", "请先选择要卖出的代币！")
                return
            
            token_address = self.current_chart_token.get('tokenAddress') or self.current_chart_token.get('address')
            token_symbol = self.current_chart_token.get('symbol', 'Unknown')
            
            if not token_address:
                QMessageBox.warning(self, "警告", "无效的代币地址！")
                return
            
            # 检查代币余额
            if not hasattr(self, 'current_token_balance') or self.current_token_balance <= 0:
                QMessageBox.warning(self, "警告", f"您没有 {token_symbol} 代币余额！")
                return
                
        except Exception as e:
            logger.error(f"卖出代币失败: {e}", exc_info=True)
            QMessageBox.critical(self, "错误", f"操作失败: {str(e)}")
    
    def sell_token_percentage(self, percentage: int):
        """按指定比例卖出代币"""
        try:
            token_address = self.current_chart_token.get('tokenAddress') or self.current_chart_token.get('address')
            token_symbol = self.current_chart_token.get('symbol', 'Unknown')
            
            # 预估滑点（简化处理）
            estimated_slippage = "5-10"  # 默认滑点范围
            
            # 显示确认对话框
            dialog = SellConfirmDialog(
                token_symbol=token_symbol,
                token_balance=str(self.current_token_balance),
                sell_percentage=percentage,
                parent=self,
                estimated_slippage=estimated_slippage,
                is_risk_token=False,  # 可以根据实际情况判断
                target_symbol="SOL"
            )

            # 打印卖出数量
            print(f"余额: {self.current_token_balance}");
            print(f"卖出数量: {self.current_token_balance * (percentage / 100.0)}");
            
            if dialog.exec_() == QDialog.Accepted:
                # 用户确认卖出，计算卖出数量
                sell_amount = self.current_token_balance * (percentage / 100.0)
                self.execute_real_sell(token_address, token_symbol, sell_amount)
                
        except Exception as e:
            logger.error(f"按比例卖出代币失败: {e}", exc_info=True)
            QMessageBox.critical(self, "错误", f"卖出失败: {str(e)}")
    
    def debug_buy_button_state(self):
        """调试买入按钮状态"""
        print(f"🔍 买入按钮调试信息:")
        print(f"   - 按钮启用状态: {self.buy_button.isEnabled()}")
        print(f"   - 按钮文本: '{self.buy_button.text()}'")
        print(f"   - 当前选择代币: {self.current_chart_token}")
        print(f"   - 钱包连接状态: {getattr(self, 'wallet_connected', 'N/A')}")
        print(f"   - DEX客户端状态: {bool(getattr(self, 'dex_client', None))}")
        
        # 记录到消息日志
        self.add_message_to_log(
            f"🔍 买入按钮状态: {'启用' if self.buy_button.isEnabled() else '禁用'} | "
            f"代币: {self.current_chart_token.get('symbol', 'None') if self.current_chart_token else 'None'} | "
            f"钱包: {'已连接' if getattr(self, 'wallet_connected', False) else '未连接'}",
            "debug"
        )
        
        # 🔥 如果有选择的代币，强制启用买入按钮
        if self.current_chart_token and not self.buy_button.isEnabled():
            print("🔧 强制启用买入按钮...")
            self.buy_button.setEnabled(True)
            self.add_message_to_log("🔧 已强制启用买入按钮", "info")

    @pyqtSlot()
    def buy_token(self):
        """执行代币买入"""
        try:
            logger.info(f"🔥 买入函数被调用")
            logger.info(f"🔥 买入按钮状态: enabled={self.buy_button.isEnabled()}, text='{self.buy_button.text()}'")
            logger.info(f"🔥 当前选择的代币: {self.current_chart_token}")
            
            # 🔥 添加详细的状态调试
            self.debug_buy_button_state()
            
            # 检查前置条件
            if not self.current_chart_token:
                logger.warning("🔥 买入失败: 未选择代币")
                QMessageBox.warning(self, "未选择代币", "请先选择一个代币进行买入")
                return
            
            if not self.wallet_connected or not hasattr(self, 'wallet_address'):
                QMessageBox.warning(self, "未连接钱包", "请先连接钱包")
                return
            
            if not self.dex_client:
                QMessageBox.warning(self, "交易功能不可用", "OKX DEX API 未初始化")
                return
            
            # 获取买入参数
            sol_amount = float(self.sol_amount_combo.currentText())
            token_symbol = self.current_chart_token.get('symbol', 'Unknown')
            token_name = self.current_chart_token.get('name', 'Unknown')
            token_address = self.current_chart_token.get('tokenAddress') or self.current_chart_token.get('address')
            
            if not token_address:
                QMessageBox.warning(self, "代币地址错误", "无法获取代币地址")
                return
            
            # 估算当前价格（用于日志记录）
            current_price = "未知"
            if hasattr(self, 'actual_chart_widget') and self.actual_chart_widget and hasattr(self.actual_chart_widget, 'df'):
                chart_df = self.actual_chart_widget.df
                if chart_df is not None and not chart_df.empty and 'close' in chart_df.columns:
                    latest_price = chart_df['close'].iloc[-1]
                    current_price = f"${latest_price:.6f}"
            
            # 🔥 移除确认对话框，直接执行买入
            logger.info(f"开始真实买入: {sol_amount} SOL -> {token_symbol} ({token_name})")
            logger.info(f"代币地址: {token_address}")
            logger.info(f"当前价格: {current_price}")
            logger.info(f"钱包地址: {self.wallet_address}")
            
            # 禁用买入按钮，防止重复点击
            self.buy_button.setEnabled(False)
            self.buy_button.setText("买入中...")
            
            # 更新状态
            self.status_label.setText(f"正在执行买入：{sol_amount} SOL → {token_symbol}")
            
            # 执行真实买入
            self.execute_real_buy(sol_amount, token_address, token_symbol)
            
        except Exception as e:
            logger.error(f"买入操作失败: {e}", exc_info=True)
            
            # 恢复按钮状态
            self.buy_button.setEnabled(True)
            self.buy_button.setText("💰 买入")
            
            # 更新状态
            self.status_label.setText(f"❌ 买入失败: {str(e)}")
            
            # 🔥 移除错误对话框，改为日志记录
            self.add_message_to_log(f"❌ 买入失败: {str(e)}", "error")
    
    def execute_real_sell(self, token_address: str, token_symbol: str, token_amount: float):
        """执行真实的卖出交易"""
        try:
            # 禁用卖出按钮，防止重复点击
            self.sell_button.setEnabled(False)
            self.sell_button.setText("🔄 卖出中...")

            # 检查是否有持仓
            if not self.has_holdings_data():
                logger.error("没有持仓数据，无法卖出")
                print(f"没有持仓数据，无法卖出")
                raise Exception("没有持仓数据，无法卖出")
            
            logger.info(f"开始卖出交易: {token_amount} {token_symbol} -> SOL")
            
            # 检查OKX客户端
            if not hasattr(self, 'dex_client') or not self.dex_client:
                logger.error("OKX客户端未初始化")
                print(f"OKX客户端未初始化")
                raise Exception("OKX客户端未初始化")


            # 查找特定代币
            token_holding = self.get_holdings_by_address(token_address)
            if not token_holding:
                logger.error(f"未找到代币 {token_address} 的持仓数据，无法卖出")
                print(f"未找到代币 {token_address} 的持仓数据，无法卖出")
                raise Exception(f"未找到代币 {token_address} 的持仓数据")
            
            # 获取代币数量
            token_rawBalance = token_holding.get('rawBalance', 0)
            token_balance = token_holding.get('balance', 0)
            token_decimals = token_holding.get('decimals', 6)
            
            logger.info(f"找到代币持仓: {token_symbol}")
            logger.info(f"  - 原始余额: {token_rawBalance}")
            logger.info(f"  - 格式化余额: {token_balance}")
            logger.info(f"  - 小数位数: {token_decimals}")
            logger.info(f"  - 请求卖出数量: {token_amount}")
            
            print(f"找到代币持仓: {token_symbol}")
            print(f"原始余额: {token_rawBalance}, 格式化余额: {token_balance}")
            print(f"请求卖出数量: {token_amount}, 小数位数: {token_decimals}")
            
            # 验证卖出数量不超过持仓
            if token_amount > token_balance:
                error_msg = f"卖出数量 {token_amount} 超过持仓数量 {token_balance}"
                logger.error(error_msg)
                print(error_msg)
                raise Exception(error_msg)
            
            # Solana链ID和SOL代币地址
            chain_id = "501"
            sol_token_address = "11111111111111111111111111111111"  # Native SOL的代币地址
            
            # 设置滑点（5%）
            slippage = "0.05"
            
            # 🔥 修复：使用 rawBalance 作为实际交易数量，确保是字符串格式
            token_amount_raw = str(token_rawBalance) if token_rawBalance else "0"

            print(f"最终交易参数:")
            print(f"  - token_amount (显示): {token_amount}")
            print(f"  - token_amount_raw (实际): {token_amount_raw}")
            print(f"  - 代币地址: {token_address}")
            print(f"  - SOL地址: {sol_token_address}")

            # 使用SwapRequest对象
            from okx_dex_client import SwapRequest
            swap_request = SwapRequest(
                chain_id=chain_id,
                from_token_address=token_address,  # SOL
                to_token_address=sol_token_address,
                amount=token_amount_raw,  # 转换为lamports
                slippage=slippage,
                user_wallet_address=self.wallet_address
            )
            
            logger.info(f"卖出请求参数: {token_symbol} -> SOL, 数量: {token_amount}, 滑点: {slippage}%")
            
            # 执行卖出交易
            result = self.dex_client.execute_swap(swap_request)
            
            if result.get('success'):
                # 交易成功
                transaction_data = result.get('data', {})
                tx_hash = transaction_data.get('txHash', 'N/A')
                
                # 🔥 移除成功提示对话框，直接记录到日志
                logger.info(f"✅ 卖出交易成功提交: {token_symbol} {token_amount:.6f}")
                logger.info(f"交易哈希: {tx_hash}")
                self.add_message_to_log(f"✅ 卖出成功: {token_symbol} {token_amount:.6f}", "success")
                
                # 记录交易
                trade_record = {
                    'timestamp': int(time.time()),
                    'type': 'SELL',
                    'token_symbol': token_symbol,
                    'token_address': token_address,
                    'amount': token_amount,
                    'price': 0,  # 卖出价格需要从交易结果中获取
                    'total_value': 0,  # 总价值需要计算
                    'status': 'PENDING',
                    'tx_hash': tx_hash,
                    'strategy': self.selected_strategy.name if self.selected_strategy else 'Manual',
                    'wallet_address': self.wallet_address
                }
                
                self.add_trade_record(trade_record)
                
                # 刷新余额
                self.refresh_token_balance()
                
                logger.info(f"卖出交易成功: {tx_hash}")
                
            else:
                # 交易失败
                error_msg = result.get('error', '未知错误')
                # 🔥 移除失败提示对话框，改为日志记录
                logger.error(f"❌ 卖出交易失败: {error_msg}")
                self.add_message_to_log(f"❌ 卖出失败: {error_msg}", "error")
                
        except Exception as e:
            logger.error(f"执行卖出交易失败: {e}", exc_info=True)
            # 🔥 移除异常提示对话框，改为日志记录
            self.add_message_to_log(f"❌ 卖出异常: {str(e)}", "error")
            
        finally:
            # 恢复卖出按钮状态
            self.sell_button.setText("💸 卖出")
            # 注意：这里不直接启用按钮，而是通过余额检查来决定是否启用
            self.update_sell_button_state()

    def execute_real_buy(self, sol_amount: float, token_address: str, token_symbol: str):
        """执行真实买入交易"""
        try:
            if not self.dex_client:
                raise Exception("OKX客户端未初始化")
            
            
            # Solana链ID和SOL代币地址
            chain_id = "501"
            sol_token_address = "11111111111111111111111111111111"  # Native SOL的代币地址
            

            # 🔥🔥 示例：如何在交易执行前检查唯一信号
            # # 检查是否有未处理的买入信号
            # unprocessed_signals = self.get_unprocessed_signals()
            # buy_signal = next((s for s in unprocessed_signals 
            #                   if s['token_address'] == token_address and s['signal_type'].lower() == 'buy'), None)
            # 
            # if not buy_signal:
            #     logger.warning(f"⚠️ 没有找到未处理的买入信号: {token_symbol}")
            #     return
            
            logger.info(f"🔄 开始执行买入交易: {sol_amount} SOL -> {token_symbol}")
            
            # 更新UI状态
            self.status_label.setText(f"🔄 正在买入 {token_symbol}...")
            QApplication.processEvents()
            
            # 使用SwapRequest对象
            from okx_dex_client import SwapRequest
            swap_request = SwapRequest(
                chain_id=chain_id,
                from_token_address=sol_token_address,  # SOL
                to_token_address=token_address,
                amount=str(int(sol_amount * 1e9)),  # 转换为lamports
                slippage="0.5",
                user_wallet_address=self.wallet_address
            )
            
            # 执行买入交易
            result = self.dex_client.execute_swap(swap_request)
            
            if result.get('success'):
                # 交易成功
                tx_hash = result.get('data', {}).get('txHash', 'Unknown')
                
                
                logger.info(f"✅ 买入成功: {sol_amount} SOL -> {token_symbol}, 交易哈希: {tx_hash}")
                
                # 更新UI
                self.status_label.setText(f"✅ 买入成功: {token_symbol}")
                
                # 添加交易记录
                trade_record = {
                    'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'type': '买入',
                    'token_symbol': token_symbol,
                    'token_address': token_address,
                    'amount': f"{sol_amount} SOL",
                    'strategy': self.selected_strategy.name if self.selected_strategy else "手动",
                    'status': '成功',
                    'tx_hash': tx_hash,
                    'notes': f"真实交易 - 花费 {sol_amount} SOL"
                }
                self.add_trade_record(trade_record)
                
                # 添加到消息日志
                self.add_message_to_log(f"✅ 买入成功: {token_symbol} (交易哈希: {tx_hash[:8]}...)", "success")
                
                # 自动刷新余额
                if self.current_chart_token and self.current_chart_token.get('tokenAddress') == token_address:
                    QTimer.singleShot(3000, self.refresh_token_balance)  # 3秒后刷新
                
            else:
                # 交易失败
                error_msg = result.get('error', 'Unknown error')
                logger.error(f"❌ 买入失败: {error_msg}")
                self.status_label.setText(f"❌ 买入失败: {error_msg}")
                
                # 添加失败记录
                trade_record = {
                    'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'type': '买入',
                    'token_symbol': token_symbol,
                    'token_address': token_address,
                    'amount': f"{sol_amount} SOL",
                    'strategy': self.selected_strategy.name if self.selected_strategy else "手动",
                    'status': '失败',
                    'tx_hash': '',
                    'notes': f"失败原因: {error_msg}"
                }
                self.add_trade_record(trade_record)
                
                # 添加到消息日志
                self.add_message_to_log(f"❌ 买入失败: {token_symbol} - {error_msg}", "error")
                
        except Exception as e:
            error_msg = str(e)
            logger.error(f"❌ 买入交易异常: {error_msg}", exc_info=True)
            self.status_label.setText(f"❌ 买入异常: {error_msg}")
            
            # 添加异常记录
            trade_record = {
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'type': '买入',
                'token_symbol': token_symbol,
                'token_address': token_address,
                'amount': f"{sol_amount} SOL",
                'strategy': self.selected_strategy.name if self.selected_strategy else "手动",
                'status': '异常',
                'tx_hash': '',
                'notes': f"异常: {error_msg}"
            }
            self.add_trade_record(trade_record)

    def auto_connect_wallet(self):
        """自动连接钱包（静默模式）"""
        try:
            # 检查OKX客户端是否可用
            if not self.dex_client:
                logger.info("OKX DEX客户端未初始化，跳过自动连接钱包")
                return
            
            logger.info("尝试自动连接钱包...")
            
            # 获取钱包信息（静默模式，不显示UI提示）
            wallet_info_result = self.dex_client.get_wallet_info()
            
            if wallet_info_result.get('success'):
                data = wallet_info_result.get('data', {})
                
                # 获取 Solana 钱包地址
                wallet_address = None
                supported_chains = data.get('supportedChains', {})
                
                if 'solana' in supported_chains:
                    solana_info = supported_chains['solana']
                    wallet_address = solana_info.get('walletAddress')
                
                # 也尝试从顶级字段获取
                if not wallet_address:
                    wallet_address = data.get('solanaWalletAddress')
                
                if wallet_address:
                    # 保存钱包地址
                    self.wallet_address = wallet_address
                    self.wallet_connected = True
                    
                    # 更新UI（静默模式，不弹出对话框）
                    self.wallet_button.setText(f"🟢 {wallet_address[:8]}...")
                    self.wallet_button.setEnabled(True)
                    self.wallet_button.setStyleSheet(WALLET_BUTTON_CONNECTED_STYLE)
                    
                    # 更新状态
                    self.status_label.setText(f"🎉 钱包已自动连接: {wallet_address[:8]}...{wallet_address[-8:]}")
                    
                    logger.info(f"钱包自动连接成功: {wallet_address}")
                    
                    # 🔥 **在此处添加 print 语句** 🔥
                    print(f"[DEBUG connect_wallet] Attempting to call refresh_holdings_data. Wallet connected: {self.wallet_connected}, Address: {self.wallet_address}")
                    self.refresh_holdings_data()

                    # 设置余额显示为等待选择代币状态
                    self.token_balance_label.setText("余额: 请选择代币")
                    self.update_balance_display("余额: 请选择代币", neutral=True)
                    
                    # 🔥 自动连接成功后，启用相关功能
                    self.update_sell_button_state()
                    
                else:
                    # 没有找到 Solana 钱包地址
                    logger.info("⚠️ 自动连接钱包失败：未找到 Solana 钱包地址")
                    self.status_label.setText("⚠️ 未找到Solana钱包，请手动连接")
            else:
                # API 调用失败
                error_msg = wallet_info_result.get('error', '未知错误')
                logger.info(f"⚠️ 自动连接钱包失败: {error_msg}")
                self.status_label.setText("⚠️ 钱包连接失败，请手动连接")
        
        except Exception as e:
            logger.warning(f"自动连接钱包异常: {e}")
            self.status_label.setText("⚠️ 钱包连接异常，请手动连接")

    @pyqtSlot()
    def connect_wallet(self):
        """连接钱包"""
        try:
            if not self.dex_client:
                QMessageBox.warning(self, "API不可用", "OKX DEX API 未初始化")
                return
            
            # 显示连接中状态
            self.wallet_button.setText("🔄 连接中...")
            self.wallet_button.setEnabled(False)
            self.status_label.setText("正在连接钱包...")
            
            # 获取钱包信息
            wallet_info_result = self.dex_client.get_wallet_info()
            
            if wallet_info_result.get('success'):
                data = wallet_info_result.get('data', {})
                
                # 获取 Solana 钱包地址
                wallet_address = None
                supported_chains = data.get('supportedChains', {})
                
                if 'solana' in supported_chains:
                    solana_info = supported_chains['solana']
                    wallet_address = solana_info.get('walletAddress')
                
                # 也尝试从顶级字段获取
                if not wallet_address:
                    wallet_address = data.get('solanaWalletAddress')
                
                if wallet_address:
                    # 保存钱包地址
                    self.wallet_address = wallet_address
                    self.wallet_connected = True
                    
                    # 更新UI
                    self.wallet_button.setText(f"🟢 {wallet_address[:8]}...")
                    self.wallet_button.setEnabled(True)
                    self.wallet_button.setStyleSheet(WALLET_BUTTON_CONNECTED_STYLE)
                    
                    # # 显示连接成功信息
                    # QMessageBox.information(
                    #     self, "钱包连接成功", 
                    #     f"🎉 成功连接到 Solana 钱包！\n\n"
                    #     f"钱包地址: {wallet_address}\n\n"
                    #     f"💡 您现在可以进行真实交易了"
                    # )
                    
                    logger.info(f"钱包连接成功: {wallet_address}")

                    self.refresh_holdings_data()

                    
                    # 🔥 钱包连接成功后，如果已选择代币则自动刷新余额
                    if self.current_chart_token:
                        self.refresh_token_balance()
                    else:
                        self.token_balance_label.setText("余额: 请选择代币")
                        self.update_balance_display("余额: 请选择代币", neutral=True)
                else:
                    # 没有找到 Solana 钱包地址
                    self.wallet_button.setText("🔗 连接钱包")
                    self.wallet_button.setEnabled(True)
                    
                    QMessageBox.warning(
                        self, "未找到钱包", 
                        "未能从 OKX DEX API 获取到 Solana 钱包地址\n\n"
                        "请确保：\n"
                        "• OKX 钱包已正确配置\n"
                        "• Solana 网络已启用\n"
                        "• API 服务正常运行"
                    )
                    
                    self.status_label.setText("❌ 未找到 Solana 钱包地址")
            else:
                # API 调用失败
                error_msg = wallet_info_result.get('error', '未知错误')
                
                self.wallet_button.setText("🔗 连接钱包")
                self.wallet_button.setEnabled(True)
                
                QMessageBox.critical(
                    self, "连接失败", 
                    f"无法获取钱包信息：\n\n{error_msg}\n\n"
                    f"请检查：\n"
                    f"• OKX DEX API 服务是否运行\n"
                    f"• 网络连接是否正常\n"
                    f"• 钱包是否已解锁"
                )
                
                self.status_label.setText(f"❌ 钱包连接失败: {error_msg}")
                logger.error(f"获取钱包信息失败: {error_msg}")
        
        except Exception as e:
            # 恢复按钮状态
            self.wallet_button.setText("🔗 连接钱包")
            self.wallet_button.setEnabled(True)
            
            logger.error(f"连接钱包失败: {e}", exc_info=True)
            QMessageBox.critical(self, "连接失败", f"连接钱包时发生错误：\n{str(e)}")
            self.status_label.setText(f"❌ 连接错误: {str(e)}")
    
    def add_trade_record(self, record_data: dict):
        """添加交易记录"""
        try:
            # 生成记录ID
            self.trade_record_id_counter += 1
            record_data['id'] = self.trade_record_id_counter
            
            # 如果记录包含图表时间戳，使用该时间戳；否则使用当前时间
            if 'chart_timestamp' in record_data and record_data['chart_timestamp']:
                # 将Unix时间戳转换为QDateTime
                chart_time = QDateTime.fromSecsSinceEpoch(record_data['chart_timestamp'])
                record_data['timestamp'] = chart_time
                logger.info(f"使用图表时间戳: {chart_time.toString('yyyy-MM-dd HH:mm:ss')}")
            else:
                record_data['timestamp'] = QDateTime.currentDateTime()
            
            # 添加到记录列表
            self.trade_records.append(record_data)
            
            # 更新显示
            self.update_trade_records_display()
            self.update_trade_statistics()
            
            logger.info(f"添加交易记录: {record_data}")
            
        except Exception as e:
            logger.error(f"添加交易记录失败: {e}")
    
    def update_trade_records_display(self):
        """更新交易记录显示"""
        try:
            # 获取过滤条件
            strategy_filter = self.strategy_filter_combo.currentText()
            time_filter = self.time_filter_combo.currentText()
            type_filter = self.type_filter_combo.currentText()
            status_filter = self.status_filter_combo.currentText()
            
            # 过滤记录 - 只显示当前选中代币的记录
            filtered_records = self.get_filtered_records(strategy_filter, time_filter, type_filter, status_filter)
            
            # 如果有当前选中的代币，进一步过滤只显示该代币的记录
            if self.current_chart_token:
                current_token_address = self.current_chart_token.get('tokenAddress') or self.current_chart_token.get('address')
                current_token_symbol = self.current_chart_token.get('symbol')
                
                filtered_records = [
                    r for r in filtered_records 
                    if (r.get('address') == current_token_address or 
                        r.get('symbol') == current_token_symbol)
                ]
                
                logger.info(f"显示当前代币 {current_token_symbol} 的交易记录: {len(filtered_records)} 条")
            else:
                # 如果没有选中代币，不显示任何记录
                filtered_records = []
                logger.info("未选中代币，不显示交易记录")
            
            # 更新表格
            self.trade_records_table.setRowCount(len(filtered_records))
            
            for row, record in enumerate(filtered_records):
                # 时间
                time_item = QTableWidgetItem(record['timestamp'].toString("MM-dd HH:mm:ss"))
                self.trade_records_table.setItem(row, 0, time_item)
                
                # 代币
                token_text = f"{record.get('symbol', 'Unknown')}"
                token_item = QTableWidgetItem(token_text)
                token_item.setToolTip(f"{record.get('name', 'Unknown')}\n{record.get('address', '')}")
                self.trade_records_table.setItem(row, 1, token_item)
                
                # 类型
                trade_type = record.get('type', 'Unknown')
                type_item = QTableWidgetItem(trade_type)
                if trade_type == "买入":
                    type_item.setForeground(QColor('#27ae60'))
                    type_item.setFont(QFont("Arial", 9, QFont.Bold))
                elif trade_type == "卖出":
                    type_item.setForeground(QColor('#e74c3c'))
                    type_item.setFont(QFont("Arial", 9, QFont.Bold))
                self.trade_records_table.setItem(row, 2, type_item)
                
                # 策略
                strategy_item = QTableWidgetItem(record.get('strategy', 'Manual'))
                self.trade_records_table.setItem(row, 3, strategy_item)
                
                # 价格
                price = record.get('price', 0)
                price_item = QTableWidgetItem(f"${price:.6f}" if price < 1 else f"${price:.2f}")
                self.trade_records_table.setItem(row, 4, price_item)
                
                # 数量
                amount = record.get('amount', 0)
                amount_unit = record.get('amount_unit', '')
                amount_item = QTableWidgetItem(f"{amount:.4f} {amount_unit}")
                self.trade_records_table.setItem(row, 5, amount_item)
                
                # 价值
                value = record.get('value', 0)
                value_item = QTableWidgetItem(f"${value:.2f}")
                self.trade_records_table.setItem(row, 6, value_item)
                
                # 状态
                status = record.get('status', 'Unknown')
                status_item = QTableWidgetItem(status)
                
                # 为不同类型的信号添加特殊标识
                if status == "信号":
                    if record.get('is_trend_analysis', False):
                        status_item.setText("趋势信号")
                        status_item.setForeground(QColor('#3498db'))  # 蓝色表示趋势分析信号
                        status_item.setFont(QFont("Arial", 9, QFont.Bold))
                        status_item.setToolTip("趋势分析信号 - 基于市场数据分析生成，不对应具体K线时间点")
                    elif record.get('chart_timestamp'):
                        status_item.setText("图表信号")
                        status_item.setForeground(QColor('#2980b9'))  # 深蓝色表示图表信号
                        status_item.setFont(QFont("Arial", 9, QFont.Bold))
                        status_item.setToolTip("图表信号 - 基于K线图技术分析生成，可在图表上闪烁显示")
                    else:
                        status_item.setForeground(QColor('#3498db'))  # 默认蓝色
                        status_item.setFont(QFont("Arial", 9, QFont.Bold))
                elif status == "已执行":
                    if record.get('is_trend_analysis', False):
                        status_item.setText("趋势已执行")
                        status_item.setForeground(QColor('#16a085'))  # 青绿色表示已执行的趋势信号
                        status_item.setFont(QFont("Arial", 9, QFont.Bold))
                        status_item.setToolTip("已执行的趋势分析信号")
                    else:
                        status_item.setText("图表已执行")
                        status_item.setForeground(QColor('#16a085'))  # 青绿色表示已执行的图表信号
                        status_item.setFont(QFont("Arial", 9, QFont.Bold))
                        status_item.setToolTip("已执行的图表信号")
                elif status == "成功":
                    status_item.setForeground(QColor('#27ae60'))
                elif status == "失败":
                    status_item.setForeground(QColor('#e74c3c'))
                elif status == "待定":
                    status_item.setForeground(QColor('#f39c12'))
                elif status == "模拟":
                    status_item.setForeground(QColor('#9b59b6'))  # 紫色表示模拟
                    status_item.setFont(QFont("Arial", 9, QFont.Bold))
                    status_item.setToolTip("策略回测模拟交易")
                
                self.trade_records_table.setItem(row, 7, status_item)
                
                # 盈亏
                profit = record.get('profit', 0)
                profit_pct = record.get('profit_pct', 0)
                if profit != 0:
                    profit_text = f"{profit_pct:+.1f}%"
                    profit_item = QTableWidgetItem(profit_text)
                    if profit > 0:
                        profit_item.setForeground(QColor('#27ae60'))
                        profit_item.setFont(QFont("Arial", 9, QFont.Bold))
                    else:
                        profit_item.setForeground(QColor('#e74c3c'))
                        profit_item.setFont(QFont("Arial", 9, QFont.Bold))
                else:
                    profit_item = QTableWidgetItem("-")
                    profit_item.setForeground(QColor('#95a5a6'))
                self.trade_records_table.setItem(row, 8, profit_item)
                
        except Exception as e:
            logger.error(f"更新交易记录显示失败: {e}")
    
    def get_filtered_records(self, strategy_filter: str, time_filter: str, type_filter: str, status_filter: str = "全部") -> list:
        """获取过滤后的记录"""
        filtered = self.trade_records.copy()
        
        # 策略过滤
        if strategy_filter == "当前策略" and self.selected_strategy:
            filtered = [r for r in filtered if r.get('strategy') == self.selected_strategy.name]
        
        # 时间过滤
        now = QDateTime.currentDateTime()
        if time_filter == "今日":
            today_start = QDateTime(now.date(), now.time().addSecs(-now.time().second() - now.time().minute() * 60 - now.time().hour() * 3600))
            filtered = [r for r in filtered if r['timestamp'] >= today_start]
        elif time_filter == "本周":
            week_start = now.addDays(-now.date().dayOfWeek() + 1)
            week_start = QDateTime(week_start.date(), week_start.time().addSecs(-week_start.time().second() - week_start.time().minute() * 60 - week_start.time().hour() * 3600))
            filtered = [r for r in filtered if r['timestamp'] >= week_start]
        elif time_filter == "本月":
            month_start = QDateTime(now.date().addDays(-now.date().day() + 1), now.time().addSecs(-now.time().second() - now.time().minute() * 60 - now.time().hour() * 3600))
            filtered = [r for r in filtered if r['timestamp'] >= month_start]
        
        # 类型过滤
        if type_filter != "全部":
            filtered = [r for r in filtered if r.get('type') == type_filter]
        
        # 状态过滤
        if status_filter != "全部":
            filtered = [r for r in filtered if r.get('status') == status_filter]
        
        # 按时间倒序排序
        filtered.sort(key=lambda x: x['timestamp'], reverse=True)
        
        return filtered
    
    def update_trade_statistics(self):
        """更新交易统计信息"""
        try:
            if not self.trade_records:
                self.trade_stats_label.setText("交易: 0 | 胜率: 0% | 盈亏: $0")
                self.quick_stats_label.setText("今日: $0 | 本周: $0")
                return
            
            # 计算总体统计
            total_trades = len(self.trade_records)
            profitable_trades = len([r for r in self.trade_records if r.get('profit', 0) > 0])
            win_rate = (profitable_trades / total_trades * 100) if total_trades > 0 else 0
            total_profit = sum(r.get('profit', 0) for r in self.trade_records)
            
            self.trade_stats_label.setText(
                f"交易: {total_trades} | 胜率: {win_rate:.1f}% | 盈亏: ${total_profit:+,.2f}"
            )
            
            # 计算今日和本周统计
            now = QDateTime.currentDateTime()
            today_start = QDateTime(now.date(), now.time().addSecs(-now.time().second() - now.time().minute() * 60 - now.time().hour() * 3600))
            week_start = now.addDays(-now.date().dayOfWeek() + 1)
            week_start = QDateTime(week_start.date(), week_start.time().addSecs(-week_start.time().second() - week_start.time().minute() * 60 - week_start.time().hour() * 3600))
            
            today_profit = sum(r.get('profit', 0) for r in self.trade_records if r['timestamp'] >= today_start)
            week_profit = sum(r.get('profit', 0) for r in self.trade_records if r['timestamp'] >= week_start)
            
            self.quick_stats_label.setText(
                f"今日: ${today_profit:+,.2f} | 本周: ${week_profit:+,.2f}"
            )
            
        except Exception as e:
            logger.error(f"更新交易统计失败: {e}")
    
    @pyqtSlot()
    def filter_trade_records(self):
        """过滤信号记录"""
        self.update_trade_records_display()
    
    @pyqtSlot()
    def clear_trade_records(self):
        """清空策略信号记录"""
        try:
            if not self.trade_records:
                QMessageBox.information(self, "无记录", "没有信号记录需要清空")
                return
            
            reply = QMessageBox.question(
                self, "确认清空",
                f"确定要清空所有 {len(self.trade_records)} 条策略信号记录吗？\n\n此操作不可撤销！",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                self.trade_records.clear()
                self.trade_record_id_counter = 0
                self.update_trade_records_display()
                self.update_trade_statistics()
                logger.info("已清空所有策略信号记录")
                
        except Exception as e:
            logger.error(f"清空策略信号记录失败: {e}")
    
    @pyqtSlot()
    def show_trade_context_menu(self, position):
        """显示信号记录右键菜单"""
        try:
            item = self.trade_records_table.itemAt(position)
            if item is None:
                return
            
            row = item.row()
            if row >= len(self.get_filtered_records(
                self.strategy_filter_combo.currentText(),
                self.time_filter_combo.currentText(),
                self.type_filter_combo.currentText(),
                self.status_filter_combo.currentText()
            )):
                return
            
            menu = QMenu(self)
            menu.setStyleSheet("""
                QMenu {
                    background-color: #34495e;
                    border: 1px solid #2c3e50;
                    padding: 5px;
                }
                QMenu::item {
                    color: #ecf0f1;
                    padding: 5px 20px;
                }
                QMenu::item:selected {
                    background-color: #3498db;
                }
            """)
            
            # 获取当前记录
            filtered_records = self.get_filtered_records(
                self.strategy_filter_combo.currentText(),
                self.time_filter_combo.currentText(),
                self.type_filter_combo.currentText(),
                self.status_filter_combo.currentText()
            )
            
            if row < len(filtered_records):
                current_record = filtered_records[row]
                
                # 如果是信号记录，添加"标记为已执行"选项
                if current_record.get('status') == '信号':
                    mark_executed_action = QAction("✅ 标记为已执行", self)
                    mark_executed_action.triggered.connect(lambda: self.mark_signal_as_executed(current_record))
                    menu.addAction(mark_executed_action)
                    menu.addSeparator()
            
            # 查看详情
            view_details_action = QAction("📋 查看详情", self)
            view_details_action.triggered.connect(lambda: self.view_trade_details(row))
            menu.addAction(view_details_action)
            
            # 复制交易ID
            copy_id_action = QAction("📋 复制交易ID", self)
            copy_id_action.triggered.connect(lambda: self.copy_trade_id(row))
            menu.addAction(copy_id_action)
            
            # 在区块浏览器查看（如果有交易哈希）
            if current_record.get('tx_hash'):
                view_explorer_action = QAction("🔗 在区块浏览器查看", self)
                view_explorer_action.triggered.connect(lambda: self.view_in_explorer(row))
                menu.addAction(view_explorer_action)
            
            menu.exec_(QCursor.pos())
            
        except Exception as e:
            logger.error(f"显示交易菜单失败: {e}")

    @pyqtSlot()
    def view_trade_details(self, row: int):
        """查看交易详情"""
        # TODO: 实现查看交易详情的对话框
        pass

    def copy_trade_id(self, row: int):
        """复制交易ID"""
        # TODO: 实现复制交易ID到剪贴板
        pass

    def view_in_explorer(self, row: int):
        """在区块浏览器查看"""
        # TODO: 实现在区块浏览器打开交易
        pass

    def mark_signal_as_executed(self, record: dict):
        """标记信号为已执行"""
        try:
            # 更新记录状态
            record['status'] = '已执行'
            record['executed_time'] = QDateTime.currentDateTime()

            # 更新显示
            self.update_trade_records_display()
            self.update_trade_statistics()

            logger.info(f"已手动标记信号为已执行: {record.get('symbol')} - {record.get('type')}")

            QMessageBox.information(
                self, "标记成功",
                f"已将 {record.get('symbol')} 的{record.get('type')}信号标记为已执行"
            )

        except Exception as e:
            logger.error(f"标记信号为已执行失败: {e}")
            QMessageBox.warning(self, "标记失败", f"无法标记信号: {str(e)}")

    @pyqtSlot()
    def on_trade_record_selected(self):
        """处理信号记录选择事件"""
        try:
            selected_rows = self.trade_records_table.selectionModel().selectedRows()
            if not selected_rows:
                # 没有选中任何行，停止闪烁
                if hasattr(self, 'actual_chart_widget') and self.actual_chart_widget:
                    self.actual_chart_widget.stop_signal_highlight()
                self.trade_summary_label.setText("选择信号记录可自动缩放图表到最佳展示区间 📊")
                return

            # 获取选中的第一行
            selected_row = selected_rows[0].row()

            # 获取过滤后的记录
            filtered_records = self.get_filtered_records(
                self.strategy_filter_combo.currentText(),
                self.time_filter_combo.currentText(),
                self.type_filter_combo.currentText(),
                self.status_filter_combo.currentText()
            )

            if selected_row >= len(filtered_records):
                return

            selected_record = filtered_records[selected_row]

            # 更新详情显示
            # Information about the currently displayed chart
            chart_token_display = "无图表"
            if self.current_chart_token:
                chart_token_display = self.current_chart_token.get('symbol', '图表代币未知')

            # Information from the selected record in the table
            record_symbol = selected_record.get('symbol', '未知')
            record_signal_type = selected_record.get('type', '未知') # Renamed from signal_type to avoid conflict
            record_price = selected_record.get('price', 0)
            record_strategy = selected_record.get('strategy', '未知策略') # Renamed from strategy
            record_timestamp = selected_record.get('chart_timestamp')
            record_is_trend_analysis = selected_record.get('is_trend_analysis', False)
            # Renamed record_type to record_display_type for clarity and to avoid conflict
            record_display_type = "趋势分析信号" if record_is_trend_analysis else "图表信号"

            # Construct the base detail text
            # Emphasize the current chart, and then the details of the selected record.
            detail_text = f"当前图表: [{chart_token_display}] - 选中记录: [{record_symbol} {record_signal_type} @ ${record_price:.6f}, 策略: {record_strategy}, 类型: {record_display_type}]"

            # 只对有chart_timestamp的图表信号记录进行闪烁处理
            # 趋势分析信号（is_trend_analysis=True）不支持闪烁，因为它们不对应具体的K线时间点
            highlight_message_suffix = "" # Initialize suffix
            if (selected_record.get('status') in ['信号', '已执行'] and
                record_timestamp and
                not selected_record.get('is_trend_analysis', False)):

                # chart_signal_type was defined inside on_trade_record_selected, but it should be derived from record_signal_type
                chart_signal_type_for_highlight = 'buy' if record_signal_type == '买入' else 'sell'
                # chart_index is fine as it comes from selected_record

                # 通知图表开始闪烁
                if hasattr(self, 'actual_chart_widget') and self.actual_chart_widget:
                    # 🔥 强制使用时间戳匹配，避免索引因数据刷新而错位
                    if record_timestamp: # Ensure record_timestamp is not None
                        # 🔥 调试：记录选中行和实际记录的信息
                        logger.info(f"🔍 选中行调试: 表格行={selected_row}, 记录={record_symbol}-{record_signal_type}")
                        logger.info(f"   └─ 存储索引={selected_record.get('chart_index')}, 时间戳={record_timestamp}, 价格={record_price}")
                        logger.info(f"   └─ ⚠️ 使用时间戳匹配避免索引错位问题")

                        # 🔥 强制使用时间戳查找，更可靠
                        self.actual_chart_widget.highlight_signal_point(record_timestamp, chart_signal_type_for_highlight, record_price)
                        logger.info(f"使用时间戳匹配闪烁图表信号点: {record_symbol} - {record_signal_type} @ {record_timestamp}")

                        # Check if highlighting was successful (assuming chart_widget might set a flag or return status)
                        # For now, we assume if it's called, it's attempting.
                        # A more robust way would be for highlight_signal_point to return a status or emit a signal.
                        # Let's assume for now that if we reach here, the attempt was made.
                        highlight_message_suffix = " 🎯已在图表上尝试高亮并缩放 (时间戳匹配)"
                    else:
                        logger.warning(f"信号记录缺少时间戳: {record_symbol} - {record_signal_type}")
                        highlight_message_suffix = " ⚠️ 信号记录缺少时间戳，无法定位"
                else:
                    highlight_message_suffix = " ⚠️ 图表组件未初始化"
            else:
                # 停止闪烁（对于趋势分析信号或其他不支持闪烁的记录）
                if hasattr(self, 'actual_chart_widget') and self.actual_chart_widget:
                    self.actual_chart_widget.stop_signal_highlight()

                # 根据不同情况显示相应的提示信息
                if record_is_trend_analysis:
                    logger.info(f"选中趋势分析信号，不支持K线闪烁: {record_symbol} - {record_signal_type}")
                    highlight_message_suffix = " ℹ️ 趋势分析信号不对应具体K线时间点"
                elif not record_timestamp:
                    highlight_message_suffix = " ℹ️ 此信号无对应的图表时间戳"
                elif selected_record.get('status') not in ['信号', '已执行']:
                    highlight_message_suffix = f" ℹ️ {selected_record.get('status')}状态不支持闪烁"

            # 更新详情显示
            self.trade_summary_label.setText(detail_text + highlight_message_suffix)

        except Exception as e:
            logger.error(f"处理信号记录选择失败: {e}")
            self.trade_summary_label.setText("❌ 处理选择事件时出错")

    def on_chart_signal_generated(self, signal_type: str, price: float, timestamp: int, strategy_name: str, chart_index: int):
        """处理图表生成的买卖信号"""
        try:
            if not self.current_chart_token:
                return

            # 只处理当前选中策略的信号
            if not self.selected_strategy or self.selected_strategy.name != strategy_name:
                logger.info(f"忽略非当前策略的信号: {strategy_name} (当前策略: {self.selected_strategy.name if self.selected_strategy else '无'})")
                return

            # 检查是否已存在相同时间点和策略的信号
            existing_signal = False
            for record in self.trade_records:
                if (record.get('address') == (self.current_chart_token.get('tokenAddress') or self.current_chart_token.get('address')) and
                    record.get('chart_timestamp') == timestamp and
                    record.get('type') == ('买入' if signal_type == 'buy' else '卖出') and
                    record.get('strategy') == strategy_name):
                    existing_signal = True
                    break

            if existing_signal:
                logger.info(f"信号已存在，跳过添加: {strategy_name} - {signal_type} @ {timestamp}")
                return

            # 直接使用传入的chart_index，这是最精确的位置
            logger.info(f"接收到图表信号，使用传入的精确索引: {chart_index}")

            # 创建交易记录
            trade_record = {
                'symbol': self.current_chart_token.get('symbol', 'Unknown'),
                'name': self.current_chart_token.get('name', 'Unknown'),
                'address': self.current_chart_token.get('tokenAddress') or self.current_chart_token.get('address', ''),
                'type': '买入' if signal_type == 'buy' else '卖出',
                'strategy': strategy_name,  # 使用传入的策略名称
                'price': price,
                'amount': 1,  # 默认数量
                'amount_unit': self.current_chart_token.get('symbol', 'TOKEN'),
                'value': price * 1,
                'status': '信号',
                'profit': 0,
                'profit_pct': 0,
                'is_signal': True,
                'chart_timestamp': timestamp,  # 记录图表信号的时间戳
                'chart_index': chart_index      # 🔥 使用传入的精确索引位置
            }

            self.add_trade_record(trade_record)
            logger.info(f"从图表添加信号记录: {trade_record['symbol']} - {trade_record['type']} @ ${price:.6f} (策略: {strategy_name}, 精确索引: {chart_index})")

        except Exception as e:
            logger.error(f"处理图表信号失败: {e}")

    @pyqtSlot()
    def refresh_token_balance(self):
        """刷新当前选中代币的余额"""
        try:
            # 防止重复查询
            if hasattr(self, 'balance_query_thread') and self.balance_query_thread and self.balance_query_thread.isRunning():
                logger.info("余额查询已在进行中，忽略重复请求")
                return

            if not self.current_chart_token:
                self.token_balance_label.setText("余额: 请选择代币")
                self.token_balance_label.setStyleSheet("""
                    QLabel {
                        color: #95a5a6;
                        font-size: 9px;
                        font-weight: normal;
                        padding: 2px 6px;
                        background: rgba(149, 165, 166, 0.1);
                        border: 1px solid #95a5a6;
                        border-radius: 3px;
                        min-width: 60px;
                    }
                """)
                return

            if not self.wallet_connected or not hasattr(self, 'wallet_address'):
                self.token_balance_label.setText("余额: 未连接钱包")
                self.token_balance_label.setStyleSheet("""
                    QLabel {
                        color: #e74c3c;
                        font-size: 9px;
                        font-weight: bold;
                        padding: 2px 6px;
                        background: rgba(231, 76, 60, 0.1);
                        border: 1px solid #e74c3c;
                        border-radius: 3px;
                        min-width: 60px;
                    }
                """)
                return

            if not self.dex_client:
                self.token_balance_label.setText("余额: API不可用")
                self.token_balance_label.setStyleSheet("""
                    QLabel {
                        color: #e74c3c;
                        font-size: 9px;
                        font-weight: bold;
                        padding: 2px 6px;
                        background: rgba(231, 76, 60, 0.1);
                        border: 1px solid #e74c3c;
                        border-radius: 3px;
                        min-width: 60px;
                    }
                """)
                return

            # 显示查询中状态
            self.token_balance_label.setText("余额: 查询中...")
            self.token_balance_label.setStyleSheet("""
                QLabel {
                    color: #3498db;
                    font-size: 9px;
                    font-weight: bold;
                    padding: 2px 6px;
                    background: rgba(52, 152, 219, 0.1);
                    border: 1px solid #3498db;
                    border-radius: 3px;
                    min-width: 60px;
                }
            """)

            # 禁用刷新按钮防止重复请求
            self.refresh_balance_button.setEnabled(False)

            # 获取代币地址
            token_address = self.current_chart_token.get('tokenAddress') or self.current_chart_token.get('address')
            if not token_address:
                self.update_balance_display("地址错误", error=True)
                self.refresh_balance_button.setEnabled(True)
                return

            # 异步查询余额
            from PyQt5.QtCore import QThread
            self.balance_query_thread = TokenBalanceQueryThread(
                self.dex_client,
                self.wallet_address,
                token_address,
                self.current_chart_token.get('symbol', 'Unknown')
            )
            self.balance_query_thread.balance_result.connect(self.on_balance_query_result)
            self.balance_query_thread.balance_error.connect(self.on_balance_query_error)
            self.balance_query_thread.start()

        except Exception as e:
            logger.error(f"刷新代币余额失败: {e}")
            self.update_balance_display("查询失败", error=True)
            self.refresh_balance_button.setEnabled(True)

    @pyqtSlot(dict)
    def on_balance_query_result(self, result: dict):
        """处理余额查询结果"""
        try:
            self.refresh_balance_button.setEnabled(True)

            symbol = result.get('symbol', 'Unknown')
            balance = result.get('balance', 0)
            balance_formatted = result.get('balance_formatted', '0')

            # 保存当前代币余额
            self.current_token_balance = balance

            if balance > 0:
                # 🔥 增强余额显示 - 突出代币符号
                balance_text = f"💰 余额: {balance_formatted} <span style='color: #3498db; font-weight: bold;'>{symbol}</span>"
                self.update_balance_display(balance_text, success=True)
                logger.info(f"查询到代币余额: {symbol} = {balance_formatted}")
            else:
                # 无余额时也使用突出显示
                balance_text = f"💰 余额: 0 <span style='color: #95a5a6; font-weight: bold;'>{symbol}</span>"
                self.update_balance_display(balance_text, neutral=True)
                logger.info(f"代币余额为0: {symbol}")

            # 更新卖出按钮状态
            self.update_sell_button_state()

        except Exception as e:
            logger.error(f"处理余额查询结果失败: {e}")
            self.update_balance_display("处理失败", error=True)
            self.current_token_balance = 0
            self.update_sell_button_state()

    @pyqtSlot(str)
    def on_balance_query_error(self, error_msg: str):
        """处理余额查询错误"""
        self.refresh_balance_button.setEnabled(True)
        self.update_balance_display("查询失败", error=True)
        self.current_token_balance = 0
        self.update_sell_button_state()
        logger.error(f"代币余额查询失败: {error_msg}")

    def update_sell_button_state(self):
        """更新卖出按钮状态"""
        try:
            # 检查是否满足卖出条件
            can_sell = (
                hasattr(self, 'wallet_connected') and self.wallet_connected and  # 钱包已连接
                hasattr(self, 'current_chart_token') and self.current_chart_token and  # 已选择代币
                hasattr(self, 'current_token_balance') and self.current_token_balance > 0  # 有余额
            )

            self.sell_button.setEnabled(can_sell)

            if can_sell:
                symbol = self.current_chart_token.get('symbol', 'TOKEN')
                self.sell_button.setToolTip(f"点击智能卖出 {symbol} 代币（余额: {self.current_token_balance:.6f}）\n点击下拉箭头选择卖出比例")
                logger.debug(f"卖出按钮已启用 - 代币: {symbol}, 余额: {self.current_token_balance}")
            else:
                if not hasattr(self, 'wallet_connected') or not self.wallet_connected:
                    self.sell_button.setToolTip("请先连接钱包")
                elif not hasattr(self, 'current_chart_token') or not self.current_chart_token:
                    self.sell_button.setToolTip("请先选择要卖出的代币")
                else:
                    self.sell_button.setToolTip("当前代币余额为0，无法卖出")
                logger.debug("卖出按钮已禁用")

        except Exception as e:
            logger.error(f"更新卖出按钮状态失败: {e}")
            self.sell_button.setEnabled(False)

    def update_balance_display(self, text: str, success: bool = False, error: bool = False, neutral: bool = False):
        """更新余额显示样式"""
        if success:
            style = TOKEN_BALANCE_LABEL_SUCCESS_STYLE
        elif error:
            style = TOKEN_BALANCE_LABEL_ERROR_STYLE
        elif neutral:
            style = TOKEN_BALANCE_LABEL_NEUTRAL_STYLE
        else: # Default/pending
            style = TOKEN_BALANCE_LABEL_DEFAULT_STYLE

        self.token_balance_label.setText(text)
        self.token_balance_label.setStyleSheet(style)

    @pyqtSlot()
    def manual_trigger_parallel_analysis(self):
        """手动触发并行策略分析"""
        logger.info("手动触发并行策略分析...")
        
        # 先确保有选择的策略
        if not self.selected_strategy:
            self.update_background_analysis_status("请先选择策略", error=True)
            QMessageBox.warning(self, "缺少策略", "请先在左上角选择一个交易策略，然后再触发分析。")
            return
        
        # 检查是否有趋势币数据
        if not hasattr(self, 'trend_tokens') or not self.trend_tokens:
            self.update_background_analysis_status("请先获取趋势币数据", error=True)
            QMessageBox.warning(self, "缺少数据", "请先点击'刷新数据'按钮获取趋势币数据。")
            return
        
        # 触发并行分析
        self.trigger_background_analysis()

    @pyqtSlot()
    def refresh_trend_data(self):
        """刷新趋势代币数据并触发K线图更新和分析"""

        if self.trend_data_thread and self.trend_data_thread.isRunning():
            self.add_message_to_log("趋势数据刷新仍在进行中，跳过此次刷新。", "info")
            return

        self.trend_data_thread = TrendDataThread(parent=self) # 将LiveTradingWidget实例作为父对象传递
        self.trend_data_thread.data_updated.connect(self.update_trend_table)
        self.trend_data_thread.error_occurred.connect(self.handle_trend_data_error)
        self.trend_data_thread.finished.connect(self.trend_data_finished)
        self.trend_data_thread.start()

        # 在启动趋势数据刷新后，也调用刷新持仓数据
        if self.wallet_connected: # 仅当钱包连接时才刷新持仓
            self.refresh_holdings_data()
        else:
            self.add_message_to_log("钱包未连接，跳过持仓数据刷新。", "info")
    
    def on_parallel_processing_started(self, total_tasks: int):
        """并行处理开始"""
        logger.info(f"🚀 并行处理开始: {total_tasks} 个任务")
        self.update_parallel_analysis_status(f"多线程并发处理中... ({total_tasks} 个代币)")
    
    def on_parallel_task_completed(self, token_address: str, result: dict, task_type: str):
        """并行任务完成"""
        try:
            # 查找对应的代币
            token_symbol = "Unknown"
            for chart_info in self.parallel_chart_widgets.values():
                if chart_info['token'].get('tokenAddress') == token_address:
                    token_symbol = chart_info['token'].get('symbol', 'Unknown')
                    break
            
            source = result.get('source', 'unknown')
            if source == 'cache':
                cache_age = result.get('cache_age', 0)
                logger.debug(f"⚡ {token_symbol} 缓存完成 (年龄: {cache_age:.1f}秒)")
            elif source == 'api':
                api_time = result.get('api_time', 0)
                logger.debug(f"🌐 {token_symbol} API完成 (耗时: {api_time:.2f}秒)")
            
            # 这里可以处理具体的数据和分析逻辑
            # 例如：调用策略分析、更新趋势表格等
            
        except Exception as e:
            logger.error(f"处理并行任务完成事件失败: {e}")
    
    def on_parallel_task_failed(self, token_address: str, error: str, task_type: str):
        """并行任务失败"""
        try:
            # 查找对应的代币
            token_symbol = "Unknown"
            for chart_info in self.parallel_chart_widgets.values():
                if chart_info['token'].get('tokenAddress') == token_address:
                    token_symbol = chart_info['token'].get('symbol', 'Unknown')
                    # 更新状态
                    chart_info['status'] = 'error'
                    break
            
            logger.warning(f"❌ {token_symbol} 处理失败: {error}")
            
        except Exception as e:
            logger.error(f"处理并行任务失败事件失败: {e}")
    
    def on_parallel_all_completed(self, stats: dict):
        """所有并行任务完成"""
        try:
            total_tasks = stats.get('total_tasks', 0)
            completed = stats.get('completed', 0)
            failed = stats.get('failed', 0)
            total_time = stats.get('total_time', 0)
            success_rate = stats.get('success_rate', 0)
            cache_hit_rate = stats.get('cache_hit_rate', 0)
            
            logger.info(f"🎉 所有并行任务完成! 成功: {completed}/{total_tasks} ({success_rate:.1f}%), "
                       f"耗时: {total_time:.2f}秒, 缓存命中: {cache_hit_rate:.1f}%")
            
            # 更新状态显示
            if failed == 0:
                self.update_parallel_analysis_status(f"✅ 全部完成 {completed}/{total_tasks} ({total_time:.1f}秒)", success=True)
            else:
                self.update_parallel_analysis_status(f"完成 {completed}/{total_tasks}, 失败 {failed} ({total_time:.1f}秒)")
            
            # 触发后续的策略分析
            QTimer.singleShot(1000, self.trigger_strategy_analysis_after_data_loaded)

        except Exception as e:
            logger.error(f"处理所有任务完成事件失败: {e}")
    
    def trigger_strategy_analysis_after_data_loaded(self):
        """数据加载完成后触发策略分析"""
        try:
            if not self.selected_strategy:
                logger.info("未选择策略，跳过分析")
                return
            
            ready_count = 0
            for chart_info in self.parallel_chart_widgets.values():
                if chart_info['status'] in ['ready', 'loading']:
                    ready_count += 1
            
            if ready_count > 0:
                logger.info(f"🚀 触发策略分析: {ready_count} 个组件已就绪")
                self.trigger_background_analysis()
            else:
                logger.warning("没有就绪的组件，无法触发策略分析")
                
        except Exception as e:
            logger.error(f"触发策略分析失败: {e}")
    
    # 🚀🚀 修改原有的 _delayed_set_token 方法为兼容性保留

    # 🔥🔥 新增：更新后台分析状态的方法
    def update_background_analysis_status(self, message: str, error: bool = False, success: bool = False):
        """更新后台分析状态显示 (替代旧的 update_parallel_analysis_status)"""
        try:
            base_style = """
                font-size: 10px;
                font-weight: bold;
                padding: 2px 8px;
                border-radius: 3px;
            """
            if error:
                style = "QLabel {{ color: #e74c3c; background: rgba(231, 76, 60, 0.1); border: 1px solid #e74c3c; {} }}".format(base_style)
            elif success:
                style = "QLabel {{ color: #27ae60; background: rgba(39, 174, 96, 0.1); border: 1px solid #27ae60; {} }}".format(base_style)
            else: # Neutral/Pending
                style = "QLabel {{ color: #e67e22; background: rgba(230, 126, 34, 0.1); border: 1px solid #e67e22; {} }}".format(base_style)
            
            if hasattr(self, 'parallel_analysis_label') and self.parallel_analysis_label:
                self.parallel_analysis_label.setText(f"后台分析: {message}") # 更新文本前缀
                self.parallel_analysis_label.setStyleSheet(style)
                self.parallel_analysis_label.setToolTip(f"后台图表分析状态: {message}")
            else:
                logger.warning("parallel_analysis_label 未找到，无法更新后台分析状态")
        except Exception as e:
            logger.error(f"更新后台分析状态失败: {e}")

    def load_initial_data(self):
        """加载初始数据"""
        logger.info("开始加载初始数据...")
        
        # 🔥🔥 新增：尝试自动连接钱包
        self.try_auto_connect_wallet()
        
        # 自动获取趋势币数据
        self.refresh_trend_data()

    def try_auto_connect_wallet(self):
        """尝试自动连接钱包（带API健康检查）"""
        try:
            # 检查OKX客户端是否可用
            if not self.dex_client:
                logger.info("OKX DEX客户端未初始化，跳过自动连接钱包")
                return
            
            logger.info("🔗 开始检测OKX本地API...")
            
            # 🔥 首先进行健康检查，确认API可用
            try:
                health_result = self.dex_client.health_check()
                if not health_result.get('success'):
                    logger.info(f"OKX API健康检查失败: {health_result.get('error', 'Unknown')}")
                    return
                
                logger.info("✅ OKX本地API可访问，尝试获取钱包信息...")
                
                # 使用短超时时间尝试自动连接
                self.auto_connect_wallet()
                
            except Exception as api_error:
                logger.info(f"OKX API无法访问: {api_error}")
                return
                
        except Exception as e:
            logger.warning(f"自动连接钱包检测失败: {e}")

    def auto_connect_wallet(self):
        """自动连接钱包（静默模式）"""
        try:
            # 检查OKX客户端是否可用
            if not self.dex_client:
                logger.info("OKX DEX客户端未初始化，跳过自动连接钱包")
                return
            
            logger.info("🔄 尝试自动连接钱包...")
            
            # 获取钱包信息（静默模式，不显示UI提示）
            wallet_info_result = self.dex_client.get_wallet_info()
            
            if wallet_info_result.get('success'):
                data = wallet_info_result.get('data', {})
                
                # 获取 Solana 钱包地址
                wallet_address = None
                supported_chains = data.get('supportedChains', {})
                
                if 'solana' in supported_chains:
                    solana_info = supported_chains['solana']
                    wallet_address = solana_info.get('walletAddress')
                
                # 也尝试从顶级字段获取
                if not wallet_address:
                    wallet_address = data.get('solanaWalletAddress')
                
                if wallet_address:
                    # 保存钱包地址
                    self.wallet_address = wallet_address
                    self.wallet_connected = True
                    
                    # 更新UI（静默模式，不弹出对话框）
                    self.wallet_button.setText(f"🟢 {wallet_address[:8]}...")
                    self.wallet_button.setEnabled(True)
                    self.wallet_button.setStyleSheet(WALLET_BUTTON_CONNECTED_STYLE)
                    
                    # 更新状态
                    self.status_label.setText(f"🎉 钱包已自动连接: {wallet_address[:8]}...{wallet_address[-8:]}")
                    
                    logger.info(f"✅ 钱包自动连接成功: {wallet_address}")
                    
                    # 🔥 **在此处添加 print 语句** 🔥
                    print(f"[DEBUG connect_wallet] Attempting to call refresh_holdings_data. Wallet connected: {self.wallet_connected}, Address: {self.wallet_address}")
                    self.refresh_holdings_data()

                    # 设置余额显示为等待选择代币状态
                    self.token_balance_label.setText("余额: 请选择代币")
                    self.update_balance_display("余额: 请选择代币", neutral=True)
                    
                    # 🔥 自动连接成功后，启用相关功能
                    self.update_sell_button_state()
                    
                else:
                    # 没有找到 Solana 钱包地址
                    logger.info("⚠️ 自动连接钱包失败：未找到 Solana 钱包地址")
                    self.status_label.setText("⚠️ 未找到Solana钱包，请手动连接")
            else:
                # API 调用失败
                error_msg = wallet_info_result.get('error', '未知错误')
                logger.info(f"⚠️ 自动连接钱包失败: {error_msg}")
                self.status_label.setText("⚠️ 钱包连接失败，请手动连接")
        
        except Exception as e:
            logger.info(f"⚠️ 自动连接钱包异常: {e}")
            self.status_label.setText("⚠️ 钱包连接异常，请手动连接")

    # 🔥🔥 新增：后台 ChartWidget 发出信号的槽函数
    @pyqtSlot(str, float, int, str, int) # 🔥 REVERTED: chart_index (5th arg) back to int to match signal source
    def on_background_chart_signal(self, signal_type: str, price: float, timestamp: int, strategy_name: str, chart_index: int):
        """处理来自后台ChartWidget的交易信号。"""
        # 🔥 RENAMED: chart_datetime_index_val back to chart_index
        try:
            # 从 sender() 获取 token_address (更可靠的方式)
            sender_widget = self.sender()
            token_address = "Unknown"
            token_symbol = "Unknown"

            # sender_widget 现在是 HeadlessChartWidget 实例
            if hasattr(sender_widget, 'token_data') and sender_widget.token_data:
                token_address = sender_widget.token_data.get('tokenAddress', "UnknownAddress")
                token_symbol = sender_widget.token_data.get('symbol', "UnknownSymbol")
            else:
                print(f"🔥 BG_SIGNAL: sender_widget 没有有效的 token_data: {type(sender_widget)}")
                logger.error(f"sender_widget 没有有效的 token_data: {type(sender_widget)}")
                return

            timestamp_minute = (timestamp // 60) * 60
            unique_key = f"{token_address}_{strategy_name}_{timestamp_minute}"
            
            is_new_or_changed_signal = False
            if unique_key not in self.unique_signals:
                is_new_or_changed_signal = True

            if is_new_or_changed_signal:
                self.unique_signals[unique_key] = {
                    'signal_type': signal_type,
                    'price': price,
                    'timestamp': timestamp, # Correct chart event time
                    'token_address': token_address,
                    'token_symbol': token_symbol,
                    'strategy_name': strategy_name,
                    'chart_index': chart_index, # MODIFIED: Was chart_datetime_index_val
                    'processed': False,
                    'created_time': time.time(),
                    'source': 'background_chart'
                }
                # print(f"🔥 BG_SIGNAL: Tok: {token_symbol}, Type: {signal_type}, Price: {price:.6f}, ChartEventTime: {timestamp}, Strategy: {strategy_name}, ChartIndexVal: {chart_index}")
                self.update_trend_table_strategy_signal(token_address, token_symbol, signal_type)
                self.update_holdings_table_strategy_signal(token_address, token_symbol, signal_type)

                signal_data = self.unique_signals[unique_key].copy()
                # 补充两个字段 
                signal_data['unique_key'] = unique_key
                signal_data['test'] = False


                print(f"🔥 BG_SIGNAL: Sending webhook for {token_symbol} {signal_type} {price} SIGNAL Count: {len(self.unique_signals)}")
                # 🔥 新增：发送Webhook通知
                self.webhook_manager.send_signal_webhook(signal_data)
                
                # 🔥🔥 立即更新未处理信号表格
                QTimer.singleShot(100, self.update_unprocessed_signals_table)

        except Exception as e:
            logger.error(f"on_background_chart_signal error: {e}", exc_info=True)
    
    def update_signal_statistics(self):
        """更新信号统计信息"""
        try:
            total_signals = 0
            buy_signals = 0
            sell_signals = 0
            active_tokens = len(self.all_chart_signals)
            
            for token_address, signals in self.all_chart_signals.items():
                total_signals += len(signals)
                for signal in signals:
                    if signal['signal_type'].lower() == 'buy':
                        buy_signals += 1
                    elif signal['signal_type'].lower() == 'sell':
                        sell_signals += 1
            
            self.signal_statistics = {
                'total_signals': total_signals,
                'buy_signals': buy_signals,
                'sell_signals': sell_signals,
                'active_tokens': active_tokens,
                'last_update': time.time()
            }
            
            # 发射统计更新信号
            self.signal_statistics_updated.emit(self.signal_statistics.copy())
            
        except Exception as e:
            logger.error(f"更新信号统计失败: {e}")
    
    def get_all_signals(self) -> Dict:
        """获取所有收集到的信号"""
        return self.all_chart_signals.copy()
    
    def get_signals_by_token(self, token_address: str) -> List[Dict]:
        """获取指定代币的所有信号"""
        return self.all_chart_signals.get(token_address, [])
    
    def get_recent_signals(self, count: int = 50) -> List[Dict]:
        """获取最近的N个信号（按时间排序）"""
        try:
            all_signals = []
            for token_address, signals in self.all_chart_signals.items():
                all_signals.extend(signals)
            
            # 按接收时间排序
            all_signals.sort(key=lambda x: x['received_time'], reverse=True)
            
            return all_signals[:count]
            
        except Exception as e:
            logger.error(f"获取最近信号失败: {e}")
            return []
    
    def get_signal_statistics(self) -> Dict:
        """获取信号统计信息"""
        return self.signal_statistics.copy()
    
    def get_active_tokens_with_signals(self) -> List[str]:
        """获取有信号的活跃代币地址列表"""
        return list(self.all_chart_signals.keys())
    
    def clear_signals_for_token(self, token_address: str):
        """清除指定代币的信号历史"""
        try:
            if token_address in self.all_chart_signals:
                del self.all_chart_signals[token_address]
                self.update_signal_statistics()
                logger.info(f"🧹 已清除 {token_address} 的信号历史")
        except Exception as e:
            logger.error(f"清除代币信号失败: {e}")
    
    def export_signals_to_dict(self) -> Dict:
        """导出所有信号数据为字典格式"""
        try:
            return {
                'signals': self.all_chart_signals,
                'statistics': self.signal_statistics,
                'latest_signals': self.latest_token_signals,
                'export_time': time.time()
            }
        except Exception as e:
            logger.error(f"导出信号数据失败: {e}")
            return {}
    

    def get_relative_time(self, timestamp: float) -> str:
        """计算相对时间显示，如'5s ago'、'2m ago'等"""
        try:
            current_time = time.time()
            diff_seconds = int(current_time - timestamp)
            
            if diff_seconds < 60:
                return f"{diff_seconds}s ago"
            elif diff_seconds < 3600:  # 小于1小时
                minutes = diff_seconds // 60
                return f"{minutes}m ago"
            elif diff_seconds < 86400:  # 小于1天
                hours = diff_seconds // 3600
                return f"{hours}h ago"
            else:  # 超过1天
                days = diff_seconds // 86400
                return f"{days}d ago"
        except Exception as e:
            logger.error(f"计算相对时间失败: {e}")
            return "--"
    
    def update_trend_table_strategy_signal(self, token_address: str, token_symbol: str, signal_type: str):
        """更新趋势表中的策略信号显示，并可选地立即刷新表格视图"""
        logger.info(f"➡️ 更新趋势表信号: {token_symbol} -> {signal_type}")
        
        try:
            # 确保信号缓存存在
            if not hasattr(self, 'strategy_signals_cache'):
                self.strategy_signals_cache = {}

            # 更新信号缓存
            current_time_unix = time.time()
            self.strategy_signals_cache[token_address] = {
                'signal': signal_type,
                'timestamp': QDateTime.fromSecsSinceEpoch(int(current_time_unix)).toString("HH:mm:ss"),
                'unix_timestamp': current_time_unix,
                'token_symbol': token_symbol
            }
            
            logger.info(f"🎯 策略信号已缓存: {token_symbol} -> {signal_type}")
            
            # 触发信号统计更新
            self.signal_statistics_updated.emit(self.get_signal_statistics())

            # 🔥 主动更新一次富文本显示，确保信号及时出现
            # 使用 QTimer.singleShot 延迟执行，避免在信号处理中直接更新UI可能引发的问题
            # 并且确保 self.trend_tokens 是最新的
            QTimer.singleShot(100, lambda: self.update_trend_table(self.trend_tokens, trigger_background_refresh=False))
            
        except Exception as e:
            logger.error(f"更新趋势表策略信号失败 ({token_symbol}): {e}", exc_info=True)

    def update_holdings_table_strategy_signal(self, token_address: str, token_symbol: str, signal_type: str):
        """更新持仓表中的策略信号列 (如果存在)"""
        try:
            # 检查持仓面板是否存在
            if not hasattr(self, 'holdings_panel') or not self.holdings_panel:
                logger.debug(f"持仓面板不存在，跳过更新: {token_symbol}")
                return
            
            # 调用持仓面板的更新方法
            self.holdings_panel.update_holdings_table_strategy_signal(token_address, token_symbol, signal_type)
            
        except Exception as e:
            logger.error(f"更新持仓表格策略信号失败 ({token_symbol if 'token_symbol' in locals() else token_address}): {e}", exc_info=True)

    def update_all_relative_times(self):
        """更新所有代币的相对时间显示 - 富文本模式"""
        try:
            # 🔥🔥 富文本模式：不需要实时更新每个单元格
            # 相对时间会在下次数据刷新时更新
            logger.debug("富文本模式：相对时间将在下次刷新时更新")
            
        except Exception as e:
            logger.error(f"更新相对时间失败: {e}")

    @pyqtSlot(str, str, str, int)  # 🔥 MODIFIED: 4th arg from object to int
    def on_strategy_analysis_completed(self, token_address: str, token_symbol: str, final_signal: str, signal_event_timestamp: int):
        """当策略分析完成时的槽函数 - 添加信号到unique_signals并更新表格显示"""
        logger.info(f"🔥 策略分析完成: Token: {token_symbol} ({token_address}), Signal: {final_signal}")
        try:
            # 🔥🔥 添加信号到unique_signals，使用策略分析来源
            if final_signal and final_signal.lower() not in ['无数据', 'no_data', 'analyzing', '分析中']:
                timestamp_minute = (signal_event_timestamp // 60) * 60
                unique_key = f"{token_address}_strategy_{timestamp_minute}"
                
                # 只有当信号不存在或发生变化时才添加
                if unique_key not in self.unique_signals:
                    # 获取当前策略名称
                    current_strategy = self.strategy_combo.currentText() if hasattr(self, 'strategy_combo') else "Unknown"
                    
                    self.unique_signals[unique_key] = {
                        'signal_type': final_signal,
                        'price': 0.0,  # 策略分析信号没有具体价格
                        'timestamp': signal_event_timestamp,
                        'token_address': token_address,
                        'token_symbol': token_symbol,
                        'strategy_name': current_strategy,
                        'chart_index': -1,  # 策略分析信号不来自特定图表
                        'processed': False,
                        'created_time': time.time(),
                        'source': 'strategy_analysis'
                    }
                    
                    logger.info(f"🎯 策略分析信号已添加到unique_signals: {token_symbol} -> {final_signal}")
                    
                    # 发送Webhook通知
                    signal_data = self.unique_signals[unique_key].copy()
                    signal_data['unique_key'] = unique_key
                    signal_data['test'] = False
                    self.webhook_manager.send_signal_webhook(signal_data)
                    
                    # 🔥🔥 立即更新未处理信号表格
                    QTimer.singleShot(100, self.update_unprocessed_signals_table)
            
            # 更新表格显示
            self.update_trend_table_strategy_signal(token_address, token_symbol, final_signal)
            self.update_holdings_table_strategy_signal(token_address, token_symbol, final_signal)
            
            # 添加到本地日志
            current_time = time.time()
            if token_address in self.last_strategy_analysis:
                last_analysis = self.last_strategy_analysis[token_address]
                if (last_analysis['signal'] == final_signal and 
                    current_time - last_analysis['timestamp'] < self.strategy_analysis_cooldown):
                    return  # 跳过重复日志
            
            self.last_strategy_analysis[token_address] = {
                'signal': final_signal,
                'timestamp': current_time
            }
            
            self.add_message_to_log(f"🎯 策略分析完成: {token_symbol} -> {final_signal}", "info")
            
        except Exception as e:
            logger.error(f"处理策略分析完成事件失败: {e}", exc_info=True)
        
    def add_message_to_log(self, message: str, log_type: str = "info"):
        """添加消息到本地日志"""
        try:
            if not self.enable_local_signal_logs:
                return
                
            self.message_log_count += 1
            
            # 获取当前时间
            timestamp = datetime.now().strftime("%H:%M:%S")
            
            # 根据日志类型设置颜色
            if log_type == "success":
                color = "#27ae60"
                icon = "✅"
            elif log_type == "error":
                color = "#e74c3c"
                icon = "❌"
            elif log_type == "warning":
                color = "#f39c12"
                icon = "⚠️"
            else:
                color = "#ecf0f1"
                icon = "📝"
            
            # 添加到日志
            formatted_message = f"[{timestamp}] {icon} {message}"
            self.message_log_text.append(f"<span style='color: {color};'>{formatted_message}</span>")
            
            # 更新统计
            self.update_log_stats()
            
            # 自动滚动
            if self.auto_scroll_log_checkbox.isChecked():
                self.message_log_text.verticalScrollBar().setValue(
                    self.message_log_text.verticalScrollBar().maximum()
                )
                
        except Exception as e:
            logger.error(f"添加消息到日志失败: {e}")
    
    def clear_message_log(self):
        """清空消息日志"""
        try:
            self.message_log_text.clear()
            self.message_log_count = 0
            self.update_log_stats()
            self.add_message_to_log("日志已清空", "info")
        except Exception as e:
            logger.error(f"清空消息日志失败: {e}")
    
    def toggle_local_signal_logs(self, enabled: bool):
        """切换本地信号日志开关"""
        try:
            self.enable_local_signal_logs = enabled
            if enabled:
                self.add_message_to_log("信号日志已启用", "success")
            else:
                self.add_message_to_log("信号日志已禁用", "warning")
        except Exception as e:
            logger.error(f"切换信号日志开关失败: {e}")
    
    def update_log_stats(self):
        """更新日志统计信息"""
        try:
            current_time = datetime.now().strftime("%H:%M:%S")
            self.log_stats_label.setText(f"消息: {self.message_log_count} | 最后更新: {current_time}")
        except Exception as e:
            logger.error(f"更新日志统计失败: {e}")
    
    def get_unprocessed_signals(self, limit: int = 30) -> List[Dict]:
        """获取所有未处理的信号，按时间排序，最新的在前面"""
        unprocessed = []
        for unique_key, signal_data in self.unique_signals.items():
            if not signal_data.get('processed', False):
                signal_copy = signal_data.copy()
                signal_copy['unique_key'] = unique_key
                unprocessed.append(signal_copy)
        
        # 🔥 按时间戳排序，最新的排在前面
        unprocessed.sort(key=lambda x: x.get('timestamp', 0), reverse=True)
        
        # 🔥 限制显示最新的30个信号
        return unprocessed[:limit]
    
    def get_signals_by_token(self, token_address: str) -> List[Dict]:
        """获取指定代币的所有唯一信号"""
        token_signals = []
        for unique_key, signal_data in self.unique_signals.items():
            if signal_data['token_address'] == token_address:
                signal_copy = signal_data.copy()
                signal_copy['unique_key'] = unique_key
                token_signals.append(signal_copy)
        return token_signals
    
    def get_unique_signals_stats(self) -> Dict:
        """获取唯一信号统计信息"""
        total_signals = len(self.unique_signals)
        processed_signals = sum(1 for s in self.unique_signals.values() if s.get('processed', False))
        unprocessed_signals = total_signals - processed_signals
        
        # 按信号类型统计
        buy_signals = sum(1 for s in self.unique_signals.values() if s['signal_type'].lower() == 'buy')
        sell_signals = sum(1 for s in self.unique_signals.values() if s['signal_type'].lower() == 'sell')
        
        # 按代币统计
        unique_tokens = len(set(s['token_address'] for s in self.unique_signals.values()))
        
        return {
            'total_signals': total_signals,
            'processed_signals': processed_signals,
            'unprocessed_signals': unprocessed_signals,
            'buy_signals': buy_signals,
            'sell_signals': sell_signals,
            'unique_tokens': unique_tokens
        }
    
    @pyqtSlot()
    def show_latest_unprocessed_signals(self):
        """显示最新的未处理信号（按时间排序，最新的在前面）"""
        try:
            print("\n" + "="*80)
            print("📊 最新未处理信号列表 (前30个)")
            print("="*80)


            # 打印后台图表数
            print(f"后台图表数: {len(self.background_chart_pool)}")
            print(f"活跃图表数: {len(self.active_background_charts)}")
            
            unprocessed_signals = self.get_unprocessed_signals(limit=30)
            if not unprocessed_signals:
                print("📭 当前没有未处理的信号")
                print("="*80)
                return
            
            print(f"总计: {len(unprocessed_signals)} 个未处理信号")
            print("-" * 80)
            
            for i, signal in enumerate(unprocessed_signals, 1):
                # 获取信号基本信息
                token_symbol = signal.get('token_symbol', 'Unknown')
                token_address = signal.get('token_address', '')
                signal_type = signal.get('signal_type', 'Unknown')
                price = signal.get('price', 0)
                timestamp = signal.get('timestamp', 0)
                strategy_name = signal.get('strategy_name', 'Unknown')
                chart_index = signal.get('chart_index', 0)
                unique_key = signal.get('unique_key', '')
                
                # 格式化时间显示
                from datetime import datetime
                time_str = datetime.fromtimestamp(timestamp).strftime('%m-%d %H:%M:%S') if timestamp else 'Unknown'
                relative_time = self.get_relative_time(timestamp) if hasattr(self, 'get_relative_time') else ''
                
                # 信号类型颜色标识
                signal_icon = "📈" if signal_type.lower() == 'buy' else ("📉" if signal_type.lower() == 'sell' else "🔄")
                
                # 显示信号详情
                print(f"{i:2d}. {signal_icon} {token_symbol} -> {signal_type.upper()}")
                print(f"    💰 价格: ${price:.8f}")
                print(f"    ⏰ 时间: {time_str} ({relative_time})")
                print(f"    📊 策略: {strategy_name} | 图表: {chart_index}")
                print(f"    🏷️  Key: {unique_key}")
                print(f"    📍 地址: {token_address[:10]}...{token_address[-6:]}" if token_address else "    📍 地址: 未知")
                print()
            
            print("="*80)
            print(f"💡 这些信号将按时间顺序用于自动交易决策")
            print("="*80)
            
        except Exception as e:
            logger.error(f"显示最新未处理信号失败: {e}")
            print(f"❌ 显示失败: {e}")

    # 🔥🔥 新增：创建未处理信号面板
    def create_unprocessed_signals_panel(self) -> QWidget:
        """创建最近30分钟未处理信号面板"""
        group = QGroupBox("🔔 最近30分钟未处理信号 (点击查看图表)")
        group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 11px; /* Slightly smaller font for this panel */
                color: #ecf0f1;
                border: 1px solid #1a252f;
                border-radius: 4px;
                margin-top: 8px; /* Smaller margin */
                padding-top: 5px;
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #3a4a5c, stop: 1 #2c3e50); /* Adjusted gradient */
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 6px 0 6px;
                color: #f39c12; /* Orange title */
            }
        """)
        
        layout = QVBoxLayout()
        layout.setContentsMargins(5, 8, 5, 5) # Adjusted margins
        
        # 🔥 新增：顶部按钮栏
        button_layout = QHBoxLayout()
        button_layout.setContentsMargins(0, 0, 0, 5)
        
        # 设置按钮
        self.webhook_settings_button = QPushButton("⚙️ 设置")
        self.webhook_settings_button.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: #2c3e50;
                border: none;
                border-radius: 3px;
                padding: 4px 8px;
                font-size: 9px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
            QPushButton:pressed {
                background-color: #d35400;
            }
        """)
        self.webhook_settings_button.setToolTip("配置Webhook和其他设置")
        self.webhook_settings_button.clicked.connect(self.webhook_manager.show_webhook_settings_dialog)
        
        # Webhook状态指示器
        self.webhook_status_label = QLabel("📡 Webhook: 未配置")
        self.webhook_status_label.setStyleSheet("""
            QLabel {
                color: #bdc3c7;
                font-size: 8px;
                padding: 2px 4px;
            }
        """)
        
        button_layout.addWidget(self.webhook_settings_button)
        button_layout.addStretch()
        button_layout.addWidget(self.webhook_status_label)
        
        layout.addLayout(button_layout)
        
        self.unprocessed_signals_table = QTableWidget()
        self.unprocessed_signals_table.setColumnCount(7)
        self.unprocessed_signals_table.setHorizontalHeaderLabels([
            "时间", "代币", "信号", "价格", "策略", "地址", "图表"
        ])
        
        self.unprocessed_signals_table.setStyleSheet("""
            QTableWidget {
                background-color: #2c3e50;
                border: none;
                gridline-color: #34495e;
                font-size: 9px;
                color: #ecf0f1;
                selection-background-color: #e67e22; /* Orange selection */
            }
            QTableWidget::item {
                padding: 2px 4px;
                border-bottom: 1px solid #34495e;
            }
            QTableWidget::item:hover {
                background-color: #34495e;
                cursor: pointer;
            }
            QHeaderView::section {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #4a5568, stop: 1 #3a4a5c);
                border: none;
                border-bottom: 1px solid #1a252f;
                border-right: 1px solid #2c3e50;
                padding: 3px 5px; /* Smaller padding */
                font-size: 9px;
                font-weight: bold;
                color: #ecf0f1;
            }
        """)
        
        header = self.unprocessed_signals_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Fixed)
        self.unprocessed_signals_table.setColumnWidth(0, 70)  # 时间
        self.unprocessed_signals_table.setColumnWidth(1, 80)  # 代币
        self.unprocessed_signals_table.setColumnWidth(2, 50)  # 信号
        self.unprocessed_signals_table.setColumnWidth(3, 90)  # 价格
        self.unprocessed_signals_table.setColumnWidth(4, 100) # 策略
        self.unprocessed_signals_table.setColumnWidth(5, 100) # 地址
        self.unprocessed_signals_table.setColumnWidth(6, 50) # 图表

        
        self.unprocessed_signals_table.setAlternatingRowColors(False)
        self.unprocessed_signals_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.unprocessed_signals_table.setSelectionMode(QTableWidget.SingleSelection)
        self.unprocessed_signals_table.setSortingEnabled(True)
        self.unprocessed_signals_table.setShowGrid(False)
        self.unprocessed_signals_table.verticalHeader().setVisible(False)
        
        # 🔥 新增：添加点击事件监听器
        self.unprocessed_signals_table.cellClicked.connect(self.on_unprocessed_signal_clicked)
        
        # 🔥 新增：添加工具提示
        self.unprocessed_signals_table.setToolTip("💡 点击任意行查看该代币的K线图表")
        
        layout.addWidget(self.unprocessed_signals_table)
        group.setLayout(layout)
        return group

    # 🔥🔥 新增：更新未处理信号表格的方法
    def update_unprocessed_signals_table(self):
        """更新最近30分钟未处理信号表格"""
        try:
            # 🔥 移除清理逻辑：unique_signals 只应该增多不应该减少
            # 注释掉清理代码，保持所有历史信号
            # current_time = time.time()
            # two_hours_ago = current_time - (2 * 60 * 60)
            # 
            # # 找出需要删除的过期信号键
            # expired_keys = []
            # for unique_key, signal_data in self.unique_signals.items():
            #     signal_timestamp = signal_data.get('timestamp', 0)
            #     if signal_timestamp < two_hours_ago:
            #         expired_keys.append(unique_key)
            # 
            # # 删除过期信号
            # for key in expired_keys:
            #     del self.unique_signals[key]
            # 
            # if expired_keys:
            #     logger.info(f"🧹 清理了 {len(expired_keys)} 个过期信号（超过2小时）")
            
            # 🔥 添加调试信息 - 显示信号来源统计
            total_unique_signals = len(self.unique_signals)
            background_signals = sum(1 for s in self.unique_signals.values() if s.get('source') == 'background_chart')
            analysis_signals = sum(1 for s in self.unique_signals.values() if s.get('source') == 'strategy_analysis')
            other_signals = total_unique_signals - background_signals - analysis_signals
            
            logger.info(f"🔥 更新未处理信号表格 - 总信号数: {total_unique_signals} (后台图表: {background_signals}, 策略分析: {analysis_signals}, 其他: {other_signals})")
            
            self.unprocessed_signals_table.setUpdatesEnabled(False)
            self.unprocessed_signals_table.setSortingEnabled(False) # Disable sorting during update

            all_unprocessed_signals = self.get_unprocessed_signals() # Gets all, already sorted by timestamp desc
            
            current_unix_time = time.time()
            onehour_ago_unix = current_unix_time - (60 * 60)
            
            recent_signals = [
                s for s in all_unprocessed_signals 
                if s.get('timestamp', 0) >= onehour_ago_unix
            ]
            
            # 🔥 添加调试信息 - 显示过滤后的信号数量
            logger.info(f"🔥 过滤后的最近1小时信号数量: {len(recent_signals)}")
            
            # Already sorted by timestamp descending by get_unprocessed_signals
            # If not, uncomment:
            # recent_signals.sort(key=lambda x: x.get('timestamp', 0), reverse=True)

            self.unprocessed_signals_table.setRowCount(len(recent_signals))
            
            for row, signal_data in enumerate(recent_signals):
                timestamp = signal_data.get('timestamp', 0)
                
                # 时间列: HH:MM:SS
                dt_object = datetime.fromtimestamp(timestamp)
                time_str = dt_object.strftime('%H:%M:%S')
                time_item = QTableWidgetItem(time_str)
                time_item.setTextAlignment(Qt.AlignCenter)
                self.unprocessed_signals_table.setItem(row, 0, time_item)
                
                # 代币列
                token_symbol = signal_data.get('token_symbol', 'N/A')
                token_item = QTableWidgetItem(token_symbol)
                self.unprocessed_signals_table.setItem(row, 1, token_item)
                
                # 信号列 - 🔥 修复：添加来源标识
                signal_type = signal_data.get('signal_type', 'N/A').lower()
                signal_source = signal_data.get('source', 'unknown')
                source_icon = "📊" if signal_source == 'background_chart' else ("🎯" if signal_source == 'strategy_analysis' else "❓")
                signal_display = f"{source_icon}{signal_type.capitalize()}"
                signal_item = QTableWidgetItem(signal_display)
                signal_item.setTextAlignment(Qt.AlignCenter)
                font = signal_item.font()
                font.setBold(True)
                signal_item.setFont(font)
                if signal_type == 'buy' or signal_type == '买入':
                    signal_item.setForeground(QColor('#27ae60')) # Green for buy
                elif signal_type == 'sell' or signal_type == '卖出':
                    signal_item.setForeground(QColor('#e74c3c')) # Red for sell
                else:
                    signal_item.setForeground(QColor('#bdc3c7')) # Gray for others
                
                # 🔥 添加工具提示显示详细信息
                signal_item.setToolTip(f"来源: {signal_source}\n唯一键: {signal_data.get('unique_key', 'N/A')}")
                self.unprocessed_signals_table.setItem(row, 2, signal_item)
                
                # 价格列
                price = signal_data.get('price', 0.0)
                price_str = f"${price:.6f}"
                price_item = NumericTableWidgetItem(price_str, float(price))
                self.unprocessed_signals_table.setItem(row, 3, price_item)
                
                # 策略列
                strategy_name = signal_data.get('strategy_name', 'N/A')
                strategy_item = QTableWidgetItem(strategy_name)
                self.unprocessed_signals_table.setItem(row, 4, strategy_item)

                # 地址列
                token_address = signal_data.get('token_address', 'N/A')
                address_item = QTableWidgetItem(token_address)
                self.unprocessed_signals_table.setItem(row, 5, address_item)

                # 图表列
                chart_index = signal_data.get('chart_index', 'N/A')
                # print(f"chart_index: {chart_index}")
                chart_item = NumericTableWidgetItem(str(chart_index), chart_index)
                self.unprocessed_signals_table.setItem(row, 6, chart_item)

            if not recent_signals:
                logger.info("🔥 没有最近的未处理信号显示")


        except Exception as e:
            logger.error(f"更新未处理信号表格失败: {e}", exc_info=True)
        finally:
            self.unprocessed_signals_table.setSortingEnabled(True) # Re-enable sorting
            self.unprocessed_signals_table.setUpdatesEnabled(True)

    # In LiveTradingWidget class
    def get_wallet_holdings_data(self) -> list:

        if not self.wallet_connected:
            self.add_message_to_log("获取持仓失败：钱包未连接。", "warning")
            return []
        
        if not self.wallet_address:
            self.add_message_to_log("无法获取持仓: 钱包已连接但地址未设置。", "warning")
            return []

        if not self.wallet_service:
            self.add_message_to_log("错误: WalletService 未成功初始化，无法获取持仓数据。", "error")
            logger.error("get_wallet_holdings_data: WalletService is not initialized.")
            return []

        self.add_message_to_log(f"正在为钱包 {self.wallet_address} 获取持仓数据...", "info")
        
        try:
            chain_id = "501" 
            holdings_list = self.wallet_service.get_holdings(self.wallet_address, chain_id=chain_id)
            
            if holdings_list:
                self.add_message_to_log(f"成功从 WalletService 获取 {len(holdings_list)} 条持仓数据。", "debug")
            else:
                self.add_message_to_log(f"钱包 {self.wallet_address} 未返回持仓数据或持仓为空。", "info")
            
            return holdings_list
        except Exception as e:
            self.add_message_to_log(f"通过 WalletService 获取持仓时发生错误: {e}", "error")
            logger.exception(f"Error calling WalletService.get_holdings in LiveTradingWidget for wallet {self.wallet_address}")
            return []
    
    # 在 LiveTradingWidget class 中
    @pyqtSlot()
    def refresh_holdings_data(self):
        """刷新持仓数据并更新UI"""
        
        print(f"[DEBUG refresh_holdings_data] 调用开始 - 钱包连接: {self.wallet_connected}, 地址: {self.wallet_address if hasattr(self, 'wallet_address') else 'N/A'}")
        
        if not self.wallet_connected:
            self.add_message_to_log("钱包未连接，无法刷新持仓。请先连接钱包。", "info") 
            print(f"[DEBUG refresh_holdings_data] 退出: 钱包未连接")
            return

        if not hasattr(self, 'wallet_address') or not self.wallet_address:
            self.add_message_to_log("钱包地址为空，无法刷新持仓。", "warning") 
            print(f"[DEBUG refresh_holdings_data] 退出: 钱包地址为空")
            return

        print(f"[DEBUG refresh_holdings_data] 开始刷新持仓数据...")

        try:
            self.add_message_to_log("开始刷新持仓数据...", "info")
            
            holdings_list = self.get_wallet_holdings_data() 
            print(f"[DEBUG refresh_holdings_data] 获取到持仓数据: {len(holdings_list) if holdings_list else 0} 个")

            # 🔥 修复：只有在获取到有效数据时才更新界面和保存变量
            if holdings_list:
                # 🔥 保存原始持仓数据
                self.holdings_list_raw = holdings_list.copy()
                print(f"[DEBUG refresh_holdings_data] 保存原始持仓数据: {len(self.holdings_list_raw)} 个")
                
                # 🔥 保存完整持仓数据（限制50个）
                self.holdings_list_full = holdings_list[:50]
                print(f"[DEBUG refresh_holdings_data] 保存完整持仓数据: {len(self.holdings_list_full)} 个")
                
                # 🔥 保存标准化持仓数据（用于后台图表分析）
                self.holdings_list_normalized = normalize_holdings_for_background_charts(self.holdings_list_full)
                print(f"[DEBUG refresh_holdings_data] 保存标准化持仓数据: {len(self.holdings_list_normalized)} 个")
                
                # 🔥 保存过滤后的持仓数据（去除小额持仓，value > 1 USD）
                self.holdings_list_filtered = [
                    holding for holding in self.holdings_list_full 
                    if holding.get('valueUsd', 0) > 1.0
                ]
                print(f"[DEBUG refresh_holdings_data] 保存过滤持仓数据（>$1）: {len(self.holdings_list_filtered)} 个")
                
                # 更新界面显示
                self.holdings_panel.update_holdings(self.holdings_list_full)
                self.add_message_to_log(f"持仓数据刷新完成，共 {len(holdings_list)} 个代币（显示前50个）。", "success")
                print(f"[DEBUG refresh_holdings_data] 成功更新持仓数据和保存所有变量")
            else:
                # 🔥 修复：没有数据时不清空界面和变量，保留当前显示
                self.add_message_to_log("获取持仓数据为空，保留当前界面显示。请检查钱包连接状态或稍后重试。", "warning")
                print(f"[DEBUG refresh_holdings_data] 持仓数据为空，保留现有界面显示和变量")
                # 不调用 self.holdings_panel.update_holdings([])，避免清空界面
                # 不清空持仓变量，保留上次的数据
        except Exception as e:
            print(f"[DEBUG refresh_holdings_data] EXCEPTION caught: {e}")
            self.add_message_to_log(f"刷新持仓数据时发生错误: {e}", "error")
            print(f"[DEBUG refresh_holdings_data] 由于异常，保留现有界面显示和变量")
            # 🔥 修复：异常时也不清空界面和变量，保留当前显示
            # self.holdings_panel.update_holdings([])  # 🔥 修复：注释掉，避免API错误时清空界面

    # In LiveTradingWidget class
    @pyqtSlot(dict)
    def on_holding_panel_token_selected(self, token_info: dict):
        """
        处理持仓面板中代币被选中的事件。
        token_info: 包含选中代币信息的字典，应与 load_token_chart 兼容。
        """
        if not token_info or not token_info.get('address'):
            self.add_message_to_log("无法加载图表：选中持仓代币信息不完整或无地址。", "error")
            return

        symbol = token_info.get('symbol', token_info.get('address'))
        self.add_message_to_log(f"持仓面板选中: {symbol} ({token_info.get('name', '')})。准备加载图表...", "info")
        
        # 🔥🔥 确保token_info有正确的数据结构，添加必要的字段以兼容load_token_chart
        if 'tokenAddress' not in token_info and 'address' in token_info:
            token_info['tokenAddress'] = token_info['address']
        
        # 设置数据来源为持仓
        token_info['source'] = 'holdings'
        
        # 🔥 修复：清空所有现有的策略信号记录，为新选择的代币做准备（与趋势列表点击行为一致）
        self.trade_records.clear()
        self.trade_record_id_counter = 0
        logger.info(f"已清空所有信号记录，为新代币 {symbol} 做准备")
        
        # 调用load_token_chart加载图表
        self.load_token_chart(token_info)
        
        # 🔥 修复：更新信号记录显示（只显示当前代币的记录）
        self.update_trade_records_display()
        self.update_trade_statistics()
        
        # 启用策略执行按钮（如果已选择策略）
        # if self.selected_strategy:
        #     self.execute_strategy_button.setEnabled(True)
        #     logger.info(f"策略执行按钮已启用 - 代币: {symbol}, 策略: {self.selected_strategy.name}")
        # else:
        #     self.execute_strategy_button.setEnabled(False)
        #     logger.info(f"策略执行按钮禁用 - 代币: {symbol}, 未选择策略")
        
        # 🔥 启用买入按钮（选中代币后即可买入）
        if hasattr(self, 'buy_button'):
            self.buy_button.setEnabled(True)

        # 重置代币余额并更新卖出按钮状态
        self.current_token_balance = 0
        self.update_sell_button_state()
        
        # 🔥 自动刷新代币余额
        if self.wallet_connected and hasattr(self, 'wallet_address'):
            self.refresh_token_balance()
        else:
            if hasattr(self, 'token_balance_label'):
                self.token_balance_label.setText("余额: 未连接钱包")
                self.update_balance_display("余额: 未连接钱包", error=True)

    def on_trend_text_clicked(self, event):
        """处理富文本框点击事件 - 修复版本，通过搜索币种符号精确匹配"""
        if event.button() == Qt.LeftButton:
            try:
                # 获取点击位置对应的字符位置
                cursor = self.trend_text_display.cursorForPosition(event.pos())
                cursor.select(cursor.LineUnderCursor)
                line_text = cursor.selectedText().strip()
                
                print(f"🔍 点击的行内容: '{line_text}'")
                
                # 通过解析行内容找到对应的代币
                if hasattr(self, 'current_trend_tokens') and self.current_trend_tokens and line_text:
                    selected_token = None
                    
                    # 方法1: 直接从行文本中提取币种符号进行匹配
                    for i, token in enumerate(self.current_trend_tokens):
                        symbol = token.get('symbol', '').upper()
                        name = token.get('name', '')
                        
                        # 检查行文本中是否包含币种符号或名称
                        if symbol and (symbol in line_text.upper() or f"#{i+1}{symbol}" in line_text.replace(' ', '')):
                            selected_token = token
                            print(f"🎯 通过符号匹配找到代币: {symbol} (索引: {i})")
                            break
                        elif name and len(name) > 3 and name.lower() in line_text.lower():
                            selected_token = token
                            print(f"🎯 通过名称匹配找到代币: {name} (索引: {i})")
                            break
                    
                    # 方法2: 如果上面没找到，使用改进的位置计算
                    if not selected_token:
                        document = self.trend_text_display.document()
                        block_number = cursor.blockNumber()
                        print(f"🔍 使用位置计算: block_number={block_number}")
                        
                        # 统计实际的HTML块结构
                        total_blocks = document.blockCount()
                        print(f"🔍 总块数: {total_blocks}")
                        
                        # 更精确的计算：找到包含代币数据的块范围
                        content_start_block = -1
                        for block_idx in range(total_blocks):
                            block = document.findBlockByNumber(block_idx)
                            block_text = block.text()
                            # 查找第一个包含 "#1" 的块，这是第一个代币
                            if "#1" in block_text and any(token.get('symbol', '') in block_text for token in self.current_trend_tokens[:1]):
                                content_start_block = block_idx
                                print(f"🎯 找到内容开始块: {content_start_block}")
                                break
                        
                        if content_start_block >= 0 and block_number >= content_start_block:
                            # 每个代币在HTML中占用的块数（通过实际测试确定）
                            # 由于每个 token-row div 可能占用1个块
                            token_index = block_number - content_start_block
                            
                            print(f"🔍 计算的代币索引: {token_index}")
                            
                            if 0 <= token_index < len(self.current_trend_tokens):
                                selected_token = self.current_trend_tokens[token_index]
                                print(f"🎯 通过位置计算找到代币: {selected_token.get('symbol', 'Unknown')} (索引: {token_index})")
                    
                    # 如果找到了代币，执行加载
                    if selected_token:
                        print(f"✅ 最终选择的代币: {selected_token.get('symbol', 'Unknown')}")
                        
                        # 🔥 修复：与持仓/趋势表点击行为一致，清空旧信号记录
                        self.trade_records.clear()
                        self.trade_record_id_counter = 0
                        logger.info(f"已清空所有信号记录，为新代币 {selected_token.get('symbol', 'Unknown')} (从趋势文本点击) 做准备")

                        # 加载图表分析
                        self.load_token_chart(selected_token)
                        
                        # 🔥 强制启用买入按钮（修复问题）
                        if hasattr(self, 'buy_button'):
                            self.buy_button.setEnabled(True)
                            print(f"🔥 趋势点击后强制启用买入按钮: {selected_token.get('symbol', 'Unknown')}")
                        
                        # 在消息日志中显示选择的代币
                        self.add_message_to_log(f"📈 从趋势列表选择代币: {selected_token.get('symbol', 'Unknown')}", "info")
                        
                        # 🔥 调试买入按钮状态
                        self.debug_buy_button_state()
                    else:
                        print("❌ 未能识别点击的代币")
                        self.add_message_to_log(f"⚠️ 无法识别点击的代币，请重试", "warning")
                
            except Exception as e:
                print(f"❌ 点击处理异常: {e}")
                logger.error(f"趋势文本点击处理异常: {e}", exc_info=True)
        
        # 调用原始的鼠标事件处理
        QTextEdit.mousePressEvent(self.trend_text_display, event)
    
    # 🔥 新增：持仓数据获取方法，供其他地方使用
    def get_holdings_raw(self) -> list:
        """获取原始持仓数据（未处理）"""
        return self.holdings_list_raw.copy() if hasattr(self, 'holdings_list_raw') else []
    
    def get_holdings_full(self) -> list:
        """获取完整持仓数据（包含所有字段，最多50个）"""
        return self.holdings_list_full.copy() if hasattr(self, 'holdings_list_full') else []
    
    def get_holdings_normalized(self) -> list:
        """获取标准化持仓数据（用于后台图表分析）"""
        return self.holdings_list_normalized.copy() if hasattr(self, 'holdings_list_normalized') else []
    
    def get_holdings_filtered(self) -> list:
        """获取过滤后的持仓数据（去除小额持仓，value > 1 USD）"""
        return self.holdings_list_filtered.copy() if hasattr(self, 'holdings_list_filtered') else []
    
    def get_holdings_by_symbol(self, symbol: str) -> dict:
        """根据代币符号获取持仓信息"""
        if not hasattr(self, 'holdings_list_raw'):
            return {}
        
        for holding in self.holdings_list_raw:
            if holding.get('symbol', '').upper() == symbol.upper():
                return holding.copy()
        return {}
    
    def get_holdings_by_address(self, address: str) -> dict:
        """根据代币地址获取持仓信息（支持多种字段名）"""
        if not hasattr(self, 'holdings_list_raw'):
            return {}
        
        if not self.holdings_list_raw:
            return {}
            
        target_address = address.lower()
        
        # 检查多个可能的字段名（按优先级排序）
        address_fields = ['address', 'tokenAddress', 'mint', 'token_address']
        
        for i, holding in enumerate(self.holdings_list_raw):
            for field in address_fields:
                holding_address = holding.get(field, '')
                if holding_address and holding_address.lower() == target_address:
                    symbol = holding.get('symbol', 'Unknown')
                    print(f"🎯 找到代币: {symbol} (地址字段: {field})")
                    return holding.copy()
        
        # 如果没找到，输出简要调试信息
        print(f"❌ 未找到代币地址: {target_address}")
        print(f"📊 当前持仓数量: {len(self.holdings_list_raw)}")
        
        return {}
    
    def get_holdings_count(self) -> dict:
        """获取各类持仓数据的数量统计"""
        return {
            'raw': len(self.holdings_list_raw) if hasattr(self, 'holdings_list_raw') else 0,
            'full': len(self.holdings_list_full) if hasattr(self, 'holdings_list_full') else 0,
            'normalized': len(self.holdings_list_normalized) if hasattr(self, 'holdings_list_normalized') else 0,
            'filtered': len(self.holdings_list_filtered) if hasattr(self, 'holdings_list_filtered') else 0,
        }
    
    def get_top_holdings(self, limit: int = 10) -> list:
        """获取按价值排序的前N个持仓"""
        if not hasattr(self, 'holdings_list_full'):
            return []
        
        # 按USD价值排序
        sorted_holdings = sorted(
            self.holdings_list_full, 
            key=lambda x: x.get('valueUsd', 0), 
            reverse=True
        )
        return sorted_holdings[:limit]
    
    def has_holdings_data(self) -> bool:
        """检查是否有持仓数据"""
        return (hasattr(self, 'holdings_list_raw') and 
                len(self.holdings_list_raw) > 0)
    
    def get_total_portfolio_value(self) -> float:
        """获取总投资组合价值（USD）"""
        if not hasattr(self, 'holdings_list_full'):
            return 0.0
        
        total_value = sum(holding.get('valueUsd', 0) for holding in self.holdings_list_full)
        return total_value
    
    def print_holdings_summary(self):
        """打印持仓数据汇总（用于调试）"""
        counts = self.get_holdings_count()
        total_value = self.get_total_portfolio_value()
        
        print(f"\n🔍 持仓数据汇总:")
        print(f"   - 原始数据: {counts['raw']} 个")
        print(f"   - 完整数据: {counts['full']} 个")
        print(f"   - 标准化数据: {counts['normalized']} 个")
        print(f"   - 过滤数据: {counts['filtered']} 个")
        print(f"   - 总投资组合价值: ${total_value:.2f}")
        
        if counts['full'] > 0:
            top_3 = self.get_top_holdings(3)
            print(f"   - 前3大持仓:")
            for i, holding in enumerate(top_3, 1):
                symbol = holding.get('symbol', 'Unknown')
                value = holding.get('valueUsd', 0)
                print(f"     {i}. {symbol}: ${value:.2f}")
        print()  # 空行
    
    @pyqtSlot()
    def show_wallet_balance_shortcut(self):
        """快捷键方法：显示钱包中所有代币的详细信息（地址、符号、余额、rawBalance）"""
        print("\n" + "="*80)
        print("🔍 钱包持仓详细信息 (Ctrl+Shift+K)")
        print("="*80)


        
        # 检查钱包连接状态
        if not self.wallet_connected:
            print("❌ 钱包未连接")
            self.add_message_to_log("❌ 钱包未连接，无法查看持仓信息", "warning")
            return
        
        if not hasattr(self, 'wallet_address') or not self.wallet_address:
            print("❌ 钱包地址为空")
            self.add_message_to_log("❌ 钱包地址为空", "warning")
            return
        
        print(f"📍 钱包地址: {self.wallet_address}")
        print()
        
        # 获取持仓数据
        holdings_raw = self.get_holdings_raw()
        holdings_full = self.get_holdings_full()
        
        if not holdings_raw and not holdings_full:
            print("📊 没有持仓数据")
            print("💡 提示: 请先刷新持仓数据")
            self.add_message_to_log("📊 没有持仓数据，请先刷新", "info")
            return
        
        # 使用原始数据（更完整）
        holdings_to_display = holdings_raw if holdings_raw else holdings_full

        holdings_to_display = holdings_to_display[:30]
        
        print(f"📊 总共发现 {len(holdings_to_display)} 个代币持仓")
        print()
        
        # 按价值排序（如果有价值信息）
        try:
            sorted_holdings = sorted(
                holdings_to_display, 
                key=lambda x: float(x.get('valueUsd', 0)), 
                reverse=True
            )
        except:
            sorted_holdings = holdings_to_display
        
        # 显示每个代币的详细信息
        total_value_usd = 0
        for i, holding in enumerate(sorted_holdings, 1):
            try:
                # 基本信息
                symbol = holding.get('symbol', 'Unknown')
                name = holding.get('name', 'Unknown')
                address = holding.get('tokenAddress', holding.get('address', 'Unknown'))
                
                # 余额信息
                balance = holding.get('balance', 0)
                rawBalance = holding.get('rawBalance', 0)
                decimals = holding.get('decimals', 6)
                
                # 价值信息
                valueUsd = float(holding.get('valueUsd', 0))
                priceUsd = float(holding.get('priceUsd', 0))
                total_value_usd += valueUsd
                
                print(f"【{i:2d}】 {symbol} ({name})")
                print(f"     📍 地址: {address}")
                print(f"     💰 余额: {balance:,.6f} {symbol}")
                print(f"     🔢 原始余额: {rawBalance}")
                print(f"     📊 小数位数: {decimals}")
                
                if valueUsd > 0:
                    print(f"     💵 价值: ${valueUsd:,.2f} USD")
                if priceUsd > 0:
                    print(f"     💲 单价: ${priceUsd:.6f} USD")
                
                # 显示其他可用字段
                other_fields = []
                for key, value in holding.items():
                    if key not in ['symbol', 'name', 'tokenAddress', 'address', 'balance', 
                                 'rawBalance', 'decimals', 'valueUsd', 'priceUsd']:
                        if value is not None and value != '' and value != 0:
                            other_fields.append(f"{key}: {value}")
                
                if other_fields:
                    print(f"     📋 其他: {', '.join(other_fields[:3])}")  # 只显示前3个
                
                print()  # 空行分隔
                
            except Exception as e:
                print(f"【{i:2d}】 ❌ 解析代币信息时出错: {e}")
                print(f"     原始数据: {holding}")
                print()
        
        # 显示汇总信息
        print("-" * 80)
        print(f"📊 汇总信息:")
        print(f"   - 总代币数量: {len(sorted_holdings)}")
        
        if total_value_usd > 0:
            print(f"   - 总投资组合价值: ${total_value_usd:,.2f} USD")
            
            # 显示前5大持仓
            top_5 = [h for h in sorted_holdings[:5] if float(h.get('valueUsd', 0)) > 0]
            if top_5:
                print(f"   - 前5大持仓:")
                for i, holding in enumerate(top_5, 1):
                    symbol = holding.get('symbol', 'Unknown')
                    value = float(holding.get('valueUsd', 0))
                    percentage = (value / total_value_usd) * 100
                    print(f"     {i}. {symbol}: ${value:,.2f} ({percentage:.1f}%)")
        else:
            print(f"   - 投资组合价值: 无法计算（缺少价格数据）")
        
        print("-" * 80)
        
        # 记录到消息日志
        self.add_message_to_log(
            f"🔍 已显示钱包持仓详情: {len(sorted_holdings)} 个代币, "
            f"总价值: ${total_value_usd:,.2f}", 
            "info"
        )
        
        # 显示刷新提示
        print(f"💡 提示:")
        print(f"   - 数据更新时间: 根据最后一次刷新持仓的时间")
        print(f"   - 如需最新数据，请点击持仓面板的刷新按钮")
        print(f"   - 原始余额用于实际交易，格式化余额用于显示")
        print("="*80)
        print()
    
    @pyqtSlot(int, int)
    def on_unprocessed_signal_clicked(self, row: int, column: int):
        """未处理信号表格点击事件"""
        try:
            logger.info(f"点击未处理信号表格：行{row}，列{column}")
            
            # 获取未处理信号数据
            all_unprocessed_signals = self.get_unprocessed_signals()
            current_unix_time = time.time()
            onehour_ago_unix = current_unix_time - (60 * 60)
            
            recent_signals = [
                s for s in all_unprocessed_signals 
                if s.get('timestamp', 0) >= onehour_ago_unix
            ]
            
            if row >= len(recent_signals):
                logger.warning(f"无效的行索引: {row}，总行数: {len(recent_signals)}")
                return
            
            signal_data = recent_signals[row]
            token_address = signal_data.get('token_address', '')
            token_symbol = signal_data.get('token_symbol', 'Unknown')
            signal_type = signal_data.get('signal_type', 'Unknown')
            price = signal_data.get('price', 0.0)
            strategy_name = signal_data.get('strategy_name', 'Unknown')
            
            logger.info(f"选择信号代币: {token_symbol} ({token_address})")
            
            # 构建代币信息对象（类似趋势币数据格式）
            token_info = {
                'symbol': token_symbol,
                'name': token_symbol,  # 使用symbol作为name
                'price': price,
                'tokenAddress': token_address,
                'source': 'unprocessed_signal'
            }
            
            # 清空现有的策略信号记录
            self.trade_records.clear()
            self.trade_record_id_counter = 0
            logger.info(f"已清空所有信号记录，为信号代币 {token_symbol} 做准备")
            
            # 加载图表
            self.load_token_chart(token_info)
            
            # 更新信号记录显示
            self.update_trade_records_display()
            self.update_trade_statistics()
            
            # 启用策略执行按钮（如果已选择策略）- 注释掉因为按钮不存在
            # if self.selected_strategy:
            #     self.execute_strategy_button.setEnabled(True)
            #     logger.info(f"策略执行按钮已启用 - 代币: {token_symbol}, 策略: {self.selected_strategy.name}")
            # else:
            #     self.execute_strategy_button.setEnabled(False)
            #     logger.info(f"策略执行按钮禁用 - 代币: {token_symbol}, 未选择策略")
            
            # 启用买入按钮（选中代币后即可买入）
            self.buy_button.setEnabled(True)
            logger.info(f"买入按钮已启用 - 代币: {token_symbol}")
            
            # 重置代币余额并更新卖出按钮状态
            self.current_token_balance = 0
            self.update_sell_button_state()
            
            # 自动刷新代币余额
            if self.wallet_connected and hasattr(self, 'wallet_address'):
                self.refresh_token_balance()
            else:
                self.token_balance_label.setText("余额: 未连接钱包")
                self.update_balance_display("余额: 未连接钱包", error=True)
            
            # 记录到消息日志
            self.add_message_to_log(
                f"📊 已选择信号代币: {token_symbol} ({signal_type.upper()}) - {strategy_name}",
                "info"
            )
                
        except Exception as e:
            logger.error(f"处理未处理信号点击事件失败: {e}")
            self.add_message_to_log(f"❌ 处理信号点击失败: {str(e)}", "error")

    def update_webhook_status_display(self, status_text: str, style_type: str):
        """更新Webhook状态显示"""
        if hasattr(self, 'webhook_status_label'):
            self.webhook_status_label.setText(status_text)
            self.webhook_status_label.setStyleSheet(self.webhook_manager.get_status_style())
