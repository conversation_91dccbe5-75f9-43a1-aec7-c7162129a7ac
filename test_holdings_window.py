#!/usr/bin/env python3
"""
测试新版持仓面板窗口功能
"""

import sys
import logging
import time
from PyQt5.QtWidgets import QApplication, QVBoxLayout, QWidget, QPushButton, QLabel, QHBoxLayout
from PyQt5.QtCore import Qt, QTimer

# 导入新版持仓面板
from holdings_panel_window import HoldingsWindow
from api_service import APIService
from config import PORTFOLIO_CONFIG

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(name)s - %(message)s'
)

logger = logging.getLogger(__name__)


class TestMainWindow(QWidget):
    """测试主窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("测试新版持仓面板")
        self.setGeometry(100, 100, 600, 400)
        
        # API服务
        self.api_service = APIService()
        
        # 持仓窗口引用
        self.holdings_window = None
        
        self._init_ui()
    
    def _init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        
        # 标题
        title_label = QLabel("新版持仓面板测试")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("font-size: 18px; font-weight: bold; margin: 20px;")
        layout.addWidget(title_label)
        
        # 配置信息
        config_info = QLabel(f"""
配置信息:
• OKX DEX API: {PORTFOLIO_CONFIG['okx_dex_api_url']}
• 刷新间隔: {PORTFOLIO_CONFIG['refresh_interval']/1000}秒
• 默认策略: {PORTFOLIO_CONFIG['default_strategy']}
• 最大监控数量: {PORTFOLIO_CONFIG.get('max_monitor_count', 20)}
        """)
        config_info.setStyleSheet("background-color: #f0f0f0; padding: 10px; border-radius: 5px;")
        layout.addWidget(config_info)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        # 打开持仓面板按钮
        self.open_holdings_btn = QPushButton("打开持仓面板")
        self.open_holdings_btn.setMinimumHeight(40)
        self.open_holdings_btn.clicked.connect(self.open_holdings_window)
        button_layout.addWidget(self.open_holdings_btn)
        
        # 测试OKX连接按钮
        self.test_okx_btn = QPushButton("测试OKX连接")
        self.test_okx_btn.setMinimumHeight(40)
        self.test_okx_btn.clicked.connect(self.test_okx_connection)
        button_layout.addWidget(self.test_okx_btn)
        
        layout.addLayout(button_layout)
        
        # 状态信息
        self.status_label = QLabel("状态: 就绪")
        self.status_label.setStyleSheet("padding: 10px; background-color: #e8f5e8; border-radius: 3px;")
        layout.addWidget(self.status_label)
        
        # 功能说明
        features_info = QLabel("""
功能特性:
✅ 自动从OKX DEX API获取钱包地址
✅ 实时获取Solana钱包持仓数据
✅ 按价值排序，支持选择分析前30个代币
✅ 自动OHLCV数据下载和策略分析
✅ 策略信号实时显示（买入/卖出/持有/观察）
✅ 双击或点击按钮打开K线图表
✅ 风险代币检测和标记
✅ 30秒自动刷新周期
        """)
        features_info.setStyleSheet("background-color: #f9f9f9; padding: 10px; border-radius: 5px; margin-top: 10px;")
        layout.addWidget(features_info)
        
        layout.addStretch()
        
        # 使用说明
        usage_info = QLabel("""
使用说明:
1. 点击"测试OKX连接"验证API连接
2. 点击"打开持仓面板"启动主功能
3. 持仓面板会自动获取钱包地址和持仓数据
4. 自动策略分析每30秒运行一次
5. 双击代币行或点击"图表"按钮查看K线
        """)
        usage_info.setStyleSheet("background-color: #fff3cd; padding: 10px; border-radius: 5px; font-size: 11px;")
        layout.addWidget(usage_info)
    
    def open_holdings_window(self):
        """打开持仓面板窗口"""
        try:
            if self.holdings_window and self.holdings_window.isVisible():
                self.holdings_window.raise_()
                self.holdings_window.activateWindow()
                self.status_label.setText("状态: 持仓面板已打开")
                return
            
            self.status_label.setText("状态: 正在初始化持仓面板...")
            
            # 创建新的持仓窗口
            self.holdings_window = HoldingsWindow(
                parent=None,  # 独立窗口
                api_service=self.api_service
            )
            
            # 显示窗口
            self.holdings_window.show()
            
            self.status_label.setText("状态: 持仓面板已打开，正在获取钱包信息...")
            
            logger.info("TestMainWindow: 持仓面板已打开")
            
        except Exception as e:
            error_msg = f"状态: 打开持仓面板失败 - {str(e)}"
            self.status_label.setText(error_msg)
            logger.error(f"TestMainWindow: 打开持仓面板失败: {e}")
    
    def test_okx_connection(self):
        """测试OKX DEX API连接"""
        try:
            self.status_label.setText("状态: 正在测试OKX连接...")
            
            from okx_dex_client import OKXDexClient
            
            # 创建OKX客户端
            okx_client = OKXDexClient(PORTFOLIO_CONFIG["okx_dex_api_url"])
            
            # 测试健康检查
            health_result = okx_client.health_check()
            
            if health_result.get("success"):
                self.status_label.setText("状态: ✅ OKX DEX API连接成功")
                logger.info("TestMainWindow: OKX DEX API连接成功")
                
                # 尝试获取钱包信息
                QTimer.singleShot(1000, self.test_wallet_info)
            else:
                error_msg = health_result.get("error", "未知错误")
                self.status_label.setText(f"状态: ❌ OKX连接失败 - {error_msg}")
                logger.error(f"TestMainWindow: OKX连接失败: {error_msg}")
                
        except Exception as e:
            error_msg = f"状态: ❌ OKX连接异常 - {str(e)}"
            self.status_label.setText(error_msg)
            logger.error(f"TestMainWindow: OKX连接异常: {e}")
    
    def test_wallet_info(self):
        """测试获取钱包信息"""
        try:
            from okx_dex_client import OKXDexClient
            
            okx_client = OKXDexClient(PORTFOLIO_CONFIG["okx_dex_api_url"])
            wallet_info = okx_client.get_wallet_info()
            
            if wallet_info.get("success"):
                wallet_data = wallet_info.get("data", {})
                # 🔥 修复：适配实际API响应格式
                solana_wallet = (
                    wallet_data.get("solanaWallet", "") or 
                    wallet_data.get("solanaWalletAddress", "") or
                    wallet_data.get("supportedChains", {}).get("solana", {}).get("walletAddress", "")
                )
                
                if solana_wallet:
                    short_address = f"{solana_wallet[:8]}...{solana_wallet[-4:]}"
                    self.status_label.setText(f"状态: ✅ 已获取钱包地址: {short_address}")
                    logger.info(f"TestMainWindow: 获取到Solana钱包: {solana_wallet}")
                else:
                    self.status_label.setText("状态: ⚠️ 钱包连接成功但未找到Solana地址")
                    logger.warning("TestMainWindow: 未找到Solana钱包地址")
            else:
                error_msg = wallet_info.get("error", "未知错误")
                self.status_label.setText(f"状态: ❌ 获取钱包信息失败 - {error_msg}")
                logger.error(f"TestMainWindow: 获取钱包信息失败: {error_msg}")
                
        except Exception as e:
            error_msg = f"状态: ❌ 获取钱包信息异常 - {str(e)}"
            self.status_label.setText(error_msg)
            logger.error(f"TestMainWindow: 获取钱包信息异常: {e}")
    
    def closeEvent(self, event):
        """窗口关闭事件"""
        try:
            # 关闭持仓窗口
            if self.holdings_window:
                self.holdings_window.close()
            
            # 关闭API服务
            if hasattr(self.api_service, 'shutdown'):
                self.api_service.shutdown()
            
            logger.info("TestMainWindow: 测试主窗口已关闭")
            
        except Exception as e:
            logger.error(f"TestMainWindow: 关闭窗口时出错: {e}")
        
        super().closeEvent(event)


def test_okx_client_standalone():
    """独立测试OKX客户端连接"""
    print("\n" + "="*50)
    print("🔥 独立测试OKX DEX API连接")
    print("="*50)
    
    try:
        from okx_dex_client import OKXDexClient
        
        # 创建客户端
        okx_client = OKXDexClient(PORTFOLIO_CONFIG["okx_dex_api_url"])
        print(f"✅ OKX客户端创建成功: {PORTFOLIO_CONFIG['okx_dex_api_url']}")
        
        # 1. 健康检查
        print("\n1. 测试健康检查...")
        health_result = okx_client.health_check()
        print(f"   健康检查结果: {health_result}")
        
        if not health_result.get("success"):
            print("❌ OKX DEX API健康检查失败")
            return False
        
        # 2. 获取钱包信息
        print("\n2. 测试获取钱包信息...")
        wallet_info = okx_client.get_wallet_info()
        print(f"   钱包信息结果: {wallet_info}")
        
        if wallet_info.get("success"):
            wallet_data = wallet_info.get("data", {})
            # 🔥 修复：适配实际API响应格式
            solana_wallet = (
                wallet_data.get("solanaWallet", "") or 
                wallet_data.get("solanaWalletAddress", "") or
                wallet_data.get("supportedChains", {}).get("solana", {}).get("walletAddress", "")
            )
            
            if solana_wallet:
                print(f"✅ 获取到Solana钱包地址: {solana_wallet}")
                
                # 3. 测试获取总价值
                print("\n3. 测试获取总价值...")
                from okx_dex_client import TotalValueRequest
                total_value_request = TotalValueRequest(
                    address=solana_wallet,
                    chains="501"
                )
                total_value_result = okx_client.get_total_value(total_value_request)
                print(f"   总价值结果: {total_value_result}")
                
                # 4. 测试获取持仓（简化版）
                print("\n4. 测试获取持仓...")
                from okx_dex_client import AllTokenBalancesRequest
                holdings_request = AllTokenBalancesRequest(
                    address=solana_wallet,
                    chains="501",
                    exclude_risk_token="0"
                )
                holdings_result = okx_client.get_all_token_balances(holdings_request)
                
                if holdings_result.get("success"):
                    holdings_data = holdings_result.get("data", [])
                    print(f"✅ 获取到 {len(holdings_data)} 个代币持仓")
                    
                    # 显示前5个
                    if isinstance(holdings_data, list) and len(holdings_data) > 0:
                        display_count = min(5, len(holdings_data))
                        for i in range(display_count):
                            holding = holdings_data[i]
                            symbol = holding.get('symbol', 'Unknown')
                            value = holding.get('value', 0)
                            print(f"   [{i+1}] {symbol}: ${value}")
                    else:
                        print("   暂无持仓数据或数据格式异常")
                        print(f"   数据类型: {type(holdings_data)}")
                        print(f"   数据内容: {holdings_data}")
                else:
                    print(f"❌ 获取持仓失败: {holdings_result.get('error')}")
                
                return True
            else:
                print("❌ 未找到Solana钱包地址")
                return False
        else:
            print(f"❌ 获取钱包信息失败: {wallet_info.get('error')}")
            return False
        
    except Exception as e:
        print(f"❌ 独立测试异常: {e}")
        return False


def main():
    """主函数"""
    print("🚀 启动新版持仓面板测试")
    
    # 先进行独立测试
    if not test_okx_client_standalone():
        print("\n❌ 独立测试失败，请检查OKX DEX API配置")
        print(f"当前API地址: {PORTFOLIO_CONFIG['okx_dex_api_url']}")
        print("\n建议:")
        print("1. 确保OKX DEX API服务正在运行")
        print("2. 检查API地址是否正确")
        print("3. 确保网络连接正常")
        return
    
    print("\n✅ 独立测试通过，启动GUI界面...")
    
    # 创建QApplication
    app = QApplication(sys.argv)
    
    # 设置应用信息
    app.setApplicationName("新版持仓面板测试")
    app.setApplicationVersion("1.0")
    
    try:
        # 创建测试主窗口
        test_window = TestMainWindow()
        test_window.show()
        
        print("✅ GUI界面已启动")
        print("\n使用说明:")
        print("1. 点击 '测试OKX连接' 验证API连接")
        print("2. 点击 '打开持仓面板' 启动主功能")
        print("3. 持仓面板会自动获取钱包数据并分析")
        
        # 运行应用
        sys.exit(app.exec_())
        
    except Exception as e:
        logger.error(f"启动GUI失败: {e}")
        print(f"❌ 启动GUI失败: {e}")


if __name__ == '__main__':
    main() 