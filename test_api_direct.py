"""
直接测试API功能，不依赖Qt
"""
import sys
import logging
from api_service import APIService

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('test_api')

def test_trending_tokens():
    """测试获取趋势代币"""
    print("\n=== 测试获取趋势代币 ===")
    
    def on_success(tokens):
        print(f"成功获取到 {len(tokens)} 个代币")
        if tokens:
            print(f"第一个代币: {tokens[0].get('name')} ({tokens[0].get('symbol')})")
    
    def on_error(error_msg):
        print(f"获取趋势代币失败: {error_msg}")
    
    # 直接调用API
    print("同步调用...")
    tokens = APIService.get_trending_tokens()
    print(f"同步获取到 {len(tokens) if tokens else 0} 个代币")
    
    # 异步调用
    print("\n异步调用...")
    APIService.get_trending_tokens_async(on_success, on_error)
    
    # 等待异步调用完成
    import time
    print("等待异步调用完成...")
    time.sleep(5)

if __name__ == "__main__":
    test_trending_tokens()
