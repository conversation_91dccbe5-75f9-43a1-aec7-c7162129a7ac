"""
测试买入次数限制功能
"""
import sys
import os

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.append(current_dir)

import pandas as pd
import numpy as np
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_buy_limit():
    """测试买入次数限制功能"""
    try:
        logger.info("开始导入模块...")
        
        from strategies.base_strategy import BaseStrategy
        from config import STRATEGY_CONFIG
        
        logger.info("模块导入成功")
        
        class TestStrategy(BaseStrategy):
            """用于测试的简单策略"""
            
            def __init__(self):
                super().__init__(
                    name="测试买入限制策略",
                    description="用于测试最大买入次数限制的策略"
                )
            
            def generate_signals(self, df: pd.DataFrame) -> pd.DataFrame:
                """生成简单的测试信号"""
                df['signal'] = 0
                
                # 创建多个买入信号（超过限制）来测试限制功能
                buy_positions = [5, 10, 15, 20, 25, 30, 35]  # 7个买入信号，超过5次限制
                for pos in buy_positions:
                    if pos < len(df):
                        df.iloc[pos, df.columns.get_loc('signal')] = 1
                
                # 在买入后的几根K线添加卖出信号
                sell_positions = [8, 13, 18, 23, 28, 33, 38]
                for pos in sell_positions:
                    if pos < len(df):
                        df.iloc[pos, df.columns.get_loc('signal')] = -1
                
                return df

        logger.info("=" * 50)
        logger.info("开始测试买入次数限制功能")
        logger.info(f"配置的最大买入次数: {STRATEGY_CONFIG.get('max_buy_times_per_coin', 5)}")
        logger.info(f"配置的每次买入金额: {STRATEGY_CONFIG.get('trade_amount', 500.0)}")
        logger.info("=" * 50)
        
        # 创建测试数据
        num_candles = 50
        np.random.seed(42)  # 设置随机种子以保证结果可重现
        
        data = {
            'timestamp': range(num_candles),
            'datetime': [f"2024-01-01 00:{i:02d}:00" for i in range(num_candles)],
            'open': np.random.uniform(0.0001, 0.0002, num_candles),
            'high': np.random.uniform(0.0001, 0.0002, num_candles),
            'low': np.random.uniform(0.0001, 0.0002, num_candles),
            'close': np.random.uniform(0.0001, 0.0002, num_candles),
            'volume': np.random.uniform(1000, 10000, num_candles)
        }
        
        # 确保价格符合逻辑
        for i in range(num_candles):
            data['low'][i] = min(data['open'][i], data['high'][i], data['low'][i], data['close'][i])
            data['high'][i] = max(data['open'][i], data['high'][i], data['low'][i], data['close'][i])
        
        df = pd.DataFrame(data)
        logger.info(f"创建了 {len(df)} 根K线数据")
        
        # 创建测试策略
        strategy = TestStrategy()
        logger.info("策略创建成功")
        
        # 生成信号
        df_with_signals = strategy.generate_signals(df)
        
        buy_signals = (df_with_signals['signal'] == 1).sum()
        sell_signals = (df_with_signals['signal'] == -1).sum()
        
        logger.info(f"生成的买入信号数量: {buy_signals}")
        logger.info(f"生成的卖出信号数量: {sell_signals}")
        
        # 执行回测
        initial_capital = 10000.0
        logger.info("开始执行回测...")
        result = strategy.backtest(df_with_signals, initial_capital)
        
        # 输出结果
        logger.info("\n" + "=" * 50)
        logger.info("回测结果:")
        logger.info(f"策略名称: {result['strategy_name']}")
        logger.info(f"初始资金: ${result['initial_capital']:.2f}")
        logger.info(f"最终资金: ${result['final_capital']:.2f}")
        logger.info(f"总盈亏: ${result['total_profit']:.2f}")
        logger.info(f"盈亏百分比: {result['profit_percentage']:.2f}%")
        logger.info(f"交易次数: {result['trade_count']}")
        logger.info(f"实际买入次数: {result['buy_count']}")
        logger.info(f"最大买入次数限制: {result['max_buy_times']}")
        logger.info(f"买入次数使用率: {result['buy_times_used_percentage']:.1f}%")
        logger.info("=" * 50)
        
        # 验证限制是否生效
        if result['buy_count'] <= result['max_buy_times']:
            logger.info("✅ 买入次数限制功能正常工作！")
            if result['buy_count'] == result['max_buy_times']:
                logger.info("✅ 达到最大买入次数限制")
            else:
                logger.info(f"ℹ️ 买入次数未达到限制（{result['buy_count']}/{result['max_buy_times']}）")
        else:
            logger.error("❌ 买入次数限制功能未生效！")
        
        # 显示交易详情
        buy_trades = [t for t in result['trades'] if t['type'] == 'buy']
        logger.info(f"\n买入交易详情 (共{len(buy_trades)}笔):")
        for i, trade in enumerate(buy_trades, 1):
            logger.info(f"  第{i}次买入: 时间={trade['datetime']}, 价格={trade['price']:.8f}, 序号={trade.get('buy_sequence', 'N/A')}")
            
        return True
        
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("开始运行买入次数限制测试...")
    success = test_buy_limit()
    if success:
        print("测试完成")
    else:
        print("测试失败")
        sys.exit(1) 