#!/usr/bin/env python3
"""
策略分析调试脚本
测试MultiThreadOHLCVManager的策略分析功能
"""

import logging
import sys
import time

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

def test_strategy_analysis():
    """测试策略分析功能"""
    print("🔥 策略分析功能调试测试")
    print("=" * 50)
    
    try:
        # 导入必要模块
        from multi_thread_ohlcv_manager import MultiThreadOHLCVManager
        from api_service import APIService
        from PyQt5.QtCore import QObject, pyqtSignal
        from PyQt5.QtWidgets import QApplication
        
        print("✅ 成功导入必要模块")
        
        # 创建QApplication
        app = QApplication(sys.argv) if not QApplication.instance() else QApplication.instance()
        
        # 创建API服务和管理器
        api_service = APIService()
        manager = MultiThreadOHLCVManager(api_service, max_workers=1)
        
        print("✅ 成功创建管理器实例")
        
        # 测试数据
        test_token_data = {
            'tokenAddress': 'So11111111111111111111111111111111111111112',
            'symbol': 'SOL',
            'strategy_name': 'VWAP 交叉策略',
            'source': 'trend'
        }
        
        # 模拟OHLCV数据（简单的价格走势）
        test_ohlcv_data = []
        base_price = 100.0
        for i in range(100):
            price = base_price + (i % 10) * 0.5  # 简单的价格波动
            test_ohlcv_data.append({
                'timestamp': int(time.time()) - (100-i) * 60,
                'open': price,
                'high': price + 0.5,
                'low': price - 0.5,
                'close': price + 0.2,
                'volume': 1000 + i * 10
            })
        
        print(f"✅ 创建测试数据 - {len(test_ohlcv_data)} 条OHLCV数据")
        
        # 设置信号接收器
        signal_received = {'count': 0}
        
        def on_strategy_signal(token_address, symbol, signal_type, price, timestamp, strategy_name):
            signal_received['count'] += 1
            signal_zh = {'buy': '买入', 'sell': '卖出', 'hold': '持有', 'wait': '观察'}.get(signal_type, signal_type)
            print(f"📊 接收到策略信号 - {symbol}: {signal_zh} @ ${price:.2f}")
            print(f"   地址: {token_address[:8]}...")
            print(f"   策略: {strategy_name}")
            print(f"   时间: {time.strftime('%H:%M:%S', time.localtime(timestamp))}")
        
        manager.strategy_signal_generated.connect(on_strategy_signal)
        print("✅ 连接策略信号处理器")
        
        # 测试策略分析功能
        print("\n🔥 开始测试策略分析...")
        try:
            manager.perform_strategy_analysis_and_emit_signal(test_token_data, test_ohlcv_data)
            print("✅ 策略分析函数执行完成")
        except Exception as e:
            print(f"❌ 策略分析执行失败: {e}")
            return False
        
        # 处理Qt事件循环
        print("🔄 处理Qt事件循环...")
        for _ in range(10):  # 给Qt足够时间处理信号
            app.processEvents()
            time.sleep(0.1)
        
        # 检查结果
        print(f"\n📈 测试结果:")
        print(f"   接收到的信号数量: {signal_received['count']}")
        
        if signal_received['count'] > 0:
            print("🎉 策略分析功能正常！信号发射成功")
            return True
        else:
            print("❌ 策略分析未发射信号，可能存在问题")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔥 MultiThreadOHLCVManager 策略分析功能调试")
    print("=" * 60)
    
    success = test_strategy_analysis()
    
    if success:
        print(f"\n{'='*60}")
        print("🎉 策略分析功能测试通过！")
        print("现在应该能正常显示策略信号了")
        sys.exit(0)
    else:
        print(f"\n{'='*60}")
        print("❌ 策略分析功能测试失败")
        print("需要进一步调试")
        sys.exit(1) 