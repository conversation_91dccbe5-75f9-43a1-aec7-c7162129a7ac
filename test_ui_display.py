#!/usr/bin/env python3
"""
测试UI显示效果 - 验证收益率和交易笔数的组合显示
"""

def test_profit_percentage_display():
    """测试收益率和交易笔数的显示格式"""
    
    # 模拟不同的回测结果
    test_cases = [
        {
            'profit_percentage': 15.67,
            'trade_count': 8,
            'description': '盈利情况'
        },
        {
            'profit_percentage': -3.24,
            'trade_count': 12,
            'description': '亏损情况'
        },
        {
            'profit_percentage': 0.0,
            'trade_count': 0,
            'description': '无交易情况'
        },
        {
            'profit_percentage': 150.89,
            'trade_count': 25,
            'description': '高收益情况'
        },
        {
            'profit_percentage': -0.06,
            'trade_count': 4,
            'description': '实际测试结果(meme币VWAP策略)'
        }
    ]
    
    print("=" * 60)
    print("回测结果显示格式测试")
    print("=" * 60)
    
    for case in test_cases:
        profit_percentage = case['profit_percentage']
        trade_count = case['trade_count']
        description = case['description']
        
        # 模拟UI显示逻辑
        percentage_color = "#4caf50" if profit_percentage >= 0 else "#f44336"
        color_name = "绿色" if profit_percentage >= 0 else "红色"
        
        # 按照修改后的格式显示
        display_text = f"{profit_percentage:.2f}% ({trade_count}笔)"
        
        print(f"\n{description}:")
        print(f"  原始数据: 收益率={profit_percentage:.2f}%, 交易次数={trade_count}")
        print(f"  显示文本: {display_text}")
        print(f"  显示颜色: {color_name} ({percentage_color})")
    
    print("\n" + "=" * 60)
    print("测试结论: 修改后的显示格式将收益率和交易笔数合并显示")
    print("格式: [收益率]% ([交易笔数]笔)")
    print("颜色: 根据盈亏情况显示绿色(盈利)或红色(亏损)")
    print("=" * 60)

if __name__ == "__main__":
    test_profit_percentage_display() 