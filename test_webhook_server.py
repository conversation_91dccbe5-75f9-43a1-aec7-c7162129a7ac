#!/usr/bin/env python3
"""
简单的Webhook测试服务器
用于接收和显示来自交易信号的webhook请求
"""

from flask import Flask, request, jsonify
import json
from datetime import datetime

app = Flask(__name__)

@app.route('/webhook', methods=['POST'])
def webhook():
    """接收webhook请求"""
    try:
        # 获取请求头
        headers = dict(request.headers)
        
        # 获取请求体
        data = request.get_json()
        
        # 记录时间
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        print(f"\n{'='*60}")
        print(f"🔔 收到Webhook请求 - {timestamp}")
        print(f"{'='*60}")
        print(f"📋 请求头:")
        for key, value in headers.items():
            if key.lower() not in ['host', 'user-agent', 'accept-encoding', 'connection']:
                print(f"  {key}: {value}")
        
        print(f"\n📊 请求数据:")
        if data:
            if data.get('test', False):
                print(f"  🧪 这是一个测试请求")
            else:
                print(f"  📈 交易信号:")
            
            for key, value in data.items():
                if key == 'timestamp':
                    # 转换时间戳为可读时间
                    try:
                        dt = datetime.fromtimestamp(value)
                        print(f"  {key}: {value} ({dt.strftime('%Y-%m-%d %H:%M:%S')})")
                    except:
                        print(f"  {key}: {value}")
                else:
                    print(f"  {key}: {value}")
        else:
            print("  (空数据)")
        
        print(f"{'='*60}\n")
        
        # 返回成功响应
        return jsonify({
            "status": "success",
            "message": "Webhook接收成功",
            "received_at": timestamp,
            "data_keys": list(data.keys()) if data else []
        }), 200
        
    except Exception as e:
        print(f"❌ 处理Webhook请求失败: {e}")
        return jsonify({
            "status": "error",
            "message": f"处理失败: {str(e)}"
        }), 500

@app.route('/health', methods=['GET'])
def health():
    """健康检查"""
    return jsonify({
        "status": "ok",
        "message": "Webhook服务器运行正常",
        "timestamp": datetime.now().isoformat()
    }), 200

if __name__ == '__main__':
    print("🚀 启动Webhook测试服务器...")
    print("📡 Webhook URL: http://localhost:5001/webhook")
    print("🔍 健康检查: http://localhost:5001/health")
    print("💡 在交易软件中设置webhook URL为: http://localhost:5001/webhook")
    print("="*60)
    
    app.run(host='0.0.0.0', port=5001, debug=True) 