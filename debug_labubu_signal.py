#!/usr/bin/env python3
"""
LABUBU信号不一致问题调试脚本
"""

import sys
import os
import pandas as pd
import logging
from datetime import datetime, timedelta

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from strategies import StrategyFactory
from api_service import APIService
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer, pyqtSlot

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

class LABUBUSignalDebugger:
    """LABUBU信号调试器"""
    
    def __init__(self):
        self.app = QApplication(sys.argv)
        self.api_service = APIService()
        self.strategy = StrategyFactory.get_strategy_by_name("VWAP 交叉策略")
        self.labubu_address = "JB2wezZLdzWfnaCfHxLg193RS3Rh51ThiXxEDWQDpump"
        self.chart_data = None
        self.trend_data = None
        
    def debug_signal_consistency(self):
        """调试信号一致性"""
        logger.info("🔥🔥🔥 开始LABUBU信号一致性调试")
        
        # 1. 获取图表数据（1440分钟数据）
        logger.info("1. 获取图表数据...")
        self.get_chart_data()
        
    def get_chart_data(self):
        """获取图表数据"""
        # 模拟获取1440分钟的数据
        self.api_service.ohlcv_data_ready.connect(self.on_chart_data_ready)
        self.api_service.get_ohlcv_data_async(
            token_address=self.labubu_address,
            timeframe="1m",
            days=1,
            source="chart"
        )
        
    @pyqtSlot(str, str, list, str)
    def on_chart_data_ready(self, token_address, timeframe, ohlcv_data, source):
        """处理图表数据"""
        if token_address != self.labubu_address:
            return
            
        logger.info(f"收到LABUBU图表数据: {len(ohlcv_data)} 条")
        
        # 转换为DataFrame
        df = pd.DataFrame(ohlcv_data)
        if 'timestamp' in df.columns:
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='s')
            df.set_index('timestamp', inplace=True)
        
        # 应用策略
        df_with_signals = self.strategy.apply_strategy(df)
        
        # 分析信号
        self.analyze_signals(df_with_signals, "图表数据")
        
        # 获取趋势数据进行对比
        logger.info("2. 获取趋势分析数据...")
        self.get_trend_data()
        
    def get_trend_data(self):
        """获取趋势分析数据"""
        # 模拟趋势分析的数据获取
        self.api_service.get_ohlcv_data_async(
            token_address=self.labubu_address,
            timeframe="1m",
            days=1,
            source="trend"
        )
        
        # 由于APIService的限制，我们直接分析趋势逻辑
        QTimer.singleShot(3000, self.analyze_trend_logic)
        
    def analyze_trend_logic(self):
        """分析趋势逻辑"""
        logger.info("3. 分析趋势解释逻辑...")
        
        # 模拟创建与图表相同的信号数据
        if self.chart_data is not None:
            # 应用interpret_latest_signal逻辑
            result = self.interpret_latest_signal_simulation(self.chart_data)
            logger.info(f"🔥🔥🔥 趋势分析解释结果: {result}")
        
        # 结束调试
        QTimer.singleShot(1000, self.app.quit)
        
    def analyze_signals(self, df, data_source):
        """分析信号"""
        if df is None or df.empty:
            logger.error(f"[{data_source}] DataFrame为空")
            return
            
        if 'signal' not in df.columns:
            logger.error(f"[{data_source}] 缺少signal列")
            return
            
        # 保存数据用于后续分析
        if data_source == "图表数据":
            self.chart_data = df
            
        # 分析信号统计
        signal_counts = df['signal'].value_counts()
        logger.info(f"🔥🔥🔥 [{data_source}] 信号统计:")
        logger.info(f"  - 总数据点: {len(df)}")
        logger.info(f"  - 买入信号(1): {signal_counts.get(1, 0)}")
        logger.info(f"  - 卖出信号(-1): {signal_counts.get(-1, 0)}")
        logger.info(f"  - 观察信号(0): {signal_counts.get(0, 0)}")
        
        # 找到最后一个非零信号
        non_zero_signals = df[df['signal'] != 0]
        if not non_zero_signals.empty:
            last_non_zero = non_zero_signals.iloc[-1]
            last_signal_value = last_non_zero['signal']
            last_signal_index = non_zero_signals.index[-1]
            
            logger.info(f"🔥🔥🔥 [{data_source}] 最后非零信号:")
            logger.info(f"  - 信号值: {last_signal_value}")
            logger.info(f"  - 信号索引: {last_signal_index}")
            logger.info(f"  - 信号时间: {last_signal_index}")
            logger.info(f"  - 价格: {last_non_zero.get('close', 'N/A')}")
        
        # 显示最近10个信号
        recent_signals = df['signal'].tail(10).tolist()
        logger.info(f"🔥🔥🔥 [{data_source}] 最近10个信号: {recent_signals}")
        
    def interpret_latest_signal_simulation(self, df):
        """模拟interpret_latest_signal函数的逻辑"""
        try:
            if df.empty or 'signal' not in df.columns:
                return "观察"
            
            # 检查全部信号 如果全都是0 那就等于没有信号
            if (df['signal'] == 0).all():
                return "无信号"
            
            # 找到最后一个非0的信号
            non_zero_signals = df[df['signal'] != 0]
            
            if non_zero_signals.empty:
                return "观察"
            
            # 获取最后一个非0信号的位置和值
            last_non_zero_index = non_zero_signals.index[-1]
            last_non_zero_signal = non_zero_signals['signal'].iloc[-1]
            latest_signal = df['signal'].iloc[-1]  # 当前最新信号
            
            logger.info(f"🔥🔥🔥 模拟信号分析:")
            logger.info(f"  - 最新信号: {latest_signal}")
            logger.info(f"  - 最后非0信号: {last_non_zero_signal} (位置: {last_non_zero_index})")
            logger.info(f"  - 总信号数: {len(df)}")
            logger.info(f"  - 非0信号数: {len(non_zero_signals)}")
            
            # 智能持有判断：如果最后非0信号是买入，检查之后是否有超过10个0信号
            if last_non_zero_signal == 1:  # 最后非0信号是买入
                # 计算从最后买入信号之后的0信号数量
                signals_after_buy = df.loc[last_non_zero_index:]['signal']
                zero_signals_after_buy = signals_after_buy[signals_after_buy == 0]
                
                logger.info(f"🔥🔥🔥 买入信号后分析:")
                logger.info(f"  - 买入信号后总信号: {len(signals_after_buy)}")
                logger.info(f"  - 买入信号后0信号数: {len(zero_signals_after_buy)}")
                logger.info(f"  - 买入信号后信号序列: {signals_after_buy.tolist()}")
                
                if len(zero_signals_after_buy) > 10:
                    logger.info(f"🔥🔥 买入后有{len(zero_signals_after_buy)}个0信号，返回持有")
                    return "持有"
                else:
                    logger.info(f"🔥🔥 买入后只有{len(zero_signals_after_buy)}个0信号，返回买入")
                    return "买入"
            
            # 转换信号为文字描述 - 使用最后一个非0信号
            elif last_non_zero_signal == -1:
                logger.info(f"🔥🔥🔥 最后非0信号为-1，返回卖出")
                return "卖出"
            else:
                # 如果最后非0信号不是±1，则检查持有逻辑
                recent_signals = df['signal'].tail(120)  # 查看最近120个信号
                
                logger.info(f"🔥🔥🔥 特殊信号值: {last_non_zero_signal}")
                logger.info(f"  - 最近120个信号中有买入: {any(recent_signals == 1)}")
                logger.info(f"  - 最近120个信号中有卖出: {any(recent_signals == -1)}")
                
                if any(recent_signals == 1) and not any(recent_signals == -1):
                    return "持有"
                else:
                    return "观察"
                    
        except Exception as e:
            logger.error(f"信号解释失败: {e}")
            return "观察"
    
    def run(self):
        """运行调试器"""
        logger.info("启动LABUBU信号调试器")
        self.debug_signal_consistency()
        
        # 设置超时
        QTimer.singleShot(30000, self.app.quit)
        
        return self.app.exec_()

def main():
    """主函数"""
    debugger = LABUBUSignalDebugger()
    return debugger.run()

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code) 