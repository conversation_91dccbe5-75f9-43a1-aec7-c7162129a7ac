#!/usr/bin/env python
"""
OHLCV API测试脚本 - 用于测试OHLCV API的响应格式
"""

import requests
import json
import time
import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 获取API密钥
API_KEY = os.getenv('BIRDEYE_API_KEY')
print(f"API密钥长度: {len(API_KEY) if API_KEY else 0}")

# OHLCV API URL
OHLCV_API_URL = "https://public-api.birdeye.so/defi/ohlcv"

def test_ohlcv_api():
    """测试OHLCV API"""
    print(f"开始测试OHLCV API: {OHLCV_API_URL}")
    
    try:
        # 添加请求头部
        headers = {
            'X-API-KEY': API_KEY,
            'accept': 'application/json',
            'x-chain': 'solana'
        }
        
        # 设置请求参数
        params = {
            'address': "DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263",  # 测试代币地址
            'type': '1m',
            'time_from': int(time.time()) - 3600,  # 过去一小时
            'time_to': int(time.time()),
            'limit': 60
        }
        
        print("发送请求...")
        response = requests.get(
            OHLCV_API_URL,
            headers=headers,
            params=params,
            timeout=15
        )
        
        print(f"响应状态码: {response.status_code}")
        
        # 检查响应状态
        response.raise_for_status()
        
        # 打印原始响应内容
        print(f"原始响应内容: {response.text[:500]}...")
        
        # 尝试解析JSON
        try:
            data = response.json()
            print(f"响应数据类型: {type(data)}")
            
            # 打印响应数据的结构
            if isinstance(data, dict):
                print(f"响应数据是字典，包含以下键: {list(data.keys())}")
                if 'data' in data:
                    if isinstance(data['data'], list):
                        print(f"数据是列表，包含 {len(data['data'])} 个元素")
                        if data['data']:
                            print(f"第一个元素包含以下键: {list(data['data'][0].keys()) if isinstance(data['data'][0], dict) else '不是字典'}")
                            print(f"第一个元素: {json.dumps(data['data'][0], indent=2)}")
                    else:
                        print(f"数据不是列表，而是 {type(data['data'])}")
            else:
                print(f"响应数据是其他类型: {type(data)}")
                print(f"响应数据: {data}")
            
            return data
        except json.JSONDecodeError as e:
            print(f"解析JSON失败: {str(e)}")
            print(f"原始响应内容: {response.text}")
            return None
    except Exception as e:
        print(f"测试OHLCV API失败: {str(e)}")
        return None

if __name__ == "__main__":
    test_ohlcv_api()
