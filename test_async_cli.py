#!/usr/bin/env python
"""
异步趋势榜单命令行测试脚本 - 不使用GUI，避免Qt库冲突
"""

import time
import logging
import threading
from api_service import APIService

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('async_trend_cli_test')

# 创建事件，用于等待异步操作完成
done_event = threading.Event()

def test_async_trend():
    """测试异步获取趋势榜单"""
    print("开始测试异步获取趋势榜单...")
    
    # 定义成功回调函数
    def on_success(tokens):
        print(f"成功回调函数被调用，获取到 {len(tokens) if tokens else 0} 个代币")
        
        if tokens:
            print(f"第一个代币: {tokens[0]['name']} ({tokens[0]['symbol']})")
            
            # 显示所有代币
            print("\n所有代币列表:")
            for i, token in enumerate(tokens[:10]):  # 只显示前10个
                print(f"{i+1}. {token['name']} ({token['symbol']})")
            
            if len(tokens) > 10:
                print(f"... 还有 {len(tokens) - 10} 个代币未显示")
        else:
            print("未获取到任何代币数据")
        
        # 设置事件，表示异步操作已完成
        done_event.set()
    
    # 定义错误回调函数
    def on_error(error_msg):
        print(f"错误回调函数被调用: {error_msg}")
        
        # 设置事件，表示异步操作已完成
        done_event.set()
    
    # 测试直接获取
    print("先测试直接获取趋势榜单...")
    try:
        tokens = APIService.get_trending_tokens()
        print(f"直接获取到 {len(tokens) if tokens else 0} 个代币")
        if tokens:
            print(f"直接获取的第一个代币: {tokens[0]['name']} ({tokens[0]['symbol']})")
    except Exception as e:
        print(f"直接获取趋势榜单失败: {str(e)}")
    
    # 测试异步获取
    print("\n开始测试异步获取趋势榜单...")
    try:
        task_id = APIService.get_trending_tokens_async(on_success, on_error)
        print(f"异步任务已启动，任务ID: {task_id}")
        
        # 等待异步操作完成，最多等待30秒
        if not done_event.wait(30):
            print("等待超时，异步操作未完成")
    except Exception as e:
        print(f"启动异步任务失败: {str(e)}")

if __name__ == "__main__":
    test_async_trend()
    
    # 等待一段时间，确保所有日志都被打印
    time.sleep(2)
    print("测试完成")
