#!/usr/bin/env python3
"""
策略信号聚合监控主窗口 - 使用QLocalSocket/QLocalServer实现进程间通信
"""

import sys
import logging
import json
import time
from datetime import datetime
from typing import Dict, List, Set
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, 
    QWidget, QPushButton, QLabel, QTableWidget, QTableWidgetItem,
    QHeaderView, QFrame, QSplitter, QTabWidget, QMessageBox,
    QProgressBar, QCheckBox
)
from PyQt5.QtCore import (
    QTimer, pyqtSlot, pyqtSignal, QObject, QLocalServer, 
    QLocalSocket, QByteArray, QDataStream, QIODevice
)
from PyQt5.QtGui import QIcon, QColor, QFont

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("SignalMonitor")

class SignalServer(QObject):
    """策略信号服务器 - 接收来自不同窗口的策略信号"""
    
    signal_received = pyqtSignal(dict)  # 接收到新信号时发射
    
    def __init__(self, server_name="StrategySignalServer", parent=None):
        super().__init__(parent)
        self.server_name = server_name
        self.server = QLocalServer(self)
        self.clients = []
        
        # 尝试移除可能存在的旧服务器
        QLocalServer.removeServer(self.server_name)
        
        # 启动服务器
        if not self.server.listen(self.server_name):
            logger.error(f"无法启动信号服务器: {self.server.errorString()}")
        else:
            logger.info(f"信号服务器已启动: {self.server_name}")
        
        # 连接新连接信号
        self.server.newConnection.connect(self.on_new_connection)
    
    def on_new_connection(self):
        """处理新客户端连接"""
        client_socket = self.server.nextPendingConnection()
        if client_socket:
            logger.info(f"新客户端已连接: {client_socket.socketDescriptor()}")
            self.clients.append(client_socket)
            client_socket.readyRead.connect(lambda: self.on_data_received(client_socket))
            client_socket.disconnected.connect(lambda: self.on_client_disconnected(client_socket))
    
    def on_data_received(self, client_socket):
        """处理接收到的数据"""
        try:
            # 读取所有可用数据
            data = client_socket.readAll().data()
            if not data:
                return
            
            # 解析JSON数据
            signal_data = json.loads(data.decode('utf-8'))
            logger.info(f"接收到信号数据: {signal_data.get('symbol')} {signal_data.get('signal_type')}")
            
            # 发射信号
            self.signal_received.emit(signal_data)
            
        except Exception as e:
            logger.error(f"处理接收数据时出错: {e}")
    
    def on_client_disconnected(self, client_socket):
        """处理客户端断开连接"""
        try:
            logger.info(f"客户端已断开连接: {client_socket.socketDescriptor()}")
            self.clients.remove(client_socket)
            client_socket.deleteLater()
        except Exception as e:
            logger.error(f"处理客户端断开连接时出错: {e}")
    
    def broadcast_message(self, message):
        """向所有客户端广播消息"""
        try:
            data = json.dumps(message).encode('utf-8')
            for client in self.clients:
                if client.state() == QLocalSocket.ConnectedState:
                    client.write(data)
                    client.flush()
        except Exception as e:
            logger.error(f"广播消息时出错: {e}")


class SignalMonitorWindow(QMainWindow):
    """策略信号监控主窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("策略信号聚合监控")
        self.setGeometry(100, 100, 1200, 800)
        
        # 信号服务器
        self.signal_server = SignalServer(parent=self)
        self.signal_server.signal_received.connect(self.on_signal_received)
        
        # 信号存储
        self.signals = []  # 所有接收到的信号
        self.unique_signals = {}  # 按代币地址存储的最新信号
        self.executed_signals = {}  # 已执行的信号记录 {token_address: timestamp}
        
        # 初始化UI
        self._init_ui()
        
        # 设置定时器
        self.update_timer = QTimer(self)
        self.update_timer.timeout.connect(self.update_signal_table)
        self.update_timer.start(5000)  # 每5秒更新一次表格
        
        logger.info("策略信号监控窗口已初始化")
    
    def _init_ui(self):
        """初始化UI"""
        # 主布局
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        main_layout = QVBoxLayout(main_widget)
        
        # 状态面板
        status_frame = QFrame()
        status_frame.setFrameShape(QFrame.StyledPanel)
        status_layout = QHBoxLayout(status_frame)
        
        self.status_label = QLabel("状态: 等待信号...")
        self.connection_label = QLabel("连接: 0 个客户端")
        self.signal_count_label = QLabel("信号: 0 个")
        
        status_layout.addWidget(self.status_label, 3)
        status_layout.addWidget(self.connection_label, 1)
        status_layout.addWidget(self.signal_count_label, 1)
        
        # 自动执行控制
        self.auto_execute_checkbox = QCheckBox("自动执行交易信号")
        self.auto_execute_checkbox.setChecked(True)
        status_layout.addWidget(self.auto_execute_checkbox, 1)
        
        # 信号表格
        self.signal_table = QTableWidget()
        self.signal_table.setColumnCount(9)
        self.signal_table.setHorizontalHeaderLabels([
            "时间", "代币", "信号类型", "价格", "来源", "状态", "执行时间", "延迟(秒)", "操作"
        ])
        self.signal_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.signal_table.setAlternatingRowColors(True)
        
        # 添加到主布局
        main_layout.addWidget(status_frame)
        main_layout.addWidget(self.signal_table)
        
        # 底部控制面板
        control_frame = QFrame()
        control_layout = QHBoxLayout(control_frame)
        
        self.clear_button = QPushButton("清除所有信号")
        self.clear_button.clicked.connect(self.clear_signals)
        
        self.execute_button = QPushButton("执行选中信号")
        self.execute_button.clicked.connect(self.execute_selected_signal)
        
        self.refresh_button = QPushButton("刷新")
        self.refresh_button.clicked.connect(self.update_signal_table)
        
        control_layout.addWidget(self.clear_button)
        control_layout.addWidget(self.execute_button)
        control_layout.addStretch()
        control_layout.addWidget(self.refresh_button)
        
        main_layout.addWidget(control_frame)
    
    @pyqtSlot(dict)
    def on_signal_received(self, signal_data):
        """处理接收到的信号"""
        try:
            # 添加接收时间戳
            signal_data['received_timestamp'] = time.time()
            signal_data['status'] = '待处理'
            
            # 添加到信号列表
            self.signals.append(signal_data)
            
            # 更新状态
            self.status_label.setText(f"状态: 收到新信号 - {signal_data.get('symbol')} {signal_data.get('signal_type')}")
            self.signal_count_label.setText(f"信号: {len(self.signals)} 个")
            
            # 处理信号去重和执行
            self.process_signal(signal_data)
            
            # 更新表格
            self.update_signal_table()
            
        except Exception as e:
            logger.error(f"处理接收信号时出错: {e}")
    
    def process_signal(self, signal_data):
        """处理信号去重和执行逻辑"""
        try:
            token_address = signal_data.get('token_address')
            timestamp = signal_data.get('timestamp')
            
            if not token_address or not timestamp:
                logger.warning(f"信号数据缺少必要字段: {signal_data}")
                return
            
            # 检查是否是最新信号
            if token_address in self.unique_signals:
                existing_signal = self.unique_signals[token_address]
                if existing_signal.get('timestamp', 0) >= timestamp:
                    # 旧信号，标记为已忽略
                    signal_data['status'] = '已忽略(旧信号)'
                    logger.info(f"忽略旧信号: {signal_data.get('symbol')} {signal_data.get('signal_type')}")
                    return
            
            # 检查是否已执行过更新的信号
            if token_address in self.executed_signals:
                last_executed = self.executed_signals[token_address]
                if last_executed >= timestamp:
                    # 已执行过更新的信号，忽略
                    signal_data['status'] = '已忽略(已执行更新信号)'
                    logger.info(f"忽略已执行过的信号: {signal_data.get('symbol')} {signal_data.get('signal_type')}")
                    return
            
            # 更新为最新信号
            self.unique_signals[token_address] = signal_data
            
            # 如果启用了自动执行，则执行信号
            if self.auto_execute_checkbox.isChecked():
                self.execute_signal(signal_data)
            
        except Exception as e:
            logger.error(f"处理信号去重和执行逻辑时出错: {e}")
    
    def execute_signal(self, signal_data):
        """执行交易信号"""
        try:
            token_address = signal_data.get('token_address')
            symbol = signal_data.get('symbol')
            signal_type = signal_data.get('signal_type')
            timestamp = signal_data.get('timestamp')
            
            # 这里添加实际的交易执行逻辑
            # 例如调用交易API执行买入或卖出操作
            
            # 模拟执行延迟
            execution_time = time.time()
            delay = execution_time - signal_data.get('received_timestamp', execution_time)
            
            # 更新信号状态
            signal_data['status'] = '已执行'
            signal_data['execution_time'] = execution_time
            signal_data['delay'] = delay
            
            # 记录已执行信号
            self.executed_signals[token_address] = timestamp
            
            logger.info(f"执行信号: {symbol} {signal_type} (延迟: {delay:.2f}秒)")
            self.status_label.setText(f"状态: 已执行信号 - {symbol} {signal_type}")
            
        except Exception as e:
            logger.error(f"执行交易信号时出错: {e}")
            signal_data['status'] = f'执行失败: {str(e)[:50]}'
    
    def execute_selected_signal(self):
        """执行选中的信号"""
        try:
            selected_rows = self.signal_table.selectedItems()
            if not selected_rows:
                QMessageBox.information(self, "提示", "请先选择要执行的信号")
                return
            
            # 获取选中行的索引
            row_indices = set()
            for item in selected_rows:
                row_indices.add(item.row())
            
            # 执行选中行的信号
            for row in row_indices:
                if row < len(self.signals):
                    signal_data = self.signals[row]
                    if signal_data.get('status') not in ['已执行', '已忽略(旧信号)', '已忽略(已执行更新信号)']:
                        self.execute_signal(signal_data)
            
            # 更新表格
            self.update_signal_table()
            
        except Exception as e:
            logger.error(f"执行选中信号时出错: {e}")
            QMessageBox.warning(self, "错误", f"执行信号时出错: {str(e)}")
    
    def update_signal_table(self):
        """更新信号表格"""
        try:
            # 更新连接状态
            self.connection_label.setText(f"连接: {len(self.signal_server.clients)} 个客户端")
            
            # 清空表格
            self.signal_table.setRowCount(0)
            
            # 按接收时间倒序排序
            sorted_signals = sorted(
                self.signals, 
                key=lambda x: x.get('received_timestamp', 0), 
                reverse=True
            )
            
            # 填充表格
            for i, signal in enumerate(sorted_signals):
                self.signal_table.insertRow(i)
                
                # 时间
                time_str = datetime.fromtimestamp(signal.get('timestamp', 0)).strftime("%H:%M:%S")
                self.signal_table.setItem(i, 0, QTableWidgetItem(time_str))
                
                # 代币
                self.signal_table.setItem(i, 1, QTableWidgetItem(signal.get('symbol', 'Unknown')))
                
                # 信号类型
                signal_type = signal.get('signal_type', 'Unknown')
                signal_item = QTableWidgetItem(signal_type)
                if signal_type == 'buy':
                    signal_item.setForeground(QColor('green'))
                elif signal_type == 'sell':
                    signal_item.setForeground(QColor('red'))
                self.signal_table.setItem(i, 2, signal_item)
                
                # 价格
                price = signal.get('price', 0)
                self.signal_table.setItem(i, 3, QTableWidgetItem(f"${price:.6f}"))
                
                # 来源
                self.signal_table.setItem(i, 4, QTableWidgetItem(signal.get('source', 'Unknown')))
                
                # 状态
                status = signal.get('status', '待处理')
                status_item = QTableWidgetItem(status)
                if status == '已执行':
                    status_item.setForeground(QColor('blue'))
                elif status.startswith('已忽略'):
                    status_item.setForeground(QColor('gray'))
                elif status.startswith('执行失败'):
                    status_item.setForeground(QColor('red'))
                self.signal_table.setItem(i, 5, status_item)
                
                # 执行时间
                execution_time = signal.get('execution_time')
                if execution_time:
                    exec_time_str = datetime.fromtimestamp(execution_time).strftime("%H:%M:%S")
                    self.signal_table.setItem(i, 6, QTableWidgetItem(exec_time_str))
                else:
                    self.signal_table.setItem(i, 6, QTableWidgetItem("-"))
                
                # 延迟
                delay = signal.get('delay')
                if delay is not None:
                    self.signal_table.setItem(i, 7, QTableWidgetItem(f"{delay:.2f}"))
                else:
                    self.signal_table.setItem(i, 7, QTableWidgetItem("-"))
                
                # 操作按钮
                if signal.get('status') not in ['已执行', '已忽略(旧信号)', '已忽略(已执行更新信号)']:
                    execute_button = QPushButton("执行")
                    execute_button.clicked.connect(lambda checked, row=i: self.execute_signal_at_row(row))
                    self.signal_table.setCellWidget(i, 8, execute_button)
            
        except Exception as e:
            logger.error(f"更新信号表格时出错: {e}")
    
    def execute_signal_at_row(self, row):
        """执行指定行的信号"""
        try:
            if row < len(self.signals):
                signal_data = self.signals[row]
                self.execute_signal(signal_data)
                self.update_signal_table()
        except Exception as e:
            logger.error(f"执行行信号时出错: {e}")
    
    def clear_signals(self):
        """清除所有信号"""
        try:
            reply = QMessageBox.question(
                self, 
                "确认清除", 
                "确定要清除所有信号记录吗？\n(已执行的信号记录将保留以避免重复执行)",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                self.signals = []
                self.unique_signals = {}
                # 注意：不清除executed_signals，以避免重复执行
                self.update_signal_table()
                self.status_label.setText("状态: 已清除所有信号")
                self.signal_count_label.setText("信号: 0 个")
        except Exception as e:
            logger.error(f"清除信号时出错: {e}")


if __name__ == "__main__":
    try:
        app = QApplication(sys.argv)
        window = SignalMonitorWindow()
        window.show()
        
        print("✅ 策略信号聚合监控已启动")
        print("\n🔥 主要功能:")
        print("• 接收来自趋势窗口和持仓窗口的策略信号")
        print("• 自动去重和按时间排序")
        print("• 记录每个代币最后执行的信号时间")
        print("• 忽略旧信号，避免重复执行")
        print("• 支持手动和自动执行交易信号")
        
        sys.exit(app.exec_())
        
    except Exception as e:
        logger.error(f"启动失败: {e}")
        print(f"❌ 启动失败: {e}")