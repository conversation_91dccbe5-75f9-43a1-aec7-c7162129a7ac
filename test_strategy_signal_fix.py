#!/usr/bin/env python3
"""
测试策略信号修复
验证 MultiThreadOHLCVManager 的策略信号是否能正常发射
"""

import sys
import logging
import time
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

from api_service import APIService
from multi_thread_ohlcv_manager import MultiThreadOHLCVManager

def test_strategy_signal_fix():
    """测试策略信号修复"""
    
    # 配置日志
    logging.basicConfig(
        level=logging.INFO, 
        format='%(asctime)s - %(levelname)s - %(name)s - %(message)s'
    )
    
    logger = logging.getLogger(__name__)
    logger.info("=== 开始测试策略信号修复 ===")
    
    app = QApplication(sys.argv)
    
    # 记录接收到的信号
    received_signals = []
    
    def on_strategy_signal(token_address, symbol, signal_type, price, timestamp, strategy_name):
        """接收策略信号"""
        signal_info = {
            'token_address': token_address,
            'symbol': symbol,
            'signal_type': signal_type,
            'price': price,
            'timestamp': timestamp,
            'strategy_name': strategy_name
        }
        received_signals.append(signal_info)
        logger.info(f"✅ 接收到策略信号: {symbol} - {signal_type} @ ${price:.6f} (策略: {strategy_name})")
    
    try:
        # 创建API服务和管理器
        api_service = APIService()
        manager = MultiThreadOHLCVManager(api_service, max_workers=2)
        
        # 连接策略信号
        manager.strategy_signal_generated.connect(on_strategy_signal)
        
        # 准备测试代币数据（使用一些知名代币）
        test_tokens = [
            {
                'tokenAddress': 'So11111111111111111111111111111111111111112',  # WSOL
                'symbol': 'SOL',
                'name': 'Wrapped SOL',
                'source': 'trend',
                'strategy_name': 'VWAP 交叉策略'
            },
            {
                'tokenAddress': 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v',  # USDC
                'symbol': 'USDC',
                'name': 'USD Coin',
                'source': 'trend',
                'strategy_name': 'VWAP 交叉策略'
            }
        ]
        
        logger.info(f"提交 {len(test_tokens)} 个测试代币进行分析...")
        
        # 提交测试任务
        submitted_count = manager.submit_batch_download(
            token_list=test_tokens,
            strategy_name='VWAP 交叉策略',
            timeframe='1m',
            days=1
        )
        
        logger.info(f"成功提交 {submitted_count} 个任务")
        
        # 设置定时器检查结果
        def check_results():
            stats = manager.get_statistics()
            logger.info(f"统计: 提交 {stats['total_submitted']}, 完成 {stats['total_completed']}, 失败 {stats['total_failed']}")
            logger.info(f"活跃组件: {stats['active_widgets']}, 接收到信号: {len(received_signals)}")
            
            if received_signals:
                logger.info("🎉 策略信号修复成功！接收到以下信号:")
                for signal in received_signals:
                    logger.info(f"  - {signal['symbol']}: {signal['signal_type']} @ ${signal['price']:.6f}")
            
            # 如果所有任务完成，检查结果
            if stats['total_completed'] + stats['total_failed'] >= stats['total_submitted']:
                if received_signals:
                    logger.info("✅ 测试成功：策略信号正常发射")
                else:
                    logger.warning("⚠️  未接收到策略信号，可能需要进一步调试")
                
                # 3秒后退出
                QTimer.singleShot(3000, app.quit)
        
        # 每5秒检查一次结果
        check_timer = QTimer()
        check_timer.timeout.connect(check_results)
        check_timer.start(5000)
        
        # 30秒后强制退出
        QTimer.singleShot(30000, lambda: [
            logger.info("测试超时，退出程序"),
            app.quit()
        ])
        
        logger.info("🚀 开始测试，请等待...")
        logger.info("预期行为：")
        logger.info("1. 系统会下载代币的OHLCV数据")
        logger.info("2. 创建后台ChartWidget进行策略分析")
        logger.info("3. 发射策略信号（买入/卖出/持有/观察）")
        logger.info("4. 在控制台看到策略信号日志")
        
        # 运行应用
        sys.exit(app.exec_())
        
    except Exception as e:
        logger.error(f"测试失败: {e}", exc_info=True)

if __name__ == '__main__':
    test_strategy_signal_fix() 