#!/usr/bin/env python
"""
API测试脚本 - 用于测试API连接是否正常
"""

import logging
import json
import os
import requests
import time
from api_service import APIService
from config import BIRDEYE_API_KEY, TREND_API_URL, OHLCV_API_URL

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,  # 使用DEBUG级别获取更详细的日志
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('api_test')

def test_api_connection():
    """测试API连接状态"""
    logger.info("开始测试API连接...")
    
    # 检查环境变量
    logger.info(f"BIRDEYE_API_KEY环境变量: {os.environ.get('BIRDEYE_API_KEY', '未设置')}")
    
    # 打印API密钥信息（不显示完整密钥）
    if BIRDEYE_API_KEY:
        masked_key = BIRDEYE_API_KEY[:4] + "..." + BIRDEYE_API_KEY[-4:] if len(BIRDEYE_API_KEY) > 8 else "***"
        logger.info(f"API密钥已设置，长度: {len(BIRDEYE_API_KEY)}, 前缀: {masked_key}")
    else:
        logger.error("API密钥未设置，请检查.env文件或环境变量")
    
    # 测试API连接
    connection_status = APIService.test_api_connection()
    logger.info(f"API连接测试结果: {json.dumps(connection_status, indent=2)}")
    
    return connection_status

def direct_test_trend_api():
    """直接测试趋势榜单API，绕过APIService类"""
    logger.info(f"直接测试趋势榜单API: {TREND_API_URL}")
    
    try:
        # 添加请求头部
        headers = {
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36',
            'Accept': 'application/json'
        }
        
        # 发送请求
        response = requests.get(TREND_API_URL, headers=headers, timeout=15)
        
        # 记录响应状态码
        logger.info(f"响应状态码: {response.status_code}")
        
        # 检查响应状态
        response.raise_for_status()
        
        # 尝试解析JSON
        data = response.json()
        logger.info(f"响应数据类型: {type(data)}")
        
        # 检查数据结构
        if isinstance(data, dict) and data.get('status') == 'success' and 'data' in data:
            logger.info(f"成功获取到 {len(data['data'])} 个趋势代币")
            return True
        elif isinstance(data, list) and len(data) > 0:
            logger.info(f"成功获取到 {len(data)} 个趋势代币")
            return True
        else:
            logger.error(f"API返回格式错误: {data}")
            return False
    except Exception as e:
        logger.error(f"直接测试趋势榜单API失败: {str(e)}")
        return False

def direct_test_ohlcv_api():
    """直接测试OHLCV API，绕过APIService类"""
    logger.info(f"直接测试OHLCV API: {OHLCV_API_URL}")
    
    if not BIRDEYE_API_KEY:
        logger.error("API密钥未设置，无法测试OHLCV API")
        return False
    
    try:
        # 计算时间范围
        end_time = int(time.time())
        start_time = end_time - (1 * 24 * 60 * 60)  # 1天
        
        # 使用一个已知的代币地址进行测试
        test_token = "DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263"
        
        # 设置请求头和参数
        headers = {
            'X-API-KEY': BIRDEYE_API_KEY.encode('ascii', 'ignore').decode('ascii') if BIRDEYE_API_KEY else '',
            'accept': 'application/json',
            'x-chain': 'solana'
        }
        
        # 打印处理后的API密钥
        logger.info(f"API密钥处理后的长度: {len(headers['X-API-KEY'])}")
        if headers['X-API-KEY']:
            masked_key = headers['X-API-KEY'][:4] + "..." + headers['X-API-KEY'][-4:] if len(headers['X-API-KEY']) > 8 else "***"
            logger.info(f"处理后的API密钥: {masked_key}")
        
        params = {
            'address': test_token,
            'type': '1m',
            'time_from': start_time,
            'time_to': end_time,
            'limit': 10
        }
        
        logger.info(f"请求参数: {params}")
        
        # 发送请求
        response = requests.get(
            OHLCV_API_URL,
            headers=headers,
            params=params,
            timeout=15
        )
        
        # 记录响应状态码
        logger.info(f"响应状态码: {response.status_code}")
        
        # 检查响应状态
        response.raise_for_status()
        
        # 尝试解析JSON
        data = response.json()
        logger.info(f"响应数据类型: {type(data)}")
        
        if isinstance(data, list):
            logger.info(f"成功获取到 {len(data)} 条OHLCV数据")
            return True
        else:
            logger.error(f"API返回格式错误: {data}")
            return False
    except Exception as e:
        logger.error(f"直接测试OHLCV API失败: {str(e)}")
        if hasattr(e, 'response') and e.response is not None:
            logger.error(f"响应状态码: {e.response.status_code}")
            logger.error(f"响应内容: {e.response.text[:500]}...")
        return False

def test_trending_tokens():
    """测试获取趋势代币数据"""
    logger.info("开始测试获取趋势代币数据...")
    
    tokens = APIService.get_trending_tokens()
    
    if tokens:
        logger.info(f"成功获取到 {len(tokens)} 个趋势代币")
        # 打印前3个代币的信息
        for i, token in enumerate(tokens[:3]):
            logger.info(f"代币 {i+1}: {token.get('name', 'Unknown')} ({token.get('symbol', 'Unknown')})")
    else:
        logger.error("获取趋势代币数据失败")
    
    return tokens

if __name__ == "__main__":
    logger.info("========== 开始 API 测试 ==========\n")
    
    # 测试API连接
    logger.info("\n========== 测试API连接 ==========")
    connection_status = test_api_connection()
    
    # 直接测试趋势榜单API
    logger.info("\n========== 直接测试趋势榜单API ==========")
    trend_api_success = direct_test_trend_api()
    
    # 直接测试OHLCV API
    logger.info("\n========== 直接测试OHLCV API ==========")
    ohlcv_api_success = direct_test_ohlcv_api()
    
    # 测试通过APIService获取趋势代币数据
    logger.info("\n========== 测试获取趋势代币数据 ==========")
    if trend_api_success:
        test_trending_tokens()
    else:
        logger.error("趋势API连接失败，跳过测试获取趋势代币数据")
    
    # 打印测试结果汇总
    logger.info("\n========== 测试结果汇总 ==========")
    logger.info(f"API连接测试: {connection_status}")
    logger.info(f"直接趋势榜单API测试: {'成功' if trend_api_success else '失败'}")
    logger.info(f"直接OHLCV API测试: {'成功' if ohlcv_api_success else '失败'}")
    
    logger.info("\n========== 测试完成 ==========\n")
