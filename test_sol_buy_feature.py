#!/usr/bin/env python
"""
测试 SOL 买入功能
"""

import sys
import logging
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from PyQt5.QtCore import Qt

from ui.live_trading_widget import LiveTradingWidget

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

class TestSOLBuyWindow(QMainWindow):
    """测试 SOL 买入功能窗口"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
    
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("SOL真实买入功能测试 - LiveTradingWidget")
        self.resize(1400, 900)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        main_layout = QVBoxLayout(central_widget)
        
        # 创建实盘交易组件
        self.live_trading_widget = LiveTradingWidget()
        main_layout.addWidget(self.live_trading_widget)
        
        # 设置窗口样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #2c3e50;
            }
        """)

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 显示测试说明
    print("\n" + "="*60)
    print("💰 SOL 真实买入功能测试")
    print("="*60)
    print("🔥 新增功能：")
    print("   ✅ 自动钱包连接（从 OKX DEX API 自动获取钱包地址）")
    print("   ✅ SOL金额选择下拉框（0.1、0.3、0.5、1.0 SOL）")
    print("   ✅ 买入按钮（连接钱包并选中代币后可用）")
    print("   ✅ OKX DEX API 集成 - 真实交易报价和执行")
    print("   ✅ 交易确认和状态反馈")
    print("\n📋 测试步骤：")
    print("   1. 等待趋势币数据加载完成")
    print("   2. 点击'🔗 连接钱包'按钮（自动获取钱包地址）")
    print("   3. 确认钱包连接成功提示")
    print("   4. 点击趋势币列表中的任意代币")
    print("   5. 选择SOL金额（0.1、0.3、0.5、1.0）")
    print("   6. 点击'💰 买入'按钮开始真实买入")
    print("   7. 确认买入参数和报价信息")
    print("   8. 等待交易执行和确认")
    print("\n🎯 验证重点：")
    print("   • 自动钱包地址获取和验证")
    print("   • 买入按钮状态管理（钱包+代币双重检查）")
    print("   • OKX DEX API 报价获取")
    print("   • 真实交易执行流程")
    print("   • 错误处理和用户反馈")
    print("\n⚠️ 重要提示：")
    print("   • 这是真实交易功能，会使用 OKX DEX API")
    print("   • 钱包地址自动从 OKX 钱包服务获取")
    print("   • 交易需要钱包中有足够的 SOL 余额")
    print("   • 实际签名需要在钱包应用中完成")
    print("   • 建议先在测试网络验证功能")
    print("="*60)
    
    # 创建测试窗口
    window = TestSOLBuyWindow()
    window.show()
    
    # 运行应用程序
    sys.exit(app.exec_())

if __name__ == "__main__":
    main() 