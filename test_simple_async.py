#!/usr/bin/env python
"""
简化版异步工作线程测试脚本
"""

import time
import threading
import requests
import json
from config import TREND_API_URL
from simple_async import SimpleAsyncTask, log_info, log_error

# 创建事件，用于等待异步操作完成
done_event = threading.Event()

def get_trending_tokens():
    """获取趋势榜单数据"""
    log_info("开始获取趋势榜单数据...")
    
    try:
        # 添加请求头部
        headers = {
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36',
            'Accept': 'application/json'
        }
        
        # 发送请求
        log_info(f"请求URL: {TREND_API_URL}")
        response = requests.get(TREND_API_URL, headers=headers, timeout=15)
        
        # 记录响应状态码
        log_info(f"响应状态码: {response.status_code}")
        
        # 检查响应状态
        response.raise_for_status()
        
        # 尝试解析JSON
        data = response.json()
        log_info(f"响应数据类型: {type(data)}")
        
        # 检查数据结构
        if isinstance(data, dict) and data.get('status') == 'success' and 'data' in data:
            log_info(f"成功获取到 {len(data['data'])} 个趋势代币")
            return data['data']
        elif isinstance(data, list) and len(data) > 0:
            # 如果直接返回列表
            log_info(f"成功获取到 {len(data)} 个趋势代币")
            return data
        else:
            # 记录完整的响应数据，便于调试
            log_error(f"API返回格式错误: {data}")
            return []
    except Exception as e:
        log_error(f"获取趋势榜单失败: {str(e)}")
        return []

def test_async():
    """测试异步获取趋势榜单"""
    log_info("开始测试异步获取趋势榜单...")
    
    # 定义成功回调函数
    def on_success(tokens):
        log_info(f"成功回调函数被调用，获取到 {len(tokens) if tokens else 0} 个代币")
        
        if tokens:
            log_info(f"第一个代币: {tokens[0]['name']} ({tokens[0]['symbol']})")
            
            # 显示所有代币
            log_info("\n所有代币列表:")
            for i, token in enumerate(tokens[:5]):  # 只显示前5个
                log_info(f"{i+1}. {token['name']} ({token['symbol']})")
            
            if len(tokens) > 5:
                log_info(f"... 还有 {len(tokens) - 5} 个代币未显示")
        else:
            log_info("未获取到任何代币数据")
        
        # 设置事件，表示异步操作已完成
        done_event.set()
    
    # 定义错误回调函数
    def on_error(error_msg):
        log_error(f"错误回调函数被调用: {error_msg}")
        
        # 设置事件，表示异步操作已完成
        done_event.set()
    
    # 使用简化版异步工作线程模块获取趋势榜单数据
    task_id = SimpleAsyncTask.run(
        func=get_trending_tokens,
        on_result=on_success,
        on_error=on_error
    )
    
    log_info(f"异步任务已启动，任务ID: {task_id}")
    
    # 等待异步操作完成，最多等待30秒
    log_info("等待异步操作完成...")
    if not done_event.wait(30):
        log_error("等待超时，异步操作未完成")

if __name__ == "__main__":
    test_async()
    
    # 等待一段时间，确保所有日志都被打印
    time.sleep(2)
    log_info("测试完成")
