import requests
import time

BIRDEYE_API_KEY = "a0c45b7306f54ab4a5639b49a454bc18"  # 替换成你的真实密钥
OHLCV_API_URL = "https://public-api.birdeye.so/defi/ohlcv"
TOKEN_ADDRESS = "HZju4Hc1dmK3d1b8Vz4DXDiPiHcUu9vZQiqUT8RDpump"  # 示例地址

headers = {
    'X-API-KEY': BIRDEYE_API_KEY,
    'accept': 'application/json',
    'x-chain': 'solana'
}
params = {
    'address': TOKEN_ADDRESS,
    'type': '1m',
    'time_from': int(time.time()) - 3600,  # 1小时前
    'time_to': int(time.time()),
    'limit': 1000
}

response = requests.get(OHLCV_API_URL, headers=headers, params=params)
print(f"Status Code: {response.status_code}")
print(f"Response: {response.text[:500]}")  # 只打印前500字符