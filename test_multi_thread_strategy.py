#!/usr/bin/env python3
"""
测试多线程策略分析系统
验证 TrendingWindow 与 MultiThreadOHLCVManager 的集成
"""

import sys
import logging
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer

from api_service import APIService
from trending_window import TrendingWindow

def test_multi_thread_strategy():
    """测试多线程策略分析功能"""
    
    # 配置日志
    logging.basicConfig(
        level=logging.INFO, 
        format='%(asctime)s - %(levelname)s - %(name)s - %(message)s'
    )
    
    logger = logging.getLogger(__name__)
    logger.info("=== 开始测试多线程策略分析系统 ===")
    
    app = QApplication(sys.argv)
    
    try:
        # 创建API服务
        api_service = APIService()
        
        # 创建趋势窗口
        window = TrendingWindow(api_service=api_service)
        
        # 显示窗口
        window.show()
        
        # 启用策略分析
        window.strategy_enabled_checkbox.setChecked(True)
        logger.info("已启用策略分析选项")
        
        # 开始数据加载
        window.start_data_loading()
        logger.info("已启动数据加载")
        
        # 设置定时器，等待数据加载后自动开始分析
        def auto_start_analysis():
            if window.trending_coins_data:
                logger.info(f"检测到 {len(window.trending_coins_data)} 个趋势代币，开始批量分析...")
                window.start_batch_strategy_analysis()
            else:
                logger.info("等待趋势数据加载...")
                # 继续等待
                QTimer.singleShot(5000, auto_start_analysis)
        
        # 5秒后检查数据并开始分析
        QTimer.singleShot(5000, auto_start_analysis)
        
        # 定时器，定期显示统计信息
        def show_statistics():
            stats = window.get_strategy_analysis_statistics()
            logger.info(f"统计信息: {stats}")
            
        stats_timer = QTimer()
        stats_timer.timeout.connect(show_statistics)
        stats_timer.start(30000)  # 每30秒显示一次统计
        
        logger.info("测试界面已启动，请观察以下内容：")
        logger.info("1. 趋势数据是否正常加载")
        logger.info("2. 策略分析是否自动开始")
        logger.info("3. 策略列是否显示买入/卖出信号")
        logger.info("4. 状态栏是否显示进度信息")
        
        # 运行应用
        sys.exit(app.exec_())
        
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}")
        sys.exit(1)

if __name__ == '__main__':
    test_multi_thread_strategy() 