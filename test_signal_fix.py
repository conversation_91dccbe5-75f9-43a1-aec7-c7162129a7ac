#!/usr/bin/env python3
"""
信号一致性修复验证脚本
测试trending_window和holdings_panel_window是否现在显示一致的信号
"""

import logging
import sys
import time
import pandas as pd
from typing import Dict

# 配置简洁的日志格式
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

def test_signal_interpretation():
    """测试信号解释一致性"""
    print("🔥 测试信号解释一致性修复")
    print("=" * 50)
    
    # 导入模块
    try:
        from multi_thread_ohlcv_manager import interpret_latest_signal_unified
        from ui.chart_widget import ChartWidget
        from api_service import APIService
        print("✅ 成功导入必要模块")
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    
    # 测试不同信号模式
    test_cases = [
        {
            'name': '持有状态',
            'signals': [0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],  # 买入+10个零
            'expected': '持有'
        },
        {
            'name': '买入状态', 
            'signals': [0, 0, 1, 0, 0, 0, 0, 0],  # 买入+5个零
            'expected': '买入'
        },
        {
            'name': '卖出状态',
            'signals': [0, 1, 0, 0, -1, 0, 0],  # 先买入后卖出
            'expected': '卖出'
        },
        {
            'name': '观察状态',
            'signals': [0, 0, 0, 0, 0],  # 全零
            'expected': '观察'
        }
    ]
    
    all_passed = True
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n{i}. 测试 {case['name']}")
        
        # 创建测试DataFrame
        df = pd.DataFrame({
            'signal': case['signals'],
            'close': [100 + j for j in range(len(case['signals']))],  # 模拟价格数据
            'high': [102 + j for j in range(len(case['signals']))],
            'low': [98 + j for j in range(len(case['signals']))],
            'volume': [1000] * len(case['signals'])
        })
        
        # 测试统一解释函数
        result_signal, result_index = interpret_latest_signal_unified(df, f'TEST{i}')
        
        # 检查结果
        if result_signal == case['expected']:
            print(f"   ✅ 信号正确: {result_signal}")
        else:
            print(f"   ❌ 信号错误: 预期 {case['expected']}，实际 {result_signal}")
            all_passed = False
    
    print(f"\n{'='*50}")
    if all_passed:
        print("🎉 所有测试通过！信号解释逻辑已统一")
        print("\n📝 修复说明:")
        print("- 现在trending_window和holdings_panel_window使用相同的信号解释逻辑")
        print("- 'hold'状态需要买入信号后跟随≥10个零信号")
        print("- 'buy'状态是最近的买入信号但后续零信号不足")
        print("- 'sell'状态是最近的卖出信号")
        print("- 'wait'状态是没有任何明确信号")
        
        print("\n🔧 技术细节:")
        print("- 删除了multi_thread_ohlcv_manager.py中简化的信号判断逻辑")
        print("- 添加了interpret_latest_signal_unified()统一函数")
        print("- 两个窗口现在都使用相同的持有状态判断条件")
        
        return True
    else:
        print("❌ 部分测试失败，需要进一步检查")
        return False

def test_color_differences():
    """测试颜色差异说明"""
    print(f"\n{'='*50}")
    print("🎨 信号颜色差异说明")
    print("=" * 50)
    
    print("trending_window.py (浅色主题):")
    print("  买入: #27ae60 (绿色)")
    print("  卖出: #e74c3c (红色)")  
    print("  持有: #f39c12 (橙色)")
    print("  观察: #95a5a6 (灰色)")
    
    print("\nholdings_panel_window.py (深色主题):")
    print("  买入: #4ade80 (亮绿色)")
    print("  卖出: #f87171 (亮红色)")
    print("  持有: #fbbf24 (亮黄色)")
    print("  观察: #9ca3af (亮灰色)")
    
    print("\n💡 颜色不同是正常的，因为:")
    print("- trending_window 使用标准颜色适配浅色主题")
    print("- holdings_panel_window 使用高对比度颜色适配深色主题")
    print("- 这是UI设计决策，不影响信号逻辑一致性")

if __name__ == "__main__":
    print("🔥 信号一致性修复验证")
    print("=" * 70)
    
    success = test_signal_interpretation()
    test_color_differences()
    
    if success:
        print(f"\n{'='*70}")
        print("🎉 修复验证成功！")
        print("现在trending_window和holdings_panel_window应该显示一致的信号类型")
        print("您可以重新启动应用程序并观察两个窗口的信号是否现在一致了")
        sys.exit(0)
    else:
        print(f"\n{'='*70}")
        print("❌ 修复验证失败")
        sys.exit(1) 