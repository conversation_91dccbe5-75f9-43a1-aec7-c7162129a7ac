#!/usr/bin/env python3
"""
策略信号客户端 - 用于将策略信号发送到主监控窗口
"""

import json
import logging
import time
from typing import Dict, Any, Optional
from PyQt5.QtCore import QObject, QLocalSocket, QTimer, pyqtSignal

logger = logging.getLogger("SignalClient")

class SignalClient(QObject):
    """策略信号客户端 - 用于将策略信号发送到主监控窗口"""
    
    connected = pyqtSignal()
    disconnected = pyqtSignal()
    error = pyqtSignal(str)
    signal_sent = pyqtSignal(dict)
    
    def __init__(self, server_name="StrategySignalServer", client_name="", parent=None):
        super().__init__(parent)
        self.server_name = server_name
        self.client_name = client_name or f"Client_{int(time.time())}"
        self.socket = QLocalSocket(self)
        
        # 连接信号
        self.socket.connected.connect(self.on_connected)
        self.socket.disconnected.connect(self.on_disconnected)
        self.socket.errorOccurred.connect(self.on_error)
        
        # 自动重连
        self.reconnect_timer = QTimer(self)
        self.reconnect_timer.timeout.connect(self.connect_to_server)
        self.reconnect_timer.setInterval(5000)  # 5秒重连一次
        
        # 初始连接
        self.connect_to_server()
    
    def connect_to_server(self):
        """连接到信号服务器"""
        if self.socket.state() == QLocalSocket.UnconnectedState:
            logger.info(f"尝试连接到服务器: {self.server_name}")
            self.socket.connectToServer(self.server_name)
    
    def on_connected(self):
        """连接成功处理"""
        logger.info(f"已连接到服务器: {self.server_name}")
        self.reconnect_timer.stop()
        self.connected.emit()
        
        # 发送客户端标识
        self.send_message({
            'type': 'client_info',
            'client_name': self.client_name,
            'connection_time': time.time()
        })
    
    def on_disconnected(self):
        """断开连接处理"""
        logger.info("与服务器断开连接")
        self.disconnected.emit()
        
        # 启动重连
        if not self.reconnect_timer.isActive():
            self.reconnect_timer.start()
    
    def on_error