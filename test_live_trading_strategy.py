#!/usr/bin/env python
"""
实盘交易组件策略执行功能测试脚本
"""

import sys
import logging
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from PyQt5.QtCore import Qt

from api_service import APIService
from ui.live_trading_widget import LiveTradingWidget

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

class TestMainWindow(QMainWindow):
    """测试主窗口"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
    
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("实盘交易组件 - 策略执行功能测试")
        self.resize(1400, 900)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        main_layout = QVBoxLayout(central_widget)
        
        # 创建实盘交易组件
        self.live_trading_widget = LiveTradingWidget()
        main_layout.addWidget(self.live_trading_widget)

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序样式
    app.setStyleSheet("""
        QMainWindow {
            background-color: #2c3e50;
        }
        QWidget {
            font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
        }
    """)
    
    # 创建测试窗口
    window = TestMainWindow()
    window.show()
    
    # 显示欢迎信息
    print("\n" + "="*60)
    print("🚀 实盘交易组件 - 策略执行功能测试")
    print("="*60)
    print("📋 测试功能：")
    print("   1. 趋势币数据获取和显示")
    print("   2. 策略选择和分析")
    print("   3. 代币选择和图表显示")
    print("   4. 策略执行和结果可视化")
    print("\n💡 使用步骤：")
    print("   1. 等待趋势数据加载完成")
    print("   2. 选择一个策略（下拉菜单）")
    print("   3. 点击趋势币列表中的代币")
    print("   4. 设置初始资金")
    print("   5. 点击'🚀 执行策略'按钮")
    print("   6. 观察执行过程和结果")
    print("="*60)
    
    # 运行应用程序
    sys.exit(app.exec_())

if __name__ == "__main__":
    main() 