#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试风险代币过滤功能
验证 isRiskToken 字段的正确处理
"""

def test_risk_token_filtering():
    """测试风险代币过滤逻辑"""
    print("=== 风险代币过滤功能测试 ===\n")
    
    # 模拟API返回的代币数据
    mock_tokens = [
        {
            "symbol": "SOL",
            "balance": "0.5",
            "tokenPrice": "176.71",
            "isRiskToken": False,
            "tokenContractAddress": ""
        },
        {
            "symbol": "USDC", 
            "balance": "100.0",
            "tokenPrice": "1.00",
            "isRiskToken": False,
            "tokenContractAddress": "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v"
        },
        {
            "symbol": "SCAM1",
            "balance": "1000000.0",
            "tokenPrice": "0.000001", 
            "isRiskToken": True,
            "tokenContractAddress": "ScamToken1ContractAddress"
        },
        {
            "symbol": "AIRDROP",
            "balance": "500000.0",
            "tokenPrice": "0.000002",
            "isRiskToken": True,
            "tokenContractAddress": "AirdropTokenContractAddress"
        },
        {
            "symbol": "LEGIT",
            "balance": "10.0",
            "tokenPrice": "5.50",
            "isRiskToken": False,
            "tokenContractAddress": "LegitTokenContractAddress"
        }
    ]
    
    print("📊 原始代币数据:")
    total_value = 0
    for i, token in enumerate(mock_tokens, 1):
        balance = float(token["balance"])
        price = float(token["tokenPrice"])
        value = balance * price
        total_value += value
        risk_status = "⚠️风险" if token["isRiskToken"] else "✅正常"
        print(f"{i}. {token['symbol']}: ${value:.6f} ({risk_status})")
    
    print(f"\n总价值: ${total_value:.6f}")
    
    # 测试过滤逻辑
    def filter_tokens(tokens, hide_risk_tokens=True, min_value=0.01):
        """模拟Portfolio组件的过滤逻辑"""
        valuable_tokens = []
        risk_tokens_count = 0
        
        for token in tokens:
            # 检查是否为风险代币
            is_risk_token = token.get('isRiskToken', False)
            if is_risk_token:
                risk_tokens_count += 1
            
            # 如果开启了风险代币过滤且该代币是风险代币，跳过
            if hide_risk_tokens and is_risk_token:
                continue
            
            # 计算代币价值
            balance = float(token.get('balance', 0))
            price = float(token.get('tokenPrice', 0))
            value = balance * price
            
            # 只显示价值超过阈值的代币，或者是主要代币
            symbol = token.get('symbol', '').upper()
            is_major_token = symbol in ['SOL', 'USDC', 'USDT', 'BTC', 'ETH']
            
            if value > min_value or is_major_token:
                token['calculated_value'] = value
                valuable_tokens.append(token)
        
        return valuable_tokens, risk_tokens_count
    
    print("\n🔍 过滤测试 - 隐藏风险代币:")
    filtered_tokens, risk_count = filter_tokens(mock_tokens, hide_risk_tokens=True)
    print(f"过滤后代币数量: {len(filtered_tokens)}/{len(mock_tokens)}")
    print(f"隐藏的风险代币数量: {risk_count}")
    print("显示的代币:")
    for token in filtered_tokens:
        print(f"  - {token['symbol']}: ${token['calculated_value']:.6f}")
    
    print("\n👁️ 过滤测试 - 显示风险代币:")
    all_tokens, risk_count = filter_tokens(mock_tokens, hide_risk_tokens=False)
    print(f"显示代币数量: {len(all_tokens)}/{len(mock_tokens)}")
    print(f"风险代币数量: {risk_count}")
    print("显示的代币:")
    for token in all_tokens:
        risk_marker = "⚠️" if token.get('isRiskToken', False) else ""
        print(f"  - {risk_marker}{token['symbol']}: ${token['calculated_value']:.6f}")
    
    print("\n=== UI显示测试 ===\n")
    
    # 测试UI显示逻辑
    def format_token_name(token):
        """模拟UI中代币名称格式化"""
        token_name = token.get('symbol', 'Unknown')
        if token.get('isRiskToken', False):
            return f"⚠️ {token_name}"
        return token_name
    
    def get_button_style(token):
        """模拟UI中按钮样式选择"""
        if token.get('isRiskToken', False):
            return "风险代币样式 (红色边框)"
        return "普通代币样式 (红色背景)"
    
    print("🎨 UI显示效果:")
    for token in all_tokens:
        display_name = format_token_name(token)
        button_style = get_button_style(token)
        print(f"  显示名称: {display_name}")
        print(f"  按钮样式: {button_style}")
        print()
    
    print("=== 状态栏信息测试 ===\n")
    
    # 测试状态栏信息生成
    def generate_status_message(total_tokens, displayed_tokens, risk_tokens_count, hide_risk_tokens):
        """模拟状态栏信息生成"""
        status_parts = []
        status_parts.append(f"显示 {displayed_tokens}/{total_tokens} 个代币")
        
        filters_applied = []
        if displayed_tokens < total_tokens:
            filters_applied.append("已过滤价值<$0.01的代币")
        
        if hide_risk_tokens and risk_tokens_count > 0:
            filters_applied.append(f"已隐藏{risk_tokens_count}个风险代币")
        elif not hide_risk_tokens and risk_tokens_count > 0:
            displayed_risk_count = sum(1 for token in all_tokens if token.get('isRiskToken', False))
            if displayed_risk_count > 0:
                filters_applied.append(f"含{displayed_risk_count}个风险代币⚠️")
        
        if filters_applied:
            status_parts.append(f"（{' | '.join(filters_applied)}）")
        
        return ''.join(status_parts)
    
    # 隐藏风险代币的状态
    status_hide = generate_status_message(
        len(mock_tokens), 
        len(filtered_tokens), 
        risk_count, 
        True
    )
    print(f"隐藏风险代币状态: {status_hide}")
    
    # 显示风险代币的状态
    status_show = generate_status_message(
        len(mock_tokens), 
        len(all_tokens), 
        risk_count, 
        False
    )
    print(f"显示风险代币状态: {status_show}")

if __name__ == "__main__":
    test_risk_token_filtering() 