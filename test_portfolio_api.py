"""
测试Portfolio API响应脚本 - 用于诊断代币余额显示问题
"""

import sys
import os
import logging
import json

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.append(current_dir)

# 配置详细日志
logging.basicConfig(
    level=logging.DEBUG, 
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_api_responses():
    """测试API响应结构"""
    try:
        from okx_dex_client import OKXDexClient, AllTokenBalancesRequest, TotalValueRequest
        from config import PORTFOLIO_CONFIG
        
        client = OKXDexClient(PORTFOLIO_CONFIG["okx_dex_api_url"])
        
        # 使用用户提供的真实钱包地址进行测试
        test_wallet = "HfHb1cSKNm8BS2YYaLuYmURqQjnSoPwwdZgdWjs7crph"
        
        logger.info(f"测试钱包地址: {test_wallet}")
        
        # 1. 测试健康检查
        logger.info("=== 1. 健康检查 ===")
        health_result = client.health_check()
        logger.info(f"健康检查结果: {json.dumps(health_result, indent=2, ensure_ascii=False)}")
        
        if not health_result.get('success'):
            logger.error("API不可用，无法继续测试")
            return False
        
        # 2. 测试总估值
        logger.info("\n=== 2. 测试总估值 ===")
        total_value_request = TotalValueRequest(
            address=test_wallet,
            chains="501"
        )
        total_value_result = client.get_total_value(total_value_request)
        logger.info(f"总估值结果: {json.dumps(total_value_result, indent=2, ensure_ascii=False)}")
        
        # 3. 测试代币余额
        logger.info("\n=== 3. 测试代币余额 ===")
        token_balances_request = AllTokenBalancesRequest(
            address=test_wallet,
            chains="501",
            exclude_risk_token="0"
        )
        token_balances_result = client.get_all_token_balances(token_balances_request)
        logger.info(f"代币余额结果: {json.dumps(token_balances_result, indent=2, ensure_ascii=False)}")
        
        # 4. 分析数据结构
        logger.info("\n=== 4. 数据结构分析 ===")
        if token_balances_result.get('success') and token_balances_result.get('data'):
            data = token_balances_result['data']
            logger.info(f"data类型: {type(data)}")
            logger.info(f"data内容: {data}")
            
            if isinstance(data, dict):
                logger.info(f"data是字典，可用键: {list(data.keys())}")
                for key, value in data.items():
                    logger.info(f"  {key}: {type(value)} - {value if not isinstance(value, list) else f'列表，长度{len(value)}'}")
                    if isinstance(value, list) and len(value) > 0:
                        logger.info(f"    第一个元素: {value[0]}")
            elif isinstance(data, list):
                logger.info(f"data是列表，长度: {len(data)}")
                if len(data) > 0:
                    logger.info(f"第一个元素: {data[0]}")
        else:
            logger.warning("没有获取到有效的代币余额数据")
        
        # 5. 测试Solana便捷方法
        logger.info("\n=== 5. 测试Solana便捷方法 ===")
        solana_result = client.get_solana_balance(test_wallet, exclude_risk_token=True)
        logger.info(f"Solana余额结果: {json.dumps(solana_result, indent=2, ensure_ascii=False)}")
        
        return True
        
    except Exception as e:
        logger.error(f"测试API响应时出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    logger.info("=" * 60)
    logger.info("Portfolio API 响应测试")
    logger.info("=" * 60)
    
    success = test_api_responses()
    
    if success:
        logger.info("\n✅ API测试完成")
    else:
        logger.error("\n❌ API测试失败")
        return 1
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code) 