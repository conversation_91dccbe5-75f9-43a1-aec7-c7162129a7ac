#!/usr/bin/env python
"""
简化版线程测试脚本 - 使用标准Python线程库
"""

import threading
import time
import logging
import requests
import json
from config import TREND_API_URL

# 使用直接打印而不是日志
def log_info(message):
    print(f"INFO: {message}")
    
def log_error(message):
    print(f"ERROR: {message}")

# 创建事件，用于等待线程完成
done_event = threading.Event()
result_data = None

def get_trending_tokens():
    """获取趋势榜单数据"""
    global result_data
    
    log_info("开始获取趋势榜单数据...")
    
    try:
        # 添加请求头部
        headers = {
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36',
            'Accept': 'application/json'
        }
        
        # 发送请求
        log_info(f"请求URL: {TREND_API_URL}")
        response = requests.get(TREND_API_URL, headers=headers, timeout=15)
        
        # 记录响应状态码
        log_info(f"响应状态码: {response.status_code}")
        
        # 检查响应状态
        response.raise_for_status()
        
        # 尝试解析JSON
        data = response.json()
        log_info(f"响应数据类型: {type(data)}")
        
        # 检查数据结构
        if isinstance(data, dict) and data.get('status') == 'success' and 'data' in data:
            log_info(f"成功获取到 {len(data['data'])} 个趋势代币")
            result_data = data['data']
            return data['data']
        elif isinstance(data, list) and len(data) > 0:
            # 如果直接返回列表
            log_info(f"成功获取到 {len(data)} 个趋势代币")
            result_data = data
            return data
        else:
            # 记录完整的响应数据，便于调试
            log_error(f"API返回格式错误: {data}")
            return []
    except Exception as e:
        log_error(f"获取趋势榜单失败: {str(e)}")
        return []
    finally:
        # 设置事件，表示线程已完成
        done_event.set()

def test_thread():
    """测试线程"""
    log_info("开始测试线程...")
    
    # 创建线程
    thread = threading.Thread(target=get_trending_tokens)
    
    # 启动线程
    log_info("启动线程...")
    thread.start()
    
    # 等待线程完成，最多等待10秒
    log_info("等待线程完成...")
    if done_event.wait(10):
        log_info("线程已完成")
        if result_data:
            log_info(f"获取到 {len(result_data)} 个趋势代币")
            log_info(f"第一个代币: {result_data[0]['name']} ({result_data[0]['symbol']})")
        else:
            log_error("未获取到数据")
    else:
        log_error("等待超时，线程未完成")

if __name__ == "__main__":
    test_thread()
