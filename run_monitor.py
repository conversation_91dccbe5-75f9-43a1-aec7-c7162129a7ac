#!/usr/bin/env python3
"""
策略信号监控启动脚本
"""

import sys
import logging
import subprocess
import time
import os
from PyQt5.QtWidgets import QApplication
from main_window import SignalMonitorWindow

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("MonitorLauncher")

def main():
    """主函数"""
    try:
        # 启动监控窗口
        app = QApplication(sys.argv)
        window = SignalMonitorWindow()
        window.show()
        
        print("\n" + "="*50)
        print("✅ 策略信号聚合监控已启动")
        print("="*50)
        print("\n🔥 主要功能:")
        print("• 接收来自趋势窗口和持仓窗口的策略信号")
        print("• 自动去重和按时间排序")
        print("• 记录每个代币最后执行的信号时间")
        print("• 忽略旧信号，避免重复执行")
        print("• 支持手动和自动执行交易信号")
        print("="*50 + "\n")
        
        sys.exit(app.exec_())
        
    except Exception as e:
        logger.error(f"启动失败: {e}")
        print(f"❌ 启动失败: {e}")

if __name__ == "__main__":
    main()