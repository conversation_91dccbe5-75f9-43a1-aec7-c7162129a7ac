#!/usr/bin/env python3
"""
改进的多线程策略分析系统测试
包含更好的错误处理和日志配置
"""

import sys
import logging
from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import QTimer

from api_service import APIService
from trending_window import TrendingWindow

def setup_logging():
    """设置日志配置"""
    # 配置根日志器
    logging.basicConfig(
        level=logging.INFO, 
        format='%(asctime)s - %(levelname)s - %(name)s - %(message)s'
    )
    
    # 调整特定模块的日志级别，减少噪音
    logging.getLogger("multi_thread_ohlcv_manager").setLevel(logging.INFO)
    logging.getLogger("api_service").setLevel(logging.WARNING)  # 减少API日志
    logging.getLogger("__main__").setLevel(logging.INFO)
    
    # 设置图像相关的日志为WARNING级别
    logging.getLogger("trending_window").setLevel(logging.INFO)

def test_multi_thread_strategy_improved():
    """改进的多线程策略分析功能测试"""
    
    setup_logging()
    logger = logging.getLogger(__name__)
    logger.info("=== 开始测试多线程策略分析系统 (改进版) ===")
    
    app = QApplication(sys.argv)
    
    try:
        # 创建API服务
        api_service = APIService()
        
        # 创建趋势窗口
        window = TrendingWindow(api_service=api_service)
        
        # 显示窗口
        window.show()
        logger.info("趋势窗口已显示")
        
        # 设置窗口标题，指示这是测试模式
        window.setWindowTitle("趋势热门榜单 - 测试模式")
        
        # 启用策略分析
        window.strategy_enabled_checkbox.setChecked(True)
        logger.info("已启用策略分析选项")
        
        # 更新状态显示
        window.status_label.setText("状态: 测试模式已启动，等待数据...")
        
        # 开始数据加载
        window.start_data_loading()
        logger.info("已启动数据加载")
        
        # 设置延迟，等待数据加载后开始分析
        def delayed_analysis():
            try:
                if window.trending_coins_data:
                    count = len(window.trending_coins_data)
                    logger.info(f"检测到 {count} 个趋势代币，开始批量分析...")
                    
                    # 限制分析数量，在测试环境中使用较少的代币
                    test_limit = min(count, 10)  # 最多分析10个代币
                    
                    if test_limit < count:
                        logger.info(f"测试模式：限制分析数量为 {test_limit} 个代币")
                        # 临时修改数据列表
                        original_data = window.trending_coins_data.copy()
                        window.trending_coins_data = window.trending_coins_data[:test_limit]
                    
                    window.start_batch_strategy_analysis()
                    
                    # 恢复原始数据（如果有修改）
                    if test_limit < count:
                        window.trending_coins_data = original_data
                        
                else:
                    logger.info("等待趋势数据加载...")
                    # 继续等待，但有最大等待次数
                    if not hasattr(delayed_analysis, 'wait_count'):
                        delayed_analysis.wait_count = 0
                    
                    delayed_analysis.wait_count += 1
                    if delayed_analysis.wait_count < 6:  # 最多等待30秒 (6 * 5秒)
                        QTimer.singleShot(5000, delayed_analysis)
                    else:
                        logger.error("等待数据超时，请检查网络连接和API服务")
                        window.status_label.setText("状态: 数据加载超时 ❌")
                        
            except Exception as e:
                logger.error(f"自动分析过程出错: {e}")
                window.status_label.setText("状态: 自动分析失败 ❌")
        
        # 5秒后开始检查和分析
        QTimer.singleShot(5000, delayed_analysis)
        
        # 定时器，定期显示统计信息
        def show_statistics():
            try:
                stats = window.get_strategy_analysis_statistics()
                if stats['total_submitted'] > 0:
                    logger.info(f"📊 统计: 已提交 {stats['total_submitted']}, "
                              f"已完成 {stats['total_completed']}, "
                              f"失败 {stats['total_failed']}, "
                              f"活跃组件 {stats['active_widgets']}")
            except Exception as e:
                logger.debug(f"获取统计信息失败: {e}")
            
        stats_timer = QTimer()
        stats_timer.timeout.connect(show_statistics)
        stats_timer.start(20000)  # 每20秒显示一次统计
        
        # 设置关闭事件处理
        def on_application_closing():
            logger.info("应用程序正在关闭，清理资源...")
            try:
                if hasattr(window, 'multi_thread_manager'):
                    window.multi_thread_manager.shutdown()
                stats_timer.stop()
            except Exception as e:
                logger.error(f"清理资源时出错: {e}")
        
        app.aboutToQuit.connect(on_application_closing)
        
        logger.info("🚀 测试环境已完全启动，请观察以下内容：")
        logger.info("1. 趋势数据是否正常加载到表格")
        logger.info("2. 策略分析是否自动开始（5秒后）")
        logger.info("3. 策略列是否显示买入/卖出信号")
        logger.info("4. 状态栏是否显示正确的进度信息")
        logger.info("5. 观察控制台中的统计信息更新")
        
        # 添加键盘快捷键帮助
        def show_help():
            QMessageBox.information(window, "测试帮助", 
                "测试功能：\n"
                "• 自动数据加载：程序启动后自动获取趋势数据\n"
                "• 自动分析：检测到数据后5秒开始分析\n"
                "• 实时进度：观察状态栏和进度条\n"
                "• 统计信息：每20秒在控制台显示统计\n"
                "• 测试限制：最多分析10个代币以节省时间\n\n"
                "注意：如果分析成功率较低，可能是API限制或网络问题")
        
        # 3秒后显示帮助信息
        QTimer.singleShot(3000, show_help)
        
        # 运行应用
        return app.exec_()
        
    except Exception as e:
        logger.error(f"测试过程中发生严重错误: {e}")
        if 'app' in locals():
            QMessageBox.critical(None, "错误", f"测试启动失败：{e}")
        return 1

if __name__ == '__main__':
    sys.exit(test_multi_thread_strategy_improved()) 