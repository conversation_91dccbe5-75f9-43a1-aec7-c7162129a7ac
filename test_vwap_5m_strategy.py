#!/usr/bin/env python3
"""
测试5分钟VWAP策略
"""

import pandas as pd
import numpy as np
import logging
from strategies import VWAP5mStrategy, StrategyFactory
from indicators import TechnicalIndicators

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_test_data(num_candles=100):
    """创建测试用的5分钟K线数据"""
    np.random.seed(42)  # 设置随机种子以便结果可重现
    
    # 生成时间序列
    dates = pd.date_range(start='2024-01-01 09:00:00', periods=num_candles, freq='5T')
    
    # 生成价格数据（模拟走势）
    base_price = 100.0
    price_changes = np.random.normal(0, 0.5, num_candles)  # 价格变化
    prices = [base_price]
    
    for change in price_changes[1:]:
        new_price = max(prices[-1] * (1 + change/100), 1.0)  # 确保价格为正
        prices.append(new_price)
    
    # 生成OHLCV数据
    data = []
    for i, price in enumerate(prices):
        # 模拟高低开收
        volatility = abs(np.random.normal(0, 1))
        high = price * (1 + volatility/100)
        low = price * (1 - volatility/100)
        
        if i == 0:
            open_price = price
        else:
            open_price = prices[i-1]  # 前一根K线的收盘价
            
        close_price = price
        volume = max(np.random.normal(10000, 2000), 1000)  # 确保成交量为正
        
        data.append({
            'datetime': dates[i],
            'timestamp': int(dates[i].timestamp()),
            'open': open_price,
            'high': high,
            'low': low,
            'close': close_price,
            'volume': volume
        })
    
    df = pd.DataFrame(data)
    return df

def test_vwap_5m_strategy():
    """测试5分钟VWAP策略"""
    logger.info("开始测试5分钟VWAP策略...")
    
    # 1. 创建测试数据
    df = create_test_data(200)
    logger.info(f"创建了 {len(df)} 根测试K线数据")
    
    # 2. 添加VWAP指标
    df = TechnicalIndicators.add_vwap(df)
    logger.info("添加VWAP指标完成")
    
    # 3. 创建策略实例
    strategy = VWAP5mStrategy()
    logger.info(f"创建策略实例: {strategy.name}")
    logger.info(f"策略描述: {strategy.description}")
    logger.info(f"策略时间框架: {strategy.get_primary_timeframe()}")
    
    # 4. 生成交易信号
    df_with_signals = strategy.generate_signals(df.copy())
    
    # 5. 分析信号
    buy_signals = (df_with_signals['signal'] == 1).sum()
    sell_signals = (df_with_signals['signal'] == -1).sum()
    
    logger.info(f"生成的买入信号数量: {buy_signals}")
    logger.info(f"生成的卖出信号数量: {sell_signals}")
    
    # 6. 执行回测
    logger.info("开始执行回测...")
    backtest_results = strategy.backtest(df_with_signals, initial_capital=10000.0)
    
    # 7. 输出回测结果
    logger.info("\n" + "="*50)
    logger.info("回测结果:")
    logger.info("="*50)
    logger.info(f"策略名称: {backtest_results['strategy_name']}")
    logger.info(f"初始资金: ${backtest_results['initial_capital']:.2f}")
    logger.info(f"最终资金: ${backtest_results['final_capital']:.2f}")
    logger.info(f"总收益: ${backtest_results['total_profit']:.2f}")
    logger.info(f"收益率: {backtest_results['profit_percentage']:.2f}%")
    logger.info(f"交易次数: {backtest_results['trade_count']}")
    logger.info(f"胜率: {backtest_results['win_rate']*100:.2f}%")
    logger.info(f"平均盈利: ${backtest_results['avg_profit']:.2f}")
    logger.info(f"平均亏损: ${backtest_results['avg_loss']:.2f}")
    logger.info(f"盈亏比: {backtest_results['profit_loss_ratio']:.2f}")
    logger.info(f"最大回撤: ${backtest_results['max_drawdown']:.2f}")
    logger.info(f"最大回撤百分比: {backtest_results['max_drawdown_percentage']:.2f}%")
    logger.info(f"夏普比率: {backtest_results['sharpe_ratio']:.4f}")
    
    # 8. 显示一些交易细节
    trades = backtest_results['trades']
    if trades:
        logger.info(f"\n前5笔交易详情:")
        for i, trade in enumerate(trades[:5]):
            logger.info(f"交易 {i+1}: {trade['type']} - 时间: {trade['datetime']}, 价格: ${trade['price']:.4f}")
    
    return backtest_results

def test_strategy_factory():
    """测试策略工厂是否正确包含新策略"""
    logger.info("\n测试策略工厂...")
    
    all_strategies = StrategyFactory.get_all_strategies()
    strategy_names = [s.name for s in all_strategies]
    
    logger.info(f"工厂中的所有策略: {strategy_names}")
    
    # 检查5分钟VWAP策略是否在列表中
    vwap_5m_strategy = StrategyFactory.get_strategy_by_name("5分钟VWAP策略")
    if vwap_5m_strategy:
        logger.info(f"成功从工厂获取策略: {vwap_5m_strategy.name}")
        return True
    else:
        logger.error("无法从工厂获取5分钟VWAP策略")
        return False

if __name__ == "__main__":
    try:
        # 测试策略工厂
        factory_test_passed = test_strategy_factory()
        
        if factory_test_passed:
            # 测试策略本身
            results = test_vwap_5m_strategy()
            logger.info("\n测试完成！5分钟VWAP策略运行正常。")
        else:
            logger.error("策略工厂测试失败，请检查策略注册。")
            
    except Exception as e:
        logger.error(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc() 