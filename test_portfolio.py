"""
Portfolio 功能测试脚本
"""

import sys
import os
import logging
from PyQt5.QtWidgets import QApplication

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.append(current_dir)

# 配置日志
logging.basicConfig(
    level=logging.INFO, 
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_okx_client():
    """测试OKX DEX客户端连接"""
    try:
        from okx_dex_client import OKXDexClient
        from config import PORTFOLIO_CONFIG
        
        client = OKXDexClient(PORTFOLIO_CONFIG["okx_dex_api_url"])
        
        logger.info("测试OKX DEX API连接...")
        health_result = client.health_check()
        
        if health_result.get('success'):
            logger.info("✅ OKX DEX API连接成功")
            logger.info(f"API版本: {health_result.get('version', 'N/A')}")
            logger.info(f"服务状态: {health_result.get('status', 'N/A')}")
            
            # 测试获取支持的链
            chains_result = client.get_supported_chains()
            if chains_result.get('success'):
                logger.info("✅ 成功获取支持的区块链列表")
                chains = chains_result.get('data', [])
                logger.info(f"支持的链数量: {len(chains)}")
                for chain in chains[:3]:  # 显示前3个
                    logger.info(f"  - {chain.get('chainName')} (ID: {chain.get('chainId')})")
            else:
                logger.warning(f"❌ 获取支持的链失败: {chains_result.get('error')}")
                
        else:
            logger.error(f"❌ OKX DEX API连接失败: {health_result.get('error')}")
            logger.info("请确保:")
            logger.info("1. OKX DEX本地API服务正在运行 (https://okx-local-api.vercel.app)")
            logger.info("2. 网络连接正常")
            
        return health_result.get('success', False)
        
    except Exception as e:
        logger.error(f"测试OKX客户端时出错: {e}")
        return False

def test_portfolio_widget():
    """测试Portfolio组件"""
    try:
        from ui.portfolio_widget import PortfolioWidget
        
        app = QApplication(sys.argv)
        
        logger.info("创建Portfolio组件...")
        portfolio_widget = PortfolioWidget()
        
        # 设置窗口标题和大小
        portfolio_widget.setWindowTitle("Portfolio 测试")
        portfolio_widget.resize(1000, 700)
        
        # 显示窗口
        portfolio_widget.show()
        
        logger.info("✅ Portfolio组件创建成功")
        logger.info("Portfolio测试窗口已显示")
        logger.info("测试说明:")
        logger.info("1. 输入一个有效的Solana钱包地址")
        logger.info("2. 点击'刷新余额'按钮")
        logger.info("3. 观察API状态和余额信息")
        logger.info("4. 可以尝试开启自动刷新功能")
        
        # 运行应用
        return app.exec_()
        
    except Exception as e:
        logger.error(f"测试Portfolio组件时出错: {e}")
        import traceback
        traceback.print_exc()
        return 1

def test_config():
    """测试配置文件"""
    try:
        from config import PORTFOLIO_CONFIG
        
        logger.info("检查Portfolio配置...")
        logger.info(f"OKX DEX API地址: {PORTFOLIO_CONFIG['okx_dex_api_url']}")
        logger.info(f"默认钱包地址: {PORTFOLIO_CONFIG['default_wallet_address'] or '未设置'}")
        logger.info(f"支持的链: {list(PORTFOLIO_CONFIG['supported_chains'].keys())}")
        logger.info(f"刷新间隔: {PORTFOLIO_CONFIG['refresh_interval']}ms")
        
        # 检查常用代币配置
        common_tokens = PORTFOLIO_CONFIG.get('common_tokens', {})
        for chain_id, tokens in common_tokens.items():
            chain_name = PORTFOLIO_CONFIG['supported_chains'].get(chain_id, f"Chain {chain_id}")
            logger.info(f"{chain_name} 常用代币: {list(tokens.keys())}")
        
        logger.info("✅ Portfolio配置检查完成")
        return True
        
    except Exception as e:
        logger.error(f"检查Portfolio配置时出错: {e}")
        return False

def main():
    """主测试函数"""
    logger.info("=" * 60)
    logger.info("Portfolio 功能测试")
    logger.info("=" * 60)
    
    # 1. 测试配置
    logger.info("\n1. 测试配置文件...")
    config_ok = test_config()
    
    # 2. 测试OKX客户端
    logger.info("\n2. 测试OKX DEX API连接...")
    api_ok = test_okx_client()
    
    # 3. 测试Portfolio组件（如果前面的测试通过）
    if config_ok:
        logger.info("\n3. 测试Portfolio组件...")
        if api_ok:
            logger.info("API连接正常，启动完整功能测试...")
        else:
            logger.warning("API连接失败，但仍可测试UI组件...")
        
        exit_code = test_portfolio_widget()
        return exit_code
    else:
        logger.error("配置测试失败，无法继续")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code) 